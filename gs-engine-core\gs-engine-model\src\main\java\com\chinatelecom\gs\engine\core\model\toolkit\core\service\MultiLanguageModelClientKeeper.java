package com.chinatelecom.gs.engine.core.model.toolkit.core.service;


import com.chinatelecom.gs.engine.core.model.toolkit.BaseMultiLLMClient;
import com.chinatelecom.gs.engine.core.model.toolkit.adapter.multi.MultiLLMClient;
import com.chinatelecom.gs.engine.core.model.toolkit.adapter.qwenvlaio.QwenVLClient;
import com.chinatelecom.gs.engine.core.model.toolkit.adapter.qwenvlonline.QwenVLOnlineClient;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.ExternalModelInfo;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Model;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2023/11/22 17:42
 */
public class MultiLanguageModelClientKeeper {

    private static final Map<String, BaseMultiLLMClient> MODEL_CLIENT_CACHE = new ConcurrentHashMap<>();

    public static String buildName(String modelName) {
        return modelName;
    }

    public static BaseMultiLLMClient getClient(ExternalModelInfo modelInfo) {
        if (StringUtils.isBlank(modelInfo.getModelName())) {
            throw new IllegalArgumentException("初始化模型访问客户端异常");
        }
        String cacheName = buildName(modelInfo.getModelName());

        BaseMultiLLMClient teleChatClient = getClient(cacheName);
        if (teleChatClient != null) {
            return teleChatClient;
        }
        return createAndPutChatClient(modelInfo);
    }

    private static BaseMultiLLMClient getClient(String name) {
        return MODEL_CLIENT_CACHE.get(name);
    }

    private synchronized static BaseMultiLLMClient createAndPutChatClient(ExternalModelInfo modelInfo) {
        BaseMultiLLMClient teleChatClient;

        String cacheName = buildName(modelInfo.getModelName());

        if ((teleChatClient = getClient(cacheName)) != null) {
            return teleChatClient;
        }

        //TODO 区分客户端
        if ("qwen".equals(modelInfo.getExtraConfig())){
            teleChatClient = QwenVLClient.builder().modelConfig(Model.getModelConfig(modelInfo)).build();
        } else if ("qwenOnline".equals(modelInfo.getExtraConfig())) {
            teleChatClient = QwenVLOnlineClient.builder().modelConfig(Model.getModelConfig(modelInfo)).build();
        } else {
            teleChatClient = MultiLLMClient.builder().modelConfig(Model.getModelConfig(modelInfo)).build();
        }
        MODEL_CLIENT_CACHE.put(cacheName, teleChatClient) ;
        return teleChatClient;
    }
}
