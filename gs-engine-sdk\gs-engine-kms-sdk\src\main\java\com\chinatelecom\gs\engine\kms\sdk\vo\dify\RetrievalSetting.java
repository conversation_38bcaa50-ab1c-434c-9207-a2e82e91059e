package com.chinatelecom.gs.engine.kms.sdk.vo.dify;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 检索设置
 * 支持下划线和驼峰命名的兼容性
 */
@Data
public class RetrievalSetting {

    /**
     * 返回结果数量
     * 支持输入：top_k, topK
     * 输出：top_k
     */
    @JsonProperty("top_k")
    @JsonAlias({"topK", "top_k"})
    private long topK;

    /**
     * 相似度阈值
     * 支持输入：score_threshold, scoreThreshold
     * 输出：score_threshold
     */
    @JsonProperty("score_threshold")
    @JsonAlias({"scoreThreshold", "score_threshold"})
    private double scoreThreshold;
}
