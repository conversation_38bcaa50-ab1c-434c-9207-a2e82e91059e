package com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent;

import com.chinatelecom.gs.engine.core.sdk.vo.msg.MessageCallback;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.common.DialogMessageRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.ToolMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.ActionMessage;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.IActionCallBackService;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class AgentRequest extends DialogMessageRequest implements Serializable {

    private transient MessageCallback<ToolMessageResponse> messageCallback;

    private transient IActionCallBackService<ActionMessage> actionCallback;

    public String getAgentCode() {
        return this.getBusinessCode();
    }
}
