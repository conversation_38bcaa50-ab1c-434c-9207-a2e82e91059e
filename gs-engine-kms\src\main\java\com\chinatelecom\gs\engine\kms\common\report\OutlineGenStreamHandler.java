package com.chinatelecom.gs.engine.kms.common.report;

import com.chinatelecom.gs.engine.common.utils.JsonNotNullUtils;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Response;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.report.KnwlChose;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.report.ReportOutline;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.report.ReportOutlineQueryParam;
import com.chinatelecom.gs.engine.kms.service.KnowledgeReportService;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import retrofit2.Call;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 文章大纲生成处理逻辑
 * @date 2025年05月27日
 */
@Slf4j
public class OutlineGenStreamHandler extends SseBaseStreamHandler {

    private static final Pattern OUTLINE_PATTERN = Pattern.compile("([^#]*)(#+)([^#]*)");


    /**
     * 是否被终止
     */
    @Getter
    private volatile boolean aborted = false;

    /**
     * 是否正常完成
     */
    @Getter
    private volatile boolean finished = false;

    private final SseEmitter emitter;

    private final ReportOutlineQueryParam queryParam;

    @Setter
    private ExecutorService defaultPoolExecutor;

    /**
     * 大纲信息
     */
    private final ReportOutline root;

    private final AtomicReference<OutlineExtra> finalOutline = new AtomicReference<>();

    /**
     * 后台任务列表
     */
    private final List<Future<Boolean>> taskList = new CopyOnWriteArrayList<>();

    /**
     * 内容缓冲
     */
    private final StringBuilder contentBuffer = new StringBuilder();

    /**
     * 当前正在输出的大纲信息
     */
    private final AtomicReference<ReportOutline> currentRequest = new AtomicReference<>();


    private ReportOutline getCurrentOutline() {
        return currentRequest.get();
    }

    private OutlineExtra getFinalOutline() {
        return finalOutline.get();
    }


    public OutlineGenStreamHandler(SseEmitter emitter, ReportOutlineQueryParam queryParam) {
        this.emitter = emitter;
        this.queryParam = queryParam;
        this.root = ReportOutline.builder().depth(0).children(new ArrayList<>()).build();
        this.finalOutline.set(OutlineExtra.builder().outlineRes(root.getChildren()).build());
    }

    @Override
    public void onNext(Call<ResponseBody> call, WrapLLMMessage token) {
        try {
            if (token == null) {
                return;
            }
            safeCheckMsg(token);

            if (aborted) {
                if (call != null && !call.isCanceled()) {
                    call.cancel();
                }
                return;
            }
            if (finished) {
                throw new BizException("AA105", "报告已经终止，无法继续输出");
            }

            String content = token.getContent();
            ReportMessage<OutlineExtra> reportMessage = null;
            if (content != null) {
                reportMessage = parseOutline(content);
            } else if (token.getReasoning_content() != null) {
                reportMessage = new ReportMessage<>();
                reportMessage.setReasoningContent(token.getReasoning_content());
            } else {
                log.warn("输出数据中没有思考信息和正文，暂不处理");
                return;
            }
            if (reportMessage != null) {
                sendMessage(JsonNotNullUtils.toJsonString(reportMessage));
            }
        } catch (Exception e) {
            abort(e);
            emitter.completeWithError(e);
            log.error("推送数据异常，可能用户手动终止，该错误将忽略", e);
        }
    }

    @Override
    public void onError(Throwable error, Response<WrapLLMMessage> response) {
        safeCheckMsg(error);

        log.warn("请求大模型数据发送异常, 请求终止", error);
        abort(error);
        emitter.completeWithError(error);
    }

    @Override
    public void onComplete(Response<WrapLLMMessage> response) {
        try {
            // 等待后台任务完成
            if (CollectionUtils.isNotEmpty(taskList)) {
                try {
                    for (Future<?> future : taskList) {
                        future.get(30, TimeUnit.SECONDS);
                    }
                } catch (TimeoutException e) {
                    log.warn("后台任务执行超时，不再执行结果，将抛弃相关数据", e);
                }
            }

            OutlineExtra finishedOutline = processOutline();
            sendMessage(ReportMessage.ofAdd(StringUtils.EMPTY, finishedOutline).toString());

            sendMessage(ReportMessage.ofFinish(StringUtils.EMPTY).toString());
        } catch (InterruptedException e) {
            log.info("线程被中断");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            abort(e);
            emitter.completeWithError(e);
            log.error("推送数据异常，可能用户手动终止，该错误将忽略", e);
        } finally {
            finished = true;
            emitter.complete();
        }
    }

    /**
     * 优化最终的大纲内容
     *
     * @return
     */
    private OutlineExtra processOutline() {
        OutlineExtra finalOutline = getFinalOutline();
        recursionOutline(finalOutline.getOutlineRes(), outline -> {
            String title = outline.getTitle();
            if (StringUtils.isNotEmpty(title)) {
                outline.setTitle(StringUtils.strip(title, "`\n "));
            }

            String introduction = outline.getIntroduction();
            if (StringUtils.isNotEmpty(introduction)) {
                outline.setIntroduction(StringUtils.strip(introduction, "`\n "));
            }
        });
        return finalOutline;
    }


    private void recursionOutline(List<ReportOutline> outlineList, Consumer<ReportOutline> function) {
        if (CollectionUtils.isNotEmpty(outlineList)) {
            for (ReportOutline outline : outlineList) {
                function.accept(outline);
                recursionOutline(outline.getChildren(), function);
            }
        }
    }


    @Override
    protected SseEmitter getSseEmitter() {
        return emitter;
    }

    public void abort(Throwable e) {
        aborted = true;
    }

    /**
     * 解析大纲内容
     *
     * @param content
     * @return
     */
    private ReportMessage<OutlineExtra> parseOutline(String content) {
        contentBuffer.append(content);
        if (Objects.equals(content.charAt(content.length() - 1), '#')) {
            return null;
        }

        String buffer = contentBuffer.toString();
        contentBuffer.delete(0, contentBuffer.length());
        if (StringUtils.contains(buffer, "#")) {
            Matcher matcher = OUTLINE_PATTERN.matcher(StringUtils.strip(buffer, "`"));

            while (matcher.find()) {
                String lastLevelContent = matcher.group(1);
                String level = matcher.group(2);
                String currentLevelContent = matcher.group(3);

                if (StringUtils.isNotEmpty(lastLevelContent)) {
                    appendCurrentLevel(content);
                }
                // 创建新的大纲
                createNewLevel(level, currentLevelContent);
            }
        } else {
            appendCurrentLevel(content);
        }

        // 精简输出内容，只输出标题和简介
        if (CollectionUtils.isNotEmpty(root.getChildren())) {
            ReportOutline copy = copyOutline(root);
            ReportMessage<OutlineExtra> msg = new ReportMessage();
            msg.setExtraData(OutlineExtra.builder().outlineRes(copy.getChildren()).build());
            return msg;
        }
        return null;
    }

    /**
     * 复制输出结构
     *
     * @param outline
     * @return
     */
    private ReportOutline copyOutline(ReportOutline outline) {
        ReportOutline copy = ReportOutline.builder()
                .title(outline.getTitle())
                .introduction(outline.getIntroduction())
                .build();
        if (outline.getChildren() != null) {
            List<ReportOutline> children = new ArrayList<>();
            for (ReportOutline child : outline.getChildren()) {
                children.add(copyOutline(child));
            }
            copy.setChildren(children);
        }
        return copy;
    }

    private void createNewLevel(String level, String currentLevelContent) {
        ReportOutline currentFather = findFather(level);
        if (currentFather == null) {
            throw new BizException("A0003", "大纲级别错误");
        }

        List<ReportOutline> children = currentFather.getChildren();
        if (children == null) {
            children = new ArrayList<>();
            currentFather.setChildren(children);
        }
        ReportOutline outline = ReportOutline.builder()
                .depth(level.length())
                .title(StringUtils.EMPTY)
                .introduction(StringUtils.EMPTY)
                .build();

        children.add(outline);
        currentRequest.set(outline);

        appendCurrentLevel(currentLevelContent);
    }

    private ReportOutline findFather(String level) {
        int levelLength = level.length();
        if (levelLength <= 1) {
            return root;
        } else {
            Integer fatherDepth = levelLength - 1;
            return findWithDepth(root, fatherDepth);
        }
    }

    private ReportOutline findWithDepth(ReportOutline root, Integer level) {
        if (root == null) {
            return null;
        }
        if (Objects.equals(root.getDepth(), level)) {
            return root;
        }

        List<ReportOutline> children = root.getChildren();
        if (CollectionUtils.isNotEmpty(children)) {
            for (int i = children.size() - 1; i >= 0; i--) {
                ReportOutline child = children.get(i);
                ReportOutline result = findWithDepth(child, level);
                if (result != null) {
                    return result;
                }
            }
        }
        return null;
    }


    /**
     * 追加大纲及摘要信息
     *
     * @param content
     */
    private void appendCurrentLevel(String content) {
        ReportOutline currentOutline = getCurrentOutline();
        if (currentOutline != null) {
            if (currentOutline.isTitleFinished()) {
                currentOutline.appendIntroduction(content);
            } else {
                if (StringUtils.contains(content, "\n")) {
                    currentOutline.setTitleFinished(true);
                    String[] ti = content.split("\n", 2);
                    currentOutline.appendTitle(ti[0]);
                    if (ti.length > 1) {
                        currentOutline.appendIntroduction(ti[1]);
                    }
                    // 提交召回任务
                    submitRecallTask(currentOutline);
                } else {
                    currentOutline.appendTitle(content);
                }
            }
        }
    }

    private void submitRecallTask(ReportOutline currentOutline) {
        KnwlChose chose = queryParam.getChose();
        if (chose == null || !chose.isChoseEnable()) {
            return;
        }

        // 提交参考召回任务
        Future<Boolean> future = defaultPoolExecutor.submit(() -> {
            try {
                if (isAborted() || isFinished()) {
                    return false;
                }
                SpringContextUtils.getBean(KnowledgeReportService.class).recallExample(queryParam, currentOutline);
                return true;
            } catch (Exception e) {
                log.error("召回任务异常", e);
            }
            return false;
        });
        taskList.add(future);
    }

    /**
     * 提交重写任务
     *
     * @param param
     */
    public void submitRewrite(ReportOutlineQueryParam param) {
        Future<Boolean> future = defaultPoolExecutor.submit(() -> {
            try {
                if (isAborted() || isFinished()) {
                    return false;
                }
                String result = SpringContextUtils.getBean(KnowledgeReportService.class).rewrite(param);
                if (result != null) {
                    getFinalOutline().setHeadLine(result);
                }
                return true;
            } catch (Exception e) {
                log.error("大模型重写文章主题异常", e);
            }
            return false;
        });
        taskList.add(future);
    }
}
