package com.chinatelecom.gs.engine.kms.utils;

import com.chinatelecom.gs.engine.kms.sdk.vo.dify.MetadataCondition;
import com.chinatelecom.gs.engine.kms.sdk.vo.dify.MetadataConditionItem;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.BaseItem;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 元数据条件评估器
 */
public class MetadataConditionEvaluator {

    private MetadataConditionEvaluator() {
        throw new IllegalStateException("Utility class");
    }

    private static final Logger log = LoggerFactory.getLogger(MetadataConditionEvaluator.class);


    /**
     * 评估BaseItem是否满足metadata条件
     */
    public static boolean evaluate(BaseItem item, MetadataCondition condition) {
        if (condition == null || condition.getConditions() == null || condition.getConditions().isEmpty()) {
            return true;
        }
        
        List<Boolean> results = new ArrayList<>();
        for (MetadataConditionItem conditionItem : condition.getConditions()) {
            boolean result = evaluateConditionItem(item, conditionItem);
            results.add(result);
        }
        
        // 应用逻辑操作符
        String logicalOperator = condition.getLogicalOperator();
        if ("or".equalsIgnoreCase(logicalOperator)) {
            return results.stream().anyMatch(Boolean::booleanValue);
        } else {
            // 默认为 and
            return results.stream().allMatch(Boolean::booleanValue);
        }
    }
    
    /**
     * 评估单个条件项
     */
    private static boolean evaluateConditionItem(BaseItem item, MetadataConditionItem conditionItem) {
        if (conditionItem.getName() == null || conditionItem.getName().isEmpty()) {
            return true;
        }
        
        // 对每个字段名进行评估，只要有一个匹配就返回true
        for (String fieldName : conditionItem.getName()) {
            Object fieldValue = getFieldValue(item, fieldName);
            if (evaluateComparison(fieldValue, conditionItem.getComparisonOperator(), conditionItem.getValue())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取BaseItem中指定字段的值
     */
    private static Object getFieldValue(BaseItem item, String fieldName) {
        switch (fieldName.toLowerCase()) {
            case "category":
                return item.getKnowledgeType() != null ? item.getKnowledgeType().name() : null;
            case "tag":
            case "tags":
                return item.getTags();
            case "create_time":
                return item.getCreateTime();
            case "update_time":
                return item.getUpdateTime();
            case "publish_time":
                return item.getPublishTime();
            case "file_type":
                return item.getKnowledgeType() != null ? item.getKnowledgeType().name() : null;
            case "knowledge_base":
                return item.getKnowledgeBaseName();
            case "title":
                return item.getTitle();
            case "description":
                return item.getDescription();
            case "path":
                return item.getFileKey();
            case "knowledge_code":
                return item.getKnowledgeCode();
            case "knowledge_base_code":
                return item.getKnowledgeBaseCode();
            case "create_name":
                return item.getCreateName();
            default:
                return null;
        }
    }
    
    /**
     * 执行比较操作
     */
    private static boolean evaluateComparison(Object fieldValue, String operator, String targetValue) {
        if (operator == null) {
            return true;
        }
        
        switch (operator.toLowerCase()) {
            case "empty":
                return isEmpty(fieldValue);
            case "not empty":
                return !isEmpty(fieldValue);
            case "null":
                return fieldValue == null;
            case "not null":
                return fieldValue != null;
            default:
                // 其他操作符需要目标值
                if (targetValue == null) {
                    return false;
                }
                return evaluateWithValue(fieldValue, operator, targetValue);
        }
    }
    
    /**
     * 带值的比较操作
     */
    private static boolean evaluateWithValue(Object fieldValue, String operator, String targetValue) {
        if (fieldValue == null) {
            return false;
        }
        
        // 处理数组类型（如tags）
        if (fieldValue instanceof List) {
            return evaluateListComparison((List<?>) fieldValue, operator, targetValue);
        }
        
        String fieldStr = fieldValue.toString();
        
        switch (operator.toLowerCase()) {
            case "contains":
                return fieldStr.contains(targetValue);
            case "not contains":
                return !fieldStr.contains(targetValue);
            case "start with":
                return fieldStr.startsWith(targetValue);
            case "end with":
                return fieldStr.endsWith(targetValue);
            case "is":
            case "=":
                return fieldStr.equals(targetValue);
            case "is not":
            case "≠":
                return !fieldStr.equals(targetValue);
            case ">":
                return compareNumeric(fieldValue, targetValue) > 0;
            case "<":
                return compareNumeric(fieldValue, targetValue) < 0;
            case "≥":
                return compareNumeric(fieldValue, targetValue) >= 0;
            case "≤":
                return compareNumeric(fieldValue, targetValue) <= 0;
            case "before":
                return compareTime(fieldValue, targetValue) < 0;
            case "after":
                return compareTime(fieldValue, targetValue) > 0;
            default:
                log.warn("不支持的比较操作符: {}", operator);
                return false;
        }
    }
    
    /**
     * 列表类型比较
     */
    private static boolean evaluateListComparison(List<?> list, String operator, String targetValue) {
        switch (operator.toLowerCase()) {
            case "contains":
                return list.stream().anyMatch(item -> item != null && item.toString().contains(targetValue));
            case "not contains":
                return list.stream().noneMatch(item -> item != null && item.toString().contains(targetValue));
            case "is":
            case "=":
                return list.stream().anyMatch(item -> item != null && item.toString().equals(targetValue));
            case "is not":
            case "≠":
                return list.stream().noneMatch(item -> item != null && item.toString().equals(targetValue));
            default:
                return false;
        }
    }
    
    /**
     * 数值比较
     */
    private static int compareNumeric(Object fieldValue, String targetValue) {
        try {
            if (fieldValue instanceof Number) {
                double field = ((Number) fieldValue).doubleValue();
                double target = Double.parseDouble(targetValue);
                return Double.compare(field, target);
            } else {
                double field = Double.parseDouble(fieldValue.toString());
                double target = Double.parseDouble(targetValue);
                return Double.compare(field, target);
            }
        } catch (NumberFormatException e) {
            log.warn("数值比较失败: fieldValue={}, targetValue={}", fieldValue, targetValue);
            return 0;
        }
    }

    /**
     * 时间比较
     */
    private static int compareTime(Object fieldValue, String targetValue) {
        try {
            long fieldTime;
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (fieldValue instanceof Long) {
                fieldTime = (Long) fieldValue;
            } else {
                // 尝试解析时间字符串
                Date date = dateFormat.parse(fieldValue.toString());
                fieldTime = date.getTime();
            }

            long targetTime = parseTargetTime(targetValue, dateFormat);

            return Long.compare(fieldTime, targetTime);
        } catch (ParseException | NumberFormatException e) {
            log.warn("时间比较失败: fieldValue={}, targetValue={}", fieldValue, targetValue);
            return 0;
        }
    }

    /**
     * 解析目标时间字符串或时间戳
     */
    private static long parseTargetTime(String targetValue, SimpleDateFormat dateFormat) throws ParseException, NumberFormatException {
        try {
            return Long.parseLong(targetValue);
        } catch (NumberFormatException e) {
            Date date = dateFormat.parse(targetValue);
            return date.getTime();
        }
    }
    
    /**
     * 判断值是否为空
     */
    private static boolean isEmpty(Object value) {
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return StringUtils.isEmpty((String) value);
        }
        if (value instanceof Collection) {
            return ((Collection<?>) value).isEmpty();
        }
        return false;
    }
}
