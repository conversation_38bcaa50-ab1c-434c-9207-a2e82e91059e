package com.chinatelecom.gs.plugin.mcp.manager;

import com.chinatelecom.gs.plugin.mcp.sandbox.SandboxSpec;
import com.chinatelecom.gs.plugin.mcp.sandbox.SandboxStdioClientTransport;
import com.chinatelecom.gs.plugin.mcp.sandbox.api.McpScriptApi;
import com.chinatelecom.gs.plugin.mcp.sandbox.common.SandboxTransportParam;
import com.chinatelecom.gs.plugin.mcp.sdk.client.McpClient;
import com.chinatelecom.gs.plugin.mcp.sdk.client.McpSyncClient;
import com.chinatelecom.gs.plugin.mcp.sdk.client.transport.ServerParameters;
import com.chinatelecom.gs.plugin.mcp.sdk.client.transport.StdioClientTransport;
import com.chinatelecom.gs.plugin.mcp.sdk.spec.McpClientTransport;
import com.chinatelecom.gs.plugin.mcp.sdk.spec.McpSchema;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;


@Service
@Slf4j
public class McpStdioClientManager {
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    private final Map<String, McpSyncClient> syncClientMap = new ConcurrentHashMap<>();

    private final Map<String, LocalDateTime> createTimeMap = new ConcurrentHashMap<>();

    // 在类中新增一个用于同步的锁对象Map
    private final Map<String, Object> lockMap = new ConcurrentHashMap<>();

    @Resource
    private McpScriptApi mcpScriptApi;

    @Value("${gs.scriptExecutor.apiKey:dify-sandbox}")
    private String apiKey;

    @Value("${app.plugin.mcp.npm.registry:}")
    private String npxRegistry;

    @Value("${jasypt.encryptor.password:}")
    private String jasyptPassword;

    @Value("${app.plugin.mcp.npm.token:}")
    private String token;

    @Value("#{'${app.plugin.mcp.command:java,npx,uvx}'.split(',')}")
    private List<String> commands;

    @Value("${app.plugin.mcp.stdio.sandboxOn:true}")
    private boolean mcpSandboxOn;

    @Value("${app.plugin.mcp.requestTimeout:10}")
    private Integer requestTimeout;

    @Value("${app.plugin.mcp.serverTimeout:600000}")
    private Integer expireTime;

    private McpSyncClient createClient(ServerParameters params) {
        if (StringUtils.isBlank(params.getCommand())) {
            log.debug("MCP-Client（stdio）创建失败:command不能为空");
            throw new BizException("A0001", "创建mcp-stdio插件，command不能为空");
        } else if (!commands.contains(params.getCommand())) {
            log.debug("MCP-Client（stdio）创建失败:command 不支持");
            return null;
        }
        String hashKey = String.valueOf(params.hashCode());
        McpSyncClient client = null;

        try {
            if (mcpSandboxOn) {
                SandboxTransportParam sandboxParam = new SandboxTransportParam();
                sandboxParam.setApiKey(apiKey);
                sandboxParam.setNpxRegistry(npxRegistry);
                sandboxParam.setJasyptPassword(jasyptPassword);
                sandboxParam.setNpmToken(token);
                SandboxStdioClientTransport transport = new SandboxStdioClientTransport(params, mcpScriptApi, sandboxParam);
                client = new SandboxSpec(transport)
                        .requestTimeout(Duration.ofSeconds(requestTimeout))
                        .initializationTimeout(Duration.ofSeconds(requestTimeout))
                        .capabilities(McpSchema.ClientCapabilities.builder()
                                .roots(true)      // 启用根目录支持
                                .sampling()       // 启用采样支持
                                .build())
                        .sampling(request -> {
                            // 简单的采样处理器实现
                            log.info("MCP-stdio收到采样请求: {}", request);
                            return McpSchema.CreateMessageResult.builder()
                                    .role(McpSchema.Role.ASSISTANT)
                                    .message("这是一个示例响应")
                                    .build();
                        })
                        .build();
            } else {
                McpClientTransport transport = new StdioClientTransport(params);
                client = McpClient.sync(transport)
                        .requestTimeout(Duration.ofSeconds(requestTimeout))
                        .initializationTimeout(Duration.ofSeconds(requestTimeout))
                        .capabilities(McpSchema.ClientCapabilities.builder()
                                .roots(true)      // 启用根目录支持
                                .sampling()       // 启用采样支持
                                .build())
                        .sampling(request -> {
                            // 简单的采样处理器实现
                            log.info("MCP-stdio收到采样请求: {}", request);
                            return McpSchema.CreateMessageResult.builder()
                                    .role(McpSchema.Role.ASSISTANT)
                                    .message("这是一个示例响应")
                                    .build();
                        })
                        .build();
            }
            client.initialize();
            syncClientMap.put(hashKey, client);
            createTimeMap.put(hashKey, LocalDateTime.now());
            return client;
        } catch (Exception e) {
            //catch异常，即便mcp-server不可用，仍然会新增插件信息
            log.info("MCP-Client(stdio)创建失败:{}", e.getMessage());
            return null;
        }
    }

    public McpSyncClient getOrCreate(ServerParameters params) {
        if (StringUtils.isBlank(params.getCommand())) {
            throw new BizException("A0001", "创建mcp-stdio插件，command不能为空");
        }
        String hashKey = String.valueOf(params.hashCode());
        McpSyncClient queryClient = syncClientMap.get(hashKey);
        try {
            if (Objects.nonNull(queryClient) && queryClient.ping() != null) {
                createTimeMap.put(hashKey, LocalDateTime.now());
                return queryClient;
            }
        } catch (Exception e) {
            log.info("mcp-stdio插件连接异常：{}", e.getMessage());
        }

        // 获取或创建一个唯一的锁对象
        Object lock = lockMap.computeIfAbsent(hashKey, k -> new Object());
        synchronized (lock) {
            queryClient = syncClientMap.get(hashKey);
            if (Objects.isNull(queryClient)) {
                queryClient = createClient(params);
            } else {
                Object pingRes = null;
                try {
                    pingRes = queryClient.ping();
                    if (Objects.nonNull(pingRes)) {
                        return queryClient;
                    }
                    queryClient = createClient(params);
                } catch (Exception e) {
                    queryClient = createClient(params);  // ping不通时重新创建
                }
            }
            return queryClient;
        }
    }

    public void remove(ServerParameters params) {
        String hashKey = String.valueOf(params.hashCode());
        removeByKey(hashKey);
    }

    private void removeByKey(String hashKey) {
        McpSyncClient queryClient = syncClientMap.get(hashKey);
        if (Objects.nonNull(queryClient)) {
            queryClient.close();
            syncClientMap.remove(hashKey);
            createTimeMap.remove(hashKey);
        }
    }

    @PostConstruct
    public void startCleanupTask() {
        ZoneId systemZone = ZoneId.systemDefault();
        scheduler.scheduleAtFixedRate(() -> {
            if (!syncClientMap.isEmpty()) {
                syncClientMap.forEach((key, client) -> {
                    long now = System.currentTimeMillis();
                    long epochMilli = createTimeMap.get(key).atZone(systemZone).toInstant().toEpochMilli();
                    if (now - epochMilli > expireTime) {
                        removeByKey(key);
                        log.info("MCP-stdio客户端关闭：{}", key);
                    }
                });
            }
        }, 1, 1, TimeUnit.MINUTES); // 每分钟检查一次
    }
}
