package com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class Action implements Serializable {
    /**
     *  执行名称类型
     *  @see ActionTypeEnum
     */
    private String type;
    /**
     * 执行名称编码
     */
    private String code;
    /**
     * 执行动作名称 (纯文本）
     *
     */
    private String name;
    /**
     * 执行动作状态
     * @see ActionStateEnum
     */
    private String state;
    /**
     * 父执行动作
     */
    private String parentCode;
    /**
     * 耗时
     */
    private String cost;
    /**
     * 扩展字段
     */
    private Map<String, Object> extraData;
    /**
     * 序号
     */
    private long seqId = System.currentTimeMillis();

    /**
     * 对应的消息Id
     */
    private String messageId;
}
