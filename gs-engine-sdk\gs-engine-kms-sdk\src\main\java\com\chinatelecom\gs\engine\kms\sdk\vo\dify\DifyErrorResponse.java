package com.chinatelecom.gs.engine.kms.sdk.vo.dify;

import lombok.Data;

/**
 * Dify错误响应
 */
@Data
public class DifyErrorResponse {
    
    /**
     * 错误代码
     * 1001: 无效的 Authorization 头格式
     * 1002: 授权失败
     * 2001: 知识库不存在
     */
    private Integer error_code;
    
    /**
     * API 异常描述
     */
    private String error_msg;
    
    public DifyErrorResponse(Integer errorCode, String errorMsg) {
        this.error_code = errorCode;
        this.error_msg = errorMsg;
    }
}
