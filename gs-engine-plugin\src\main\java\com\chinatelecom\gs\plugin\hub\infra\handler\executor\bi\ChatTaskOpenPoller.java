package com.chinatelecom.gs.plugin.hub.infra.handler.executor.bi;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.util.DigestUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

@Slf4j
public class ChatTaskOpenPoller {
    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(20, TimeUnit.SECONDS)
            .readTimeout(20, TimeUnit.SECONDS)
            .build();
    private static final int MAX_POLL_TIME = 120; // 最大轮询时间（秒）
    private static final int POLL_INTERVAL = 1000; // 轮询间隔（毫秒）

    public static JSONObject pollChatTask(String url, String activeBusiness, String signKey) {
        log.info("开始轮询：url：{},activeBusiness:{},signKey:{}", url, activeBusiness, signKey);
        long startTime = System.currentTimeMillis();

        while (true) {
            try {
                long requestTime = System.currentTimeMillis();
                String format = CharSequenceUtil.format("{}#{}", requestTime, signKey);
                String encode = DigestUtils.md5DigestAsHex(format.getBytes(StandardCharsets.UTF_8));
                // 1. 构建请求
                Request request = new Request.Builder()
                        .url(url)
                        .addHeader("Accept", "application/json")
                        .addHeader("active-business", activeBusiness)
                        .addHeader("sign", CharSequenceUtil.format("{}#{}", requestTime, encode))
                        .build();

                // 2. 执行同步请求
                try (Response response = client.newCall(request).execute()) {
                    log.info("请求成功，响应结果: {}", response);
                    if (!response.isSuccessful()) {
                        log.error("请求失败，状态码: " + response.code());
                        break;
                    }

                    // 3. 解析JSON响应
                    String responseBody = response.body().string();
                    log.info("响应结果: {}", responseBody);
                    JSONObject jsonObject = JSON.parseObject(responseBody);

                    // 4. 检查任务状态
                    int taskState = jsonObject.getJSONObject("data")
                            .getJSONObject("chatTask")
                            .getIntValue("taskState");

                    if (taskState == 2) {
                        log.info("任务执行成功，停止轮询");
                        return jsonObject.getJSONObject("data");
                    }
                }

                // 5. 检查超时
                long elapsed = TimeUnit.MILLISECONDS.toSeconds(
                        System.currentTimeMillis() - startTime);
                if (elapsed >= MAX_POLL_TIME) {
                    log.info("轮询超时（20秒），终止查询");
                    return null;
                }

                // 6. 间隔等待
                Thread.sleep(POLL_INTERVAL);

            } catch (InterruptedException e) {
                log.error("线程中断异常", e);
                Thread.currentThread().interrupt();
            } catch (IOException e) {
                log.error("请求异常: " + e.getMessage());
                break;
            }
        }
        return null;
    }
}
