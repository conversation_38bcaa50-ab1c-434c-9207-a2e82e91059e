package com.chinatelecom.gs.engine.robot.manage.controller.web.manage;


import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.DataResourceAccess;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.AgentAsrConfigRequest;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.AgentInterruptConfigRequest;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.AgentTtsConfigRequest;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.AgentVadConfigRequest;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentSpeechConfigService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.traffic.InformantConfigResponse;
import com.chinatelecom.gs.engine.robot.sdk.traffic.ModelResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.config.AgentAsrConfigResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.config.AgentInterruptConfigResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.config.AgentTtsConfigResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.config.AgentVadConfigResponse;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;


@RestController
@Tag(name = "机器人语音配置")
@RequestMapping({Constants.ROBOT_PREFIX + Constants.WEB_PREFIX + "/speechConfig", Constants.ROBOT_PREFIX + Constants.API_PREFIX + "/speechConfig"})
public class AgentSpeechConfigController {
    private static final String AGENT_TYPE = "agentType";

    @Autowired
    private DataResourceAccess dataResourceAccess;

    @Resource
    private AgentSpeechConfigService agentSpeechConfigService;

    @Resource
    private AgentBasicConfigService agentBasicConfigService;

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "语音合成配置保存接口")
    @PlatformRestApi(name = "语音合成配置保存接口", groupName = "机器人语音配置")
    @PostMapping("/saveTtsConfig")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "机器人语音配置", operType = "语音合成配置保存接口", operDesc = "语音合成配置保存接口", objId = "#request.agentCode")
    public Result<Boolean> saveTtsConfig(@Valid @RequestBody AgentTtsConfigRequest request){
        return Result.success(agentSpeechConfigService.saveTtsConfig(request));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "语音合成配置获取接口")
    @PlatformRestApi(name = "语音合成配置获取接口", groupName = "机器人语音配置")
    @GetMapping("/queryTtsConfig")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "机器人语音配置", operType = "语音合成配置获取接口", operDesc = "语音合成配置获取接口", objId = "#agentCode")
    public Result<AgentTtsConfigResponse> queryTtsConfig(@RequestParam String agentCode) {
        agentBasicConfigService.checkAgentAuth(agentCode, true);
        return Result.success(agentSpeechConfigService.queryTtsConfig(agentCode));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "语音识别配置保存接口")
    @PlatformRestApi(name = "语音识别配置保存接口", groupName = "机器人语音配置")
    @PostMapping("/saveAsrConfig")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "机器人语音配置", operType = "语音识别配置保存接口", operDesc = "语音识别配置保存接口", objId = "#request.agentCode")
    public Result<Boolean> saveAsrConfig(@Valid @RequestBody AgentAsrConfigRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(agentSpeechConfigService.saveAsrConfig(request));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "语音识别配置获取接口")
    @PlatformRestApi(name = "语音识别配置获取接口", groupName = "机器人语音配置")
    @GetMapping("/queryAsrConfig")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "机器人语音配置", operType = "语音识别配置获取接口", operDesc = "语音识别配置获取接口", objId = "#agentCode")
    public Result<AgentAsrConfigResponse> queryAsrConfig(@RequestParam String agentCode) {
        agentBasicConfigService.checkAgentAuth(agentCode, true);
        return Result.success(agentSpeechConfigService.queryAsrConfig(agentCode));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "智能打断配置保存接口")
    @PlatformRestApi(name = "智能打断配置保存接口", groupName = "机器人语音配置")
    @PostMapping("/saveInterruptConfig")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "机器人语音配置", operType = "智能打断配置保存接口", operDesc = "智能打断配置保存接口", objId = "#request.agentCode")
    public Result<Boolean> saveInterruptConfig(@Valid @RequestBody AgentInterruptConfigRequest request){
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(agentSpeechConfigService.saveInterruptConfig(request));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "智能打断配置获取接口")
    @PlatformRestApi(name = "智能打断配置获取接口", groupName = "机器人语音配置")
    @GetMapping("/queryInterruptConfig")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "机器人语音配置", operType = "智能打断配置获取接口", operDesc = "智能打断配置获取接口", objId = "#agentCode")
    public Result<AgentInterruptConfigResponse> queryInterruptConfig(@RequestParam String agentCode){
        agentBasicConfigService.checkAgentAuth(agentCode, true);
        return Result.success(agentSpeechConfigService.queryInterruptConfig(agentCode));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "静音检测配置保存接口")
    @PlatformRestApi(name = "静音检测配置保存接口", groupName = "机器人语音配置")
    @PostMapping("/saveVadConfig")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "机器人语音配置", operType = "静音检测配置保存接口", operDesc = "静音检测配置保存接口", objId = "#request.agentCode")
    public Result<Boolean> saveVadConfig(@Valid @RequestBody AgentVadConfigRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(agentSpeechConfigService.saveVadConfig(request));
    }

    @Operation(summary = "静音检测配置获取接口")
    @PlatformRestApi(name = "静音检测配置获取接口", groupName = "机器人语音配置")
    @GetMapping("/queryVadConfig")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "机器人语音配置", operType = "静音检测配置获取接口", operDesc = "静音检测配置获取接口", objId = "#agentCode")
    public Result<AgentVadConfigResponse> queryVadConfig(@RequestParam String agentCode) {
        agentBasicConfigService.checkAgentAuth(agentCode, true);
        return Result.success(agentSpeechConfigService.queryVadConfig(agentCode));
    }

    /**
     * 获取语音合成模型列表
     */
    @Operation(summary = "获取语音合成模型列表")
    @PlatformRestApi(name = "获取语音合成模型列表", groupName = "机器人语音配置")
    @GetMapping("/queryTtsModelList")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "机器人语音配置", operType = "获取语音合成模型列表", operDesc = "获取语音合成模型列表", objId = "null")
    public Result<List<ModelResponse>> queryTtsModelList() {
        return Result.success(agentSpeechConfigService.queryTtsModelList());
    }

    /**
     * 获取语音识别模型列表
     */
    @Operation(summary = "获取语音识别模型列表")
    @PlatformRestApi(name = "获取语音识别模型列表", groupName = "机器人语音配置")
    @GetMapping("/queryAsrModelList")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "机器人语音配置", operType = "获取语音识别模型列表", operDesc = "获取语音识别模型列表", objId = "null")
    public Result<List<ModelResponse>> queryAsrModelList() {
        return Result.success(agentSpeechConfigService.queryAsrModelList());
    }

    /**
     * 获取语音合成发音人列表
     */
    @Operation(summary = "获取语音合成发音人列表")
    @PlatformRestApi(name = "获取语音合成发音人列表", groupName = "机器人语音配置")
    @GetMapping("/queryInformantList")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "机器人语音配置", operType = "获取语音合成发音人列表", operDesc = "获取语音合成发音人列表", objId = "#modelCode")
    public Result<List<InformantConfigResponse>> queryInformantList(@Valid @NotBlank(message = "模型code不能为空") String modelCode) {
        return Result.success(agentSpeechConfigService.queryInformantList(modelCode));
    }


}