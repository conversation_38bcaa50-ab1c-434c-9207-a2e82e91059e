{"examples": [{"name": "基本查询", "description": "不带任何筛选条件的基本查询", "request": {"method": "POST", "url": "/openapi/dify/retrieval", "headers": {"Content-Type": "application/json", "Authorization": "Bearer dGVzdFRlbmFudEBhZG1pbg=="}, "body": {"knowledge_id": "kb-001", "query": "人工智能是什么", "retrieval_setting": {"top_k": 5, "score_threshold": 0.5}}}, "response": {"status": 200, "body": {"records": [{"content": "人工智能（AI）是计算机科学的一个分支...", "score": 0.98, "title": "人工智能概述", "metadata": {"path": "/docs/ai-overview.pdf", "description": "人工智能基础概念介绍", "category": "DOCUMENT", "tags": ["AI", "概念", "基础"], "create_time": 1640995200000, "file_type": "DOCUMENT"}}]}}}, {"name": "带标签筛选的查询", "description": "筛选包含特定标签的文档", "request": {"method": "POST", "url": "/openapi/dify/retrieval", "headers": {"Content-Type": "application/json", "Authorization": "Bearer dGVzdFRlbmFudEBhZG1pbg=="}, "body": {"knowledge_id": "kb-001", "query": "机器学习算法", "retrieval_setting": {"top_k": 10, "score_threshold": 0.3}, "metadata_condition": {"logical_operator": "and", "conditions": [{"name": ["tags"], "comparison_operator": "contains", "value": "机器学习"}, {"name": ["category"], "comparison_operator": "is", "value": "DOCUMENT"}]}}}}, {"name": "复杂条件筛选", "description": "使用OR逻辑和多字段匹配", "request": {"method": "POST", "url": "/openapi/dify/retrieval", "headers": {"Content-Type": "application/json", "Authorization": "Bearer dGVzdFRlbmFudEBhZG1pbg=="}, "body": {"knowledge_id": "kb-001", "query": "深度学习", "retrieval_setting": {"top_k": 8, "score_threshold": 0.4}, "metadata_condition": {"logical_operator": "or", "conditions": [{"name": ["title", "description"], "comparison_operator": "contains", "value": "神经网络"}, {"name": ["tags"], "comparison_operator": "contains", "value": "深度学习"}, {"name": ["create_time"], "comparison_operator": "after", "value": "1640995200000"}]}}}}, {"name": "时间范围筛选", "description": "筛选特定时间范围内创建的文档", "request": {"method": "POST", "url": "/openapi/dify/retrieval", "headers": {"Content-Type": "application/json", "Authorization": "Bearer dGVzdFRlbmFudEBhZG1pbg=="}, "body": {"knowledge_id": "kb-001", "query": "最新技术", "retrieval_setting": {"top_k": 5, "score_threshold": 0.6}, "metadata_condition": {"logical_operator": "and", "conditions": [{"name": ["create_time"], "comparison_operator": "after", "value": "1672531200000"}, {"name": ["update_time"], "comparison_operator": "after", "value": "1672531200000"}]}}}}, {"name": "空值检查", "description": "筛选描述不为空的文档", "request": {"method": "POST", "url": "/openapi/dify/retrieval", "headers": {"Content-Type": "application/json", "Authorization": "Bearer dGVzdFRlbmFudEBhZG1pbg=="}, "body": {"knowledge_id": "kb-001", "query": "技术文档", "retrieval_setting": {"top_k": 10, "score_threshold": 0.3}, "metadata_condition": {"logical_operator": "and", "conditions": [{"name": ["description"], "comparison_operator": "not empty"}, {"name": ["tags"], "comparison_operator": "not empty"}]}}}}], "error_examples": [{"name": "无效的Authorization头", "request": {"headers": {"Authorization": "InvalidToken"}}, "response": {"status": 403, "body": {"error_code": 1001, "error_msg": "无效的 Authorization 头格式。预期格式为 `Bearer <api-key>`。"}}}, {"name": "授权失败", "request": {"headers": {"Authorization": "Bearer aW52YWxpZEtleQ=="}}, "response": {"status": 403, "body": {"error_code": 1002, "error_msg": "授权失败"}}}, {"name": "知识库不存在", "request": {"body": {"knowledge_id": "non-existent-kb"}}, "response": {"status": 400, "body": {"error_code": 2001, "error_msg": "知识库不存在"}}}, {"name": "内部服务器错误", "response": {"status": 500, "body": {"error_code": 500, "error_msg": "内部服务器错误"}}}], "curl_examples": [{"name": "基本查询", "command": "curl -X POST 'http://localhost:8080/openapi/dify/retrieval' \\\n  -H 'Content-Type: application/json' \\\n  -H 'Authorization: Bearer dGVzdFRlbmFudEBhZG1pbg==' \\\n  -d '{\n    \"knowledge_id\": \"kb-001\",\n    \"query\": \"人工智能\",\n    \"retrieval_setting\": {\n      \"top_k\": 5,\n      \"score_threshold\": 0.5\n    }\n  }'"}, {"name": "带筛选条件的查询", "command": "curl -X POST 'http://localhost:8080/openapi/dify/retrieval' \\\n  -H 'Content-Type: application/json' \\\n  -H 'Authorization: Bearer dGVzdFRlbmFudEBhZG1pbg==' \\\n  -d '{\n    \"knowledge_id\": \"kb-001\",\n    \"query\": \"机器学习\",\n    \"retrieval_setting\": {\n      \"top_k\": 10,\n      \"score_threshold\": 0.3\n    },\n    \"metadata_condition\": {\n      \"logical_operator\": \"and\",\n      \"conditions\": [\n        {\n          \"name\": [\"tags\"],\n          \"comparison_operator\": \"contains\",\n          \"value\": \"AI\"\n        }\n      ]\n    }\n  }'"}]}