package com.chinatelecom.gs.plugin.mcp.manager;

import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.CommonHeader;
import com.chinatelecom.gs.plugin.mcp.sdk.client.McpClient;
import com.chinatelecom.gs.plugin.mcp.sdk.client.McpSyncClient;
import com.chinatelecom.gs.plugin.mcp.sdk.client.transport.HttpClientStreamableHttpTransport;
import com.chinatelecom.gs.plugin.mcp.sdk.spec.McpSchema;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;


@Service
@Slf4j
public class McpStreamablehttpClientManager {
    private ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private Map<String, McpSyncClient> syncClientMap = new ConcurrentHashMap<>();
    private Map<String, LocalDateTime> createTimeMap = new ConcurrentHashMap<>();
    private final Map<String, Object> lockMap = new ConcurrentHashMap<>();

    @Value("${app.plugin.mcp.requestTimeout:10}")
    private Integer requestTimeout;

    @Value("${app.plugin.mcp.serverTimeout:600000}")
    private Integer expireTime;

    private McpSyncClient createClient(String baseUrl, List<CommonHeader> headers) {
        if (StringUtils.isBlank(baseUrl)) {
            log.debug("MCP-Client（sse）创建失败：baseUrl不能为空");
            throw new BizException("A0001", "创建mcp-stdio插件，baseUrl不能为空");
        }
        String hashKey = String.valueOf(baseUrl.hashCode());
        Map<String, String> mapHeaders = new HashMap<>();
        if (headers != null && !headers.isEmpty()) {
            for (CommonHeader header : headers) {
                if (StringUtils.isNotBlank(header.getName()) && StringUtils.isNotBlank(header.getValue())) {
                    mapHeaders.put(header.getName(), header.getValue());
                }
            }
        }
        HttpClientStreamableHttpTransport transport = HttpClientStreamableHttpTransport.builder(baseUrl).build();

        // 创建同步客户端
        try {
            McpSyncClient client = McpClient.sync(transport)
                    .requestTimeout(Duration.ofSeconds(requestTimeout))
                    .initializationTimeout(Duration.ofSeconds(requestTimeout))
                    .capabilities(McpSchema.ClientCapabilities.builder()
                            .roots(true)      // 启用根目录支持
                            .sampling()       // 启用采样支持
                            .build())
                    .sampling(request -> {
                        // 简单的采样处理器实现
                        log.info("MCP-sse收到采样请求: {}", request);
                        return McpSchema.CreateMessageResult.builder()
                                .role(McpSchema.Role.ASSISTANT)
                                .message("这是一个示例响应")
                                .build();
                    })
                    .build();
            client.initialize();
            syncClientMap.put(hashKey, client);
            createTimeMap.put(hashKey, LocalDateTime.now());
            log.info("MCP插件创建成功：{}", hashKey);
            return client;
        } catch (Exception e) {
            log.info("MCP-Client(streamableHttp)创建失败:{}", e.getMessage());
            return null;
        }
    }

    public McpSyncClient getOrCreate(String baseUrl) {
        return this.getOrCreate(baseUrl, null);
    }

    public McpSyncClient getOrCreate(String baseUrl, List<CommonHeader> headers) {
        if (StringUtils.isBlank(baseUrl)) {
            throw new BizException("A0001", "创建mcp-streamableHttp插件，url不能为空");
        }
        String hashKey = String.valueOf(baseUrl.hashCode());
        McpSyncClient queryClient = this.syncClientMap.get(hashKey);
        try {
            if (Objects.nonNull(queryClient) && queryClient.ping() != null) {
                createTimeMap.put(hashKey, LocalDateTime.now());
                return queryClient;
            }
        } catch (Exception e) {
            log.info("mcp-streamableHttp插件连接异常：{}", e.getMessage());
        }
        Object lock = lockMap.computeIfAbsent(hashKey, k -> new Object());
        synchronized (lock) {
            queryClient = syncClientMap.get(hashKey);
            if (Objects.isNull(queryClient)) {
                queryClient = createClient(baseUrl, headers);
            } else {
                Object pingRes = null;
                try {
                    pingRes = queryClient.ping();
                    if (Objects.nonNull(pingRes)) {
                        return queryClient;
                    }
                    queryClient = createClient(baseUrl, headers);
                } catch (Exception e) {
                    queryClient = createClient(baseUrl, headers);
                }
            }
            return queryClient;
        }
    }

    public void remove(String baseUrl) {
        String hashKey = String.valueOf(baseUrl.hashCode());
        this.removeByKey(hashKey);
    }

    private void removeByKey(String hashKey) {
        McpSyncClient queryClient = syncClientMap.get(hashKey);
        if (Objects.nonNull(queryClient)) {
            queryClient.close();
            syncClientMap.remove(hashKey);
            createTimeMap.remove(hashKey);
        }
    }

    @PostConstruct
    public void startCleanupTask() {
        ZoneId systemZone = ZoneId.systemDefault();
        scheduler.scheduleAtFixedRate(() -> {
            if (!syncClientMap.isEmpty()) {
                syncClientMap.forEach((key, client) -> {
                    long now = System.currentTimeMillis();
                    long epochMilli = createTimeMap.get(key).atZone(systemZone).toInstant().toEpochMilli();
                    if (now - epochMilli > expireTime) {
                        removeByKey(key);
                        log.info("MCP-streamableHttp客户端关闭：{}", key);
                    }
                });
            }
        }, 1, 1, TimeUnit.MINUTES); // 每分钟检查一次
    }
}
