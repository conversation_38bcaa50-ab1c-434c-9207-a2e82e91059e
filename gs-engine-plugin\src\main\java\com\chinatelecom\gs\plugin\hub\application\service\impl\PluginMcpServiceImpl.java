package com.chinatelecom.gs.plugin.hub.application.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.DataResourceAccess;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.ToolFunctionParam;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.agent.client.AgentInfoRpcApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.CommonHeader;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.PluginMetaRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.ExecutePluginApiResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.PluginApiParam;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.PluginMetaInfo;
import com.chinatelecom.gs.plugin.hub.application.convertor.PluginApiConvertor;
import com.chinatelecom.gs.plugin.hub.application.service.PluginMcpService;
import com.chinatelecom.gs.plugin.hub.application.service.PluginMetaConfigService;
import com.chinatelecom.gs.plugin.hub.infra.dto.PluginApiDTO;
import com.chinatelecom.gs.plugin.hub.infra.dto.PluginApiParamDTO;
import com.chinatelecom.gs.plugin.hub.infra.dto.PluginMcpPSDTO;
import com.chinatelecom.gs.plugin.hub.infra.dto.PluginMetaDTO;
import com.chinatelecom.gs.plugin.hub.infra.enums.PluginServiceSubTypeEnum;
import com.chinatelecom.gs.plugin.hub.infra.enums.PluginServiceTypeEnum;
import com.chinatelecom.gs.plugin.hub.infra.repository.*;
import com.chinatelecom.gs.plugin.hub.model.PluginApiRequest;
import com.chinatelecom.gs.plugin.hub.model.PluginInfo;
import com.chinatelecom.gs.plugin.hub.model.QueryPluginApiParam;
import com.chinatelecom.gs.plugin.hub.model.UpdatePluginApiRequest;
import com.chinatelecom.gs.plugin.mcp.manager.McpSseClientManager;
import com.chinatelecom.gs.plugin.mcp.manager.McpStdioClientManager;
import com.chinatelecom.gs.plugin.mcp.manager.McpStreamablehttpClientManager;
import com.chinatelecom.gs.plugin.mcp.sdk.client.McpSyncClient;
import com.chinatelecom.gs.plugin.mcp.sdk.client.transport.ServerParameters;
import com.chinatelecom.gs.plugin.mcp.sdk.spec.McpSchema;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class PluginMcpServiceImpl implements PluginMcpService {
    private final PluginApiConvertor convertor = PluginApiConvertor.INSTANCE;

    @Value("${plugin.whitelist.ips:}")
    private String whitelistIps;

    @Value("#{'${app.plugin.mcp.transports:sse,stdio,streamablehttp}'.split(',')}")
    private List<String> supportedMcpTransports;

    @Resource
    private PluginMcpPSRepository pluginMcpPSRepository;

    @Resource
    private PluginApiRepository pluginApiRepository;

    @Resource
    private PluginApiParamRepository pluginApiParamRepository;

    @Resource
    private McpStdioClientManager stdioClientManager;

    @Resource
    private McpSseClientManager sseClientManager;

    @Resource
    private McpStreamablehttpClientManager streamablehttpClientManager;

    @Resource
    private PluginMetaConfigService pluginMetaService;

    @Resource
    private PluginMetaRepository pluginMetaRepository;

    @Resource
    private AgentInfoRpcApi agentInfoRpcApi;

    @Resource
    private PluginVersionInfoRepository pluginVersionInfoRepository;

    @Resource
    private DataResourceAccess dataResourceAccess;

    private Map<String, String> toEnvsMap(List<String> mapStringList) {
        if (mapStringList == null || mapStringList.size() == 0) {
            return null;
        }
        Map<String, String> envs = new HashMap<>();
        for (String envString : mapStringList) {
            String[] split = envString.split("=");
            if (split.length != 2) {
                throw new BizException("AB058", "MCP插件，envs解析异常");
            }
            envs.put(split[0], split[1]);
        }
        return envs;
    }

    private List<String> toEnvsList(Map<String, String> envsMap) {
        if (envsMap == null || envsMap.isEmpty()) {
            return null;
        }
        List<String> envs = new ArrayList<>();
        envsMap.forEach((key, value) -> {
            envs.add(key + "=" + value);
        });
        return envs;
    }

    private McpSyncClient getMcpClient(PluginMetaRequest request) {
        McpSyncClient client;
        if (PluginServiceSubTypeEnum.STDIO.getType().equals(request.getServiceSubType())) {
            ServerParameters serverParams = ServerParameters.builder(request.getCommand())
                    .env(toEnvsMap(request.getEnvs()))
                    .args(request.getArguments())
                    .build();
            client = stdioClientManager.getOrCreate(serverParams);
        } else if (PluginServiceSubTypeEnum.SSE.getType().equals(request.getServiceSubType())) {
            client = sseClientManager.getOrCreate(request.getUrl(), request.getCommonHeaders());
        } else if (PluginServiceSubTypeEnum.STREAMABLEHTTP.getType().equals(request.getServiceSubType())) {
            client = streamablehttpClientManager.getOrCreate(request.getUrl(), request.getCommonHeaders());
        } else {
            throw new BizException("AB052", "mcp插件，只支持stdio和sse两种方式");
        }
        return client;
    }

    @Override
    //@Transactional(rollbackFor = Exception.class)
    public void handleMcpCreate(PluginMetaRequest request) {
        //构建MCP-client并连接对应的MCP-service，同时可校验服务可用性,区分stdio和sse
        McpSyncClient client = getMcpClient(request);
        String pluginId = request.getPluginId();
        if (Objects.nonNull(client)) {
            //通过client获取工具列表，提示和资源信息，完成对应的信息新增
            saveMcpTools(client, pluginId);
            saveMcpPS(client, pluginId);
        }
    }

    private Integer getTypeCode(String type, String itemType) {
        //返回参数类型对应编码
        int typeCode = 0;
        if (StringUtils.isBlank(type)) {
            return typeCode;
        }
        // 先对array类型进行处理
        if (type.toLowerCase().contains("array")) {
            if (StringUtils.isBlank(itemType)) {
                typeCode = 10;
            } else {
                switch (itemType.toLowerCase()) {
                    case "string":
                        typeCode = 6;
                        break;
                    case "integer":
                        typeCode = 7;
                        break;
                    case "number":
                        typeCode = 8;
                        break;
                    case "boolean":
                        typeCode = 9;
                        break;
                    case "file":
                        typeCode = 11;
                        break;
                    default:
                        typeCode = 10; //默认object
                }
            }
            return Integer.valueOf(typeCode);
        }
        //处理非array类型
        switch (type.toLowerCase()) {
            case "integer":
                typeCode = 2;
                break;
            case "number":
                typeCode = 3;
                break;
            case "boolean":
                typeCode = 4;
                break;
            case "object":
                typeCode = 5;
                break;
            default:
                typeCode = 1; //默认字符串
        }
        return Integer.valueOf(typeCode);
    }

    private void saveToolParams(McpSchema.JsonSchema inputSchema, String pluginId, String toolId) {
        Map<String, Object> properties = inputSchema.getProperties();
        List<String> required = inputSchema.getRequired();
        //保存参数信息
        List<PluginApiParam> inputParams = new ArrayList<>();
        properties.forEach((name, desc) -> {
            PluginApiParam param = new PluginApiParam();
            param.setName(name);
            String paramDesc = JSON.toJSONString(desc);
            param.setParamDesc(paramDesc);
            param.setRequired(required != null && required.contains(name));
            // 解析desc中的type信息，并赋值
            ObjectMapper mapper = new ObjectMapper();
            try {
                JsonNode descNode = mapper.readTree(paramDesc);
                String type = descNode.has("type") ? descNode.get("type").asText() : null;
                String itemType = null;
                if (StringUtils.isNotBlank(type) && type.toLowerCase().contains("array")) {
                    JsonNode itemNode = descNode.has("items") ? descNode.get("items") : null;
                    if (Objects.nonNull(itemNode)) {
                        itemType = itemNode.has("type") ? itemNode.get("type").asText() : null;
                    }
                }
                param.setParamType(getTypeCode(type, itemType));
            } catch (Exception e) {
                log.info("保存工具参数信息，type解析异常：{}-@-  {}", e.getMessage(), JsonUtils.toJsonString(properties));
            }
            inputParams.add(param);
        });
        UpdatePluginApiRequest updatePluginApiRequest = new UpdatePluginApiRequest();
        updatePluginApiRequest.setPluginId(pluginId);
        updatePluginApiRequest.setUpdateApiType(2);  //更新请求参数
        updatePluginApiRequest.setEnabled(true);
        updatePluginApiRequest.setApiId(toolId);
        updatePluginApiRequest.setRequestParams(inputParams);
        this.createPluginApiParams(updatePluginApiRequest);
    }

    private void saveMcpTools(McpSyncClient client, String pluginId) {
        //通过client获取工具列表，完成工具信息和工具参数信息的添加
        McpSchema.ListToolsResult listToolsResult = client.listTools();
        if (Objects.isNull(listToolsResult)) {
            return;
        }
        for (McpSchema.Tool tool : listToolsResult.getTools()) {
            PluginApiRequest pluginApiRequest = new PluginApiRequest();
            pluginApiRequest.setPluginId(pluginId);
            //对于MCP插件，工具已进行封装，method和path不需要
            pluginApiRequest.setMethod(0);
            pluginApiRequest.setPath("");
            pluginApiRequest.setApiName(tool.getName());
            pluginApiRequest.setApiDesc(tool.getDescription());
            String toolId = this.createMcpPluginApi(pluginApiRequest);

            McpSchema.JsonSchema inputSchema = tool.getInputSchema();
            if (Objects.isNull(inputSchema)) {
                continue;
            }
            saveToolParams(inputSchema, pluginId, toolId);
        }
    }

    private void checkRequest(PluginApiRequest request) {
        if (StringUtils.isEmpty(request.getApiName())) {
            throw new BizException("AB008", "工具名称不能为空");
        }
        if (request.getApiName().length() > 30) {
            throw new BizException("AB009", "工具名称最长不能超过30个字符");
        }
        if (StringUtils.isEmpty(request.getApiDesc())) {
            throw new BizException("AB053", "工具描述不能为空");
        }
        //不同MCP插件，可能含有同名工具
    }

    private Long getMcpEditVersion(String pluginId) {
        Long editVersion = pluginVersionInfoRepository.getEditVersion(pluginId);
        if (editVersion == null) {
            return 1L;
        } else {
            return editVersion;
        }
    }

    private String generateApiId(String pluginId, String apiName) {
        String fuse = pluginId + apiName.hashCode();
        String id = String.valueOf(fuse.hashCode());
        // 负数hash处理
        if (id.startsWith("-")) {
            id = id.replace("-", "");
        }
        // 填充为统一长度
        String paddedId = String.format("%18s", id).replace(' ', '0');
        return paddedId;
    }

    private String createMcpPluginApi(PluginApiRequest request) {
        checkRequest(request);
        RequestInfo userInfo = RequestContext.get();
        Long editVersion = getMcpEditVersion(request.getPluginId());
        //为了编码插件更新导致的工具增删，采用pluginId+apiName的哈希值
        String apiId = this.generateApiId(request.getPluginId(), request.getApiName());

        PluginApiDTO pluginApiDTO = convertor.convert(request);
        pluginApiDTO.setApiId(apiId);
        pluginApiDTO.setPluginId(request.getPluginId());
        pluginApiDTO.setVersion(editVersion);
        pluginApiDTO.setServiceStatus(0);  //下线
        pluginApiDTO.setEnabled(true);  //启用
        pluginApiDTO.setDebugStatus(1);  //调试成功

        pluginApiDTO.setYn(0L);
        pluginApiDTO.setCreateId(userInfo.getUserId());
        pluginApiDTO.setCreateName(userInfo.getUserName());
        pluginApiDTO.setCreateTime(LocalDateTime.now());
        pluginApiDTO.setUpdateId(userInfo.getUserId());
        pluginApiDTO.setUpdateName(userInfo.getUserName());
        pluginApiDTO.setUpdateTime(LocalDateTime.now());
        pluginApiDTO.setAppCode(userInfo.getAppCode());

        pluginApiRepository.save(pluginApiDTO);
        return apiId;
    }

    private void createPluginApiParams(UpdatePluginApiRequest request) {
        if (request.getUpdateApiType() == null) {
            throw new BizException("AB054", "更新api类型不能为空");
        }
        Long editVersion = getMcpEditVersion(request.getPluginId());
        RequestInfo userInfo = RequestContext.get();
        List<PluginApiParamDTO> requestList = Lists.newArrayList();
        //参数类型请求体为1, 响应体类型为2.对MCP插件，只记录请求参数。
        List<PluginApiParam> requestParams = request.getRequestParams();
        if (CollUtil.isNotEmpty(requestParams)) {
            requestParams.forEach(res -> {
                PluginApiParamDTO pluginApiParamPO = new PluginApiParamDTO();
                BeanUtils.copyProperties(res, pluginApiParamPO);
                pluginApiParamPO.setParamId(IdGenerator.getId(null));
                pluginApiParamPO.setApiId(request.getApiId());
                pluginApiParamPO.setPluginId(request.getPluginId());
                pluginApiParamPO.setReqRspType(1);
                pluginApiParamPO.setVersion(editVersion);
                pluginApiParamPO.setParamType(res.getParamType());
                pluginApiParamPO.setRequired(res.getRequired());
                //通用字段
                pluginApiParamPO.setYn(0L);
                pluginApiParamPO.setCreateId(userInfo.getUserId());
                pluginApiParamPO.setCreateName(userInfo.getUserName());
                pluginApiParamPO.setCreateTime(LocalDateTime.now());
                pluginApiParamPO.setUpdateId(userInfo.getUserId());
                pluginApiParamPO.setUpdateName(userInfo.getUserName());
                pluginApiParamPO.setUpdateTime(LocalDateTime.now());
                requestList.add(pluginApiParamPO);
            });
        }
        if (CollectionUtils.isNotEmpty(requestList)) {
            pluginApiParamRepository.saveBatch(requestList);
        }
    }


    private void saveMcpPS(McpSyncClient client, String pluginId) {
        McpSchema.ServerCapabilities serverCapabilities = client.getServerCapabilities();
        if (Objects.isNull(serverCapabilities)) return;
        //MCP插件提示和资源信息保存
        if (Objects.nonNull(serverCapabilities.getPrompts())) {
            McpSchema.ListPromptsResult promptsResult = client.listPrompts();
            List<McpSchema.Prompt> prompts = promptsResult == null ? null : promptsResult.getPrompts();
            if (prompts != null) {
                prompts.forEach((prompt) -> {
                    savePrompt(prompt, pluginId);
                });
            }
        }

        if (Objects.nonNull(serverCapabilities.getResources())) {
            McpSchema.ListResourcesResult listResourcesResult = client.listResources();
            List<McpSchema.Resource> resources = listResourcesResult == null ? null : listResourcesResult.getResources();
            if (resources != null) {
                resources.forEach((resource) -> {
                    saveResource(resource, pluginId);
                });
            }
        }
    }

    private PluginMcpPSDTO getBaseDTO(String pluginId, Integer type) {
        String psId = IdGenerator.getId(null);
        PluginMcpPSDTO psDTO = new PluginMcpPSDTO();
        psDTO.setPluginId(pluginId);
        psDTO.setPsId(psId);
        psDTO.setPsType(type);
        Long editVersion = getMcpEditVersion(pluginId);
        psDTO.setVersion(editVersion);
        //通用字段
        RequestInfo userInfo = RequestContext.get();
        psDTO.setYn(0L);
        psDTO.setCreateId(userInfo.getUserId());
        psDTO.setCreateName(userInfo.getUserName());
        psDTO.setCreateTime(LocalDateTime.now());
        psDTO.setUpdateId(userInfo.getUserId());
        psDTO.setUpdateName(userInfo.getUserName());
        psDTO.setUpdateTime(LocalDateTime.now());
        return psDTO;
    }

    private String savePrompt(McpSchema.Prompt prompt, String pluginId) {
        if (Objects.isNull(prompt)) {
            return null;
        }
        PluginMcpPSDTO psDTO = getBaseDTO(pluginId, 1);
        psDTO.setPsName(prompt.getName());
        psDTO.setDescription(prompt.getDescription());
        psDTO.setInputSchema(JSON.toJSONString(prompt.getArguments()));
        pluginMcpPSRepository.save(psDTO);
        return psDTO.getPsId();
    }

    private String saveResource(McpSchema.Resource resource, String pluginId) {
        if (Objects.isNull(resource)) {
            return null;
        }
        PluginMcpPSDTO psDTO = getBaseDTO(pluginId, 2);
        psDTO.setPsName(resource.getName());
        psDTO.setDescription(resource.getDescription());
        psDTO.setUri(resource.getUri());
        psDTO.setMimeType(resource.getMimeType());
        pluginMcpPSRepository.save(psDTO);
        return psDTO.getPsId();
    }


    @Override
    public void dtoToInfo(PluginMetaDTO pluginMetaDTO, PluginMetaInfo pluginMetaInfo) {
        String argsString = pluginMetaDTO.getArguments();
        if (StringUtils.isNotBlank(argsString)) {
            List largsList = JSON.parseObject(argsString, List.class);
            pluginMetaInfo.setArguments(largsList);
        }
        String envsString = pluginMetaDTO.getEnvs();
        if (StringUtils.isNotBlank(envsString)) {
            Map envsMap = JSON.parseObject(envsString, Map.class);
            List envsList = toEnvsList(envsMap);
            pluginMetaInfo.setEnvs(envsList);
        }
    }

    @Override
    public void dtoPageToInfoPage(IPage<PluginMetaDTO> dtoPage, Page<PluginMetaInfo> infoPage) {
        List<PluginMetaDTO> dtoRecords = dtoPage.getRecords();
        List<PluginMetaInfo> infoRecords = infoPage.getRecords();
        int size = infoRecords.size();
        for (int i = 0; i < size; i++) {
            dtoToInfo(dtoRecords.get(i), infoRecords.get(i));
        }
    }


    @Override
    public PluginMetaDTO requestToDTO(PluginMetaRequest request, PluginMetaDTO pluginMetaDTO) {
        if (PluginServiceTypeEnum.MCP.getType().equals(request.getServiceType())) {
            pluginMetaDTO.setServiceType(PluginServiceTypeEnum.MCP.getType());
            pluginMetaDTO.setServiceSubType(request.getServiceSubType());
            if (PluginServiceSubTypeEnum.STDIO.getType().equals(request.getServiceSubType())) {
                if (StringUtils.isBlank(request.getCommand())) {
                    throw new BizException("AB055", "工具command不能为空");
                }
                pluginMetaDTO.setCommand(request.getCommand());
                pluginMetaDTO.setArguments(JSON.toJSONString(request.getArguments()));
                pluginMetaDTO.setEnvs(JSON.toJSONString(toEnvsMap(request.getEnvs())));
            } else {
                pluginMetaDTO.setCommand("");
                pluginMetaDTO.setArguments("");
                pluginMetaDTO.setEnvs("");
            }
        } else {
            pluginMetaDTO.setServiceType(PluginServiceTypeEnum.HTTP.getType());
            pluginMetaDTO.setServiceSubType("");
            pluginMetaDTO.setCommand("");
            pluginMetaDTO.setArguments("");
            pluginMetaDTO.setEnvs("");
        }
        return pluginMetaDTO;
    }


    private Boolean checkMcpUpdate(PluginMetaRequest request, PluginMetaDTO pluginMetaDTO) {
        if (!pluginMetaDTO.getServiceType().equals(request.getServiceType())) {
            return Boolean.TRUE;
        }

        if (PluginServiceSubTypeEnum.STDIO.getType().equals(request.getServiceSubType())) {
            if (StringUtils.isBlank(request.getCommand())) {
                throw new BizException("A0001", "工具command不能为空");
            }
            if (!request.getCommand().equals(pluginMetaDTO.getCommand())) {
                return Boolean.TRUE;
            }

            Boolean argsChanged = Boolean.FALSE;
            if (StringUtils.isNotBlank(pluginMetaDTO.getArguments())) {
                argsChanged = !pluginMetaDTO.getArguments().equals(JSON.toJSONString(request.getArguments()));
            } else if (StringUtils.isNotBlank(JSON.toJSONString(request.getArguments()))) {
                return Boolean.TRUE;
            }
            Boolean envsChanged = Boolean.FALSE;
            if (StringUtils.isNotBlank(pluginMetaDTO.getEnvs())) {
                envsChanged = !pluginMetaDTO.getEnvs().equals(JSON.toJSONString(toEnvsMap(request.getEnvs())));
            } else if (StringUtils.isNotBlank(JSON.toJSONString(toEnvsMap(request.getEnvs())))) {
                return Boolean.TRUE;
            }
            if (argsChanged || envsChanged) {
                return Boolean.TRUE;
            }
        } else if (PluginServiceSubTypeEnum.SSE.getType().equals(request.getServiceSubType())) {
            if (StringUtils.isBlank(request.getUrl())) {
                throw new BizException("AB056", "工具url不能为空");
            }
            if (!request.getUrl().equals(pluginMetaDTO.getUrl())) {
                return Boolean.TRUE;
            }
            //判断headers
            Boolean headersChanged = Boolean.FALSE;
            if (StringUtils.isNotBlank(pluginMetaDTO.getCommonHeaders())) {
                headersChanged = !pluginMetaDTO.getCommonHeaders().equals(JSON.toJSONString(request.getCommonHeaders()));
            } else if (StringUtils.isNotBlank(JSON.toJSONString(request.getCommonHeaders()))) {
                return Boolean.TRUE;
            }
            return headersChanged;
        }
        return Boolean.FALSE;
    }

    @Override
    public void handleMcpUpdate(PluginMetaRequest request, PluginMetaDTO pluginMetaDTO, Boolean check) {
        if (check && !checkMcpUpdate(request, pluginMetaDTO)) {
            return;
        }
        String pluginId = request.getPluginId();
        Long editVersion = pluginVersionInfoRepository.getEditVersion(request.getPluginId());
        try {
            // mcp配置有更新，可能导致资源变动，先删除原有插件的工具、提示、资源。
            pluginApiParamRepository.removeByPluginIdAndVersion(pluginId, editVersion);
            pluginApiRepository.removeByPluginIdAndVersion(pluginId, editVersion);
            pluginMcpPSRepository.removeByPluginIdAndVersion(pluginId, editVersion);
            // 重新创建插件内部资源
            handleMcpCreate(request);
        } catch (Exception e) {
            //throw new BizException("AB059", "MCP插件更新失败");
            log.info("MCP插件更新异常:{}", e.getMessage());
        }
    }


    // 插件内资源管理：工具、提示、资源
    @Override
    public void checkMcpApi(String pluginId) {
        /**
         * 对于部分操作（修改删除等），MCP插件不支持
         */
        // 获取插件编辑版本
        Long editVersion = pluginVersionInfoRepository.getEditVersion(pluginId);
        // 检查插件是否存在
        PluginMetaDTO pluginMetaDTO = pluginMetaRepository.queryByIdAndVersion(pluginId, editVersion);
        if (pluginMetaDTO == null) {
            throw new BizException("AB001", "插件不存在");
        }
        if (PluginServiceTypeEnum.MCP.getType().equals(pluginMetaDTO.getServiceType())) {
            throw new BizException("AB057", "MCP插件不支持该操作");
        }
    }

    @Override
    public void apiOnOrOff(UpdatePluginApiRequest request, PluginApiDTO pluginApiDTO) {
        if (!request.getUpdateApiType().equals(4)) {
            throw new BizException("AB057", "MCP插件不支持该操作");
        }
        if (Objects.isNull(request.getEnabled())) {
            throw new BizException("AB006", "是否启用不能为空");
        }
        RequestInfo userInfo = RequestContext.get();
        pluginApiDTO.setEnabled(request.getEnabled());
        pluginApiDTO.setUpdateId(userInfo.getUserId());
        pluginApiDTO.setUpdateName(userInfo.getUserName());
        pluginApiDTO.setUpdateTime(LocalDateTime.now());
        pluginApiRepository.updateById(pluginApiDTO);
    }

    @Override
    public Page<PluginMcpPSDTO> getPSList(QueryPluginApiParam queryParam, Integer psTypeCode) {
        // 获取插件编辑版本
        Long version;
        if (Boolean.TRUE.equals(queryParam.getOnline())) {
            version = pluginVersionInfoRepository.getPublishVersion(queryParam.getPluginId());
        } else {
            version = pluginVersionInfoRepository.getEditVersion(queryParam.getPluginId());
        }

        // 检查插件是否存在
        PluginMetaDTO pluginMetaDTO = pluginMetaRepository.queryByIdAndVersion(queryParam.getPluginId(), version);
        if (pluginMetaDTO == null) {
            throw new BizException("AB001", "插件不存在");
        }
        queryParam.setVersion(version);

        Page<PluginMcpPSDTO> pluginMcpPSDTOPage = pluginMcpPSRepository.pageQuery(queryParam, psTypeCode);
        // 查询引用次数:跟插件引用次数一致
        Integer refCount;
        Result<Integer> result = agentInfoRpcApi.queryBotRefNumByPluginId(queryParam.getPluginId(), null);
        if (result != null && result.getData() != null) {
            refCount = result.getData();
        } else {
            refCount = 0;
        }
        pluginMcpPSDTOPage.getRecords().forEach(psDTO -> {
            psDTO.setQuoteNumber(refCount);
        });
        return pluginMcpPSDTOPage;
    }

    private Set<String> getWhiteList() {
        Set<String> ipWhitelist = Arrays.stream(whitelistIps.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toCollection(HashSet::new));
        if (ipWhitelist != null && !ipWhitelist.isEmpty()) {
            return ipWhitelist;
        } else {
            return Collections.emptySet();
        }
    }

    @Override
    public boolean checkWhitelist(Set<String> ipWhitelist, PluginMetaDTO pluginMetaDTO) {
        if (PluginServiceSubTypeEnum.SSE.getType().equals(pluginMetaDTO.getServiceSubType())) {
            //对于SSE的插件，直接校验插件url
            String host = null;
            try {
                URI uri = new URI(pluginMetaDTO.getUrl());
                host = uri.getHost();
            } catch (URISyntaxException e) {
                throw new IllegalArgumentException("Invalid URL: " + pluginMetaDTO.getUrl(), e);
            }
            if (host == null || host.isEmpty()) {
                return false;
            }
            for (String allowed : ipWhitelist) {
                if (Objects.equals(host, allowed)) {
                    return true;
                }
            }
            return false;
        } else {
            //TODO 对于STDIO的插件，校验仓库服务地址
            return true;
        }
    }

    @Override
    public void checkTransport(PluginMetaDTO pluginMetaDTO) {
        String serviceType = pluginMetaDTO.getServiceType();
        String serviceSubType = pluginMetaDTO.getServiceSubType();
        if (PluginServiceTypeEnum.MCP.getType().equals(serviceType)) {
            if (!supportedMcpTransports.contains(serviceSubType)) {
                throw new BizException("AB060", "暂不支持该类型插件");
            }
        }
    }

    private String checkToolInput(PluginInfo pluginInfo, Map<String, Object> inputs, Long version) {
        List<PluginApiParam> pluginApiParams = pluginInfo.getRequestParams();
        if (inputs.size() != pluginApiParams.size()) {
            return "参数校验异常，参数列表有误。";
        }
        for (PluginApiParam apiParam : pluginApiParams) {
            if (!inputs.containsKey(apiParam.getName())) {
                return "参数校验异常，参数名称对应有误。";
            }
        }
        return null;
    }

    @Override
    public ExecutePluginApiResponse callTool(PluginMetaDTO pluginMetaDTO, PluginInfo pluginInfo, Map<String, Object> inputs, Long version) {
        ExecutePluginApiResponse res = new ExecutePluginApiResponse();
        res.setRequestParam(JSON.toJSONString(inputs));
        String checkRes = checkToolInput(pluginInfo, inputs, version);
        if (checkRes != null) {
            log.info("调用失败-参数校验异常：{}", checkRes);
            res.setSuccess(false);
            res.setErrorMsg(checkRes);
            return res;
        }

        //获取client
        PluginMetaRequest request = new PluginMetaRequest();
        request.setServiceSubType(pluginMetaDTO.getServiceSubType());
        request.setCommand(pluginMetaDTO.getCommand());
        request.setArguments(JSON.parseObject(pluginMetaDTO.getArguments(), List.class));
        List<String> envsList = toEnvsList(JSON.parseObject(pluginMetaDTO.getEnvs(), Map.class));
        request.setEnvs(envsList);
        request.setUrl(pluginMetaDTO.getUrl());

        List headerList = JSON.parseObject(pluginMetaDTO.getCommonHeaders(), List.class);
        List<CommonHeader> headers = new ArrayList<>();
        headerList.forEach(headerJson -> {
            if (headerJson instanceof JSONObject) {
                JSONObject tempheader = (JSONObject) headerJson;
                if (!tempheader.isEmpty()) {
                    String key = (String) tempheader.keySet().toArray()[0];
                    String value = tempheader.getString(key);
                    headers.add(new CommonHeader(key, value));
                }
            }
        });
        request.setCommonHeaders(headers);
        McpSyncClient mcpClient = getMcpClient(request);
        if (mcpClient == null) {
            res.setSuccess(false);
            res.setErrorMsg("调用失败-MCP插件服务连接异常");
            return res;
        }
        //调用tool
        McpSchema.CallToolResult toolResult = null;
        try {
            toolResult = mcpClient.callTool(new McpSchema.CallToolRequest(pluginInfo.getApiName(), inputs));
        } catch (Exception e) {
            res.setSuccess(false);
            res.setErrorMsg("调用异常，异常信息为：" + e.getMessage());
            return res;
        }

        if (toolResult == null || Boolean.TRUE.equals(toolResult.getIsError())) {
            res.setSuccess(false);
            if (toolResult == null) {
                res.setErrorMsg("调用异常, MCP工具返回结果为null。");
            } else {
                res.setErrorMsg("调用异常，异常信息为：" + JsonUtils.toJsonString(toolResult.getContent()));
            }
        } else {
            res.setSuccess(true);
            //解析封装工具结果
            List<McpSchema.Content> content = toolResult.getContent();
            Map<String, Object> toolRes = new HashMap<>();
            toolRes.put("result", content);
            res.setResponseParam(JSON.toJSONString(toolRes));
            log.info("调用MCP插件 {} 的工具 {} ，结果为：{}", pluginMetaDTO.getPluginName(), pluginInfo.getApiName(), JSON.toJSONString(toolResult.getContent()));
        }
        return res;
    }

    @Override
    public List<ToolFunctionParam.ParamDefinition> convertParam(List<PluginApiParam> requestParams) {
        if (CollectionUtils.isEmpty(requestParams)) {
            return null;
        }
        List<ToolFunctionParam.ParamDefinition> definitions = Lists.newArrayList();
        for (PluginApiParam param : requestParams) {
            ToolFunctionParam.ParamDefinition define = new ToolFunctionParam.ParamDefinition();
            define.setName(param.getName());
            define.setParamDesc(param.getParamDesc());
            define.setRequired(param.getRequired());
            define.setParamType(param.getParamType());
            definitions.add(define);
        }
        return definitions;
    }
}
