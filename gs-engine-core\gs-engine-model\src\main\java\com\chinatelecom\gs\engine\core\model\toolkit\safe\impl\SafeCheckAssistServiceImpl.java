package com.chinatelecom.gs.engine.core.model.toolkit.safe.impl;


import com.chinatelecom.gs.engine.core.model.toolkit.safe.SafeCheckAssistService;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.SafeCheckService;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.config.SafeCenterConfig;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.config.SafeLLMContext;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.enums.ContentTypeEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.enums.EndMarkEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.request.CheckContentRequest;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.request.CheckImageRequest;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.utils.SafeFenceAPIKeyGenerator;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.utils.SafeRequestUtils;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.AgentConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.SafeFenceConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.AgentAnswer;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年07月19日
 */
@Slf4j
@Service
@Scope("prototype")
public class SafeCheckAssistServiceImpl implements SafeCheckAssistService {

    /**
     * 安全检测通知标记, true:检查通过  false:检查未通过
     */
    private volatile boolean safeCheckFlag = true;
    /**
     * 安全检剩余任务数
     */
    private AtomicInteger safeCheckTaskNum = new AtomicInteger(0);

    /**
     * 安全检查线程池
     */
    @Resource
    @Qualifier("safeCheckThreadPool")
    private ExecutorService safeCheckThreadPool;

    /**
     * 安全检查服务
     */
    @Resource
    private SafeCheckService safeCheckService;

    /**
     * 安全检查配置
     */
    @Resource
    private SafeCenterConfig safeCenterConfig;

    /**
     * 安全检测开关,默认为true
     */
    protected boolean safeCheck;

    /**
     * 是否是用户输入导致安全拦截不通过
     */
    protected boolean userInputNotPass = false;

    /**
     * 大模型事件输出的次数
     */
    protected AtomicInteger fenceCount = new AtomicInteger(0);

    /**
     * 上次安全检查对于fenceStringBuffer的偏移位置
     */
    protected AtomicInteger safeCheckOffset = new AtomicInteger(0);

    /**
     * 存储大模型的所有输出
     */
    protected StringBuilder fenceStrBuilder = new StringBuilder();

    public SafeCheckAssistServiceImpl(ExecutorService safeCheckThreadPool, SafeCheckService safeCheckService) {
        this.safeCheckThreadPool = safeCheckThreadPool;
        this.safeCheckService = safeCheckService;
        this.safeCenterConfig = SpringContextUtils.getBean(SafeCenterConfig.class);
    }


    /**
     * 提交事件数据, 不会每次都进行安全检查, 会分批提交
     *
     * @param content
     * @param contentType
     * @param safeLLMContext
     */
    public void submitEventCheck(String content, boolean isLastCheck, ContentTypeEnum contentType, SafeLLMContext safeLLMContext) {
        if (!safeCheckFlag) {
            return;
        }
        safeCheck = safeLLMContext.getSafeFenceSwitch();
        if (safeCheck && StringUtils.isNotBlank(content)) {
            fenceStrBuilder.append(content);
            fenceCount.getAndAdd(1);

            String checkContent = null;
            if (isLastCheck) {
                checkContent = getCheckContent(this.safeCenterConfig.getCheckOffset(), isLastCheck, safeCenterConfig.isEnableOffset());
            } else {
                // 提交异步执行安全检查的任务
                if (fenceCount.get() % this.safeCenterConfig.getFenceCount() == 0) {
                    checkContent = getCheckContent(this.safeCenterConfig.getCheckOffset(), isLastCheck, safeCenterConfig.isEnableOffset());
                }
            }

            if (StringUtils.isNotBlank(checkContent)) {
                doAsyncSafeCheck(checkContent, contentType, safeLLMContext, isLastCheck ? EndMarkEnum.END : EndMarkEnum.NOT_END);
            }
        }
    }

    public boolean doAsyncSafeCheck(String content, ContentTypeEnum contentType, SafeLLMContext safeLLMContext, EndMarkEnum endMarkEnum){
        return doAsyncSafeCheck(content,contentType,safeLLMContext,endMarkEnum,false);
    }

    /**
     * 提交异步安全检查任务
     *
     * @param content
     * @param contentType
     */
    public boolean doAsyncSafeCheck(String content, ContentTypeEnum contentType, SafeLLMContext safeLLMContext, EndMarkEnum endMarkEnum, Boolean isImageSafeCheck) {
        if (!safeCheckFlag) {
            return false;
        }

        if (safeCheck) {
            safeCheckThreadPool.submit(() -> {
                try {
                    if (Objects.isNull(safeLLMContext.getSafeFenceConfig())) {
                        // 如果没有配置,则使用默认的配置
                        SafeFenceConfig safeFenceConfig = new SafeFenceConfig();
                        BeanUtils.copyProperties(safeCenterConfig, safeFenceConfig);
                        safeLLMContext.setSafeFenceConfig(safeFenceConfig);
                    }
                    safeCheckTaskNum.getAndAdd(1);
                    // 安全检测结果检查
                    boolean safeCheckResult;
                    if (Boolean.TRUE.equals(isImageSafeCheck)) {
                        CheckImageRequest checkImageRequest = SafeRequestUtils.buildBase64ImageRequest(content, contentType, safeLLMContext, endMarkEnum);
                        HashMap<String, String> header = SafeFenceAPIKeyGenerator.generateHeader(safeLLMContext.getSafeFenceConfig());
                        safeCheckResult = safeCheckService.safeCheckImage(header, checkImageRequest);
                    } else {
                        CheckContentRequest checkContentRequest = SafeRequestUtils.buildLLMContentRequest(content, contentType, safeLLMContext, endMarkEnum.getCode());
                        HashMap<String, String> header = SafeFenceAPIKeyGenerator.generateHeader(safeLLMContext.getSafeFenceConfig());
                        safeCheckResult = safeCheckService.safeCheckContent(header, checkContentRequest, safeLLMContext.getSendInvokeMQ());
                    }
                    if (!safeCheckResult) {
                        safeCheckFlag = false;
                        if (ContentTypeEnum.USER_QUERY.equals(contentType)) {
                            userInputNotPass = true;
                        }
                    }
                } catch (Exception e) {
                    log.error("【安全围栏】安全中心检测发生异常！", e);
                } finally {
                    safeCheckTaskNum.getAndAdd(-1);
                }
            });
        }
        return true;
    }

    /**
     * 获取安全检查结果
     *
     * @param isWaitResult boolean
     * @return boolean
     */

    public boolean getCheckResult(boolean isWaitResult) {
        if (!safeCheck) {
            return true;
        }

        // 持续等待 等待一秒(目前写死,每10毫秒检查一次)
        for (int i = 0; i < safeCenterConfig.getWaitTime(); i++) {
            if (!safeCheckFlag) {
                // 安全检查就是没有通过
                return false;
            }

            if (!isWaitResult) {
                return true;
            }

            int num = safeCheckTaskNum.get();
            if (num <= 0) {
                // 已经没有任务,安全检查都通过了
                break;
            }

            try {
                // 等待后再检查
                Thread.sleep(10);
            } catch (InterruptedException e) {
                log.error("线程中断异常", e);
                Thread.currentThread().interrupt();
                // 中断后直接返回true，避免继续不准确的检查
                return true;
            }
        }
        return true;
    }


    @Override
    public AgentAnswer buildMatchAnswer(AgentConfig agentConfig, String messageId) {
        return null;
    }

    @Override
    public boolean checkContentIsSafe(String content, ContentTypeEnum contentType, SafeLLMContext safeLLMContext, EndMarkEnum endMarkEnum, boolean sendInvokeMQ) {
        try {
            if (Boolean.FALSE.equals(safeLLMContext.getSafeFenceSwitch())) {
                return true;
            }
            if (Objects.isNull(safeLLMContext.getSafeFenceConfig())) {
                // 如果没有配置,则使用默认的配置
                SafeFenceConfig safeFenceConfig = new SafeFenceConfig();
                BeanUtils.copyProperties(safeCenterConfig, safeFenceConfig);
                safeLLMContext.setSafeFenceConfig(safeFenceConfig);
            }
            CheckContentRequest checkContentRequest = SafeRequestUtils.buildLLMContentRequest(content, contentType, safeLLMContext, endMarkEnum.getCode());
            HashMap<String, String> header = SafeFenceAPIKeyGenerator.generateHeader(safeLLMContext.getSafeFenceConfig());
            boolean safeCheckResult = safeCheckService.safeCheckContent(header, checkContentRequest, sendInvokeMQ);
            if (!safeCheckResult) {
                log.info("【Agent】【SafeFence】 请求{} 未通过安全围栏", content);
                return false;
            }
        } catch (Exception e) {
            log.error("【安全围栏】安全中心检测发生异常！", e);
        }
        return true;
    }

    /**
     * 获取本次安全检查的内容
     *
     * @return
     */
    protected String getCheckContent(Integer checkOffsetConfig, boolean isLastCheck, boolean enableOffset) {
        if (!enableOffset) {
            return fenceStrBuilder.toString();
        }
        // 短信大模型需要人工审核, 最后一次送入全部数据,暂时没有更好的方式了
        if (isLastCheck) {
            return fenceStrBuilder.toString();
        }

        int indexStart = Math.max(0, safeCheckOffset.get() - checkOffsetConfig);
        int indexEnd = fenceStrBuilder.length();
        safeCheckOffset.set(indexEnd);
        return fenceStrBuilder.substring(indexStart, indexEnd);
    }

}
