package com.chinatelecom.gs.workflow.core.workflow.core.context;

import com.alibaba.fastjson.annotation.JSONField;
import com.chinatelecom.gs.engine.core.sdk.vo.msg.MessageCallback;
import com.chinatelecom.gs.engine.robot.sdk.enums.InteractionTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntentAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.ChatTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.ToolMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.Action;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.ActionMessage;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.IActionCallBackService;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.DagStatusEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.DialogStatusEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.EnvTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.WorkflowTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.model.dag.*;
import com.chinatelecom.gs.workflow.core.workflow.core.model.node.DagNode;
import com.chinatelecom.gs.workflow.core.workflow.core.model.node.NodeInfo;
import com.chinatelecom.gs.workflow.core.workflow.core.model.node.NodeLog;
import com.chinatelecom.gs.workflow.core.workflow.core.model.node.NodeParam;
import com.chinatelecom.gs.workflow.core.workflow.core.model.x6.Workflow;
import com.google.common.base.Joiner;
import lombok.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @USER: pengmc1
 * @DATE: 2024/8/21 14:27
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DagContext {

    /**
     * 图参数
     */
    private DagParam dagParam;
    /**
     * 图元数据
     */
    private Dag dag;

    /**
     * 当前图执行情况
     */
    private DagInfo dagInfo;
    /**
     * 当前图执行日志信息
     */
    private DagLog dagLog;

    /**
     * 执行线程池
     */
    private ExecutorService executor;

    /**
     * 主线程阻塞等待所有结束节点执行完成
     */
    private CountDownLatch syncLatch;
    /**
     * 正在执行的节点，用于异常后，中断流程
     */
    private Map<Thread, DagNode> runningThreadMap = new ConcurrentHashMap<>();
    /**
     * 正在执行的节点，用于判断流程是否结束。 Key: 节点执行ID
     * 为什么不能用runningThreadMap, Thread要进入线程才确定，会有时间差。
     */
    private Map<String,DagNode> runningNodeMap = new ConcurrentHashMap<>();
    /**
     * 是否为主流程
     */
    private Boolean mainFlow = true;

    /**
     * 对话状态，控制只有一个输出
     */
    private AtomicInteger dialogStatus = new AtomicInteger(DialogStatusEnum.INIT.getValue());

    /**
     * 节点路径
     */
    private List<String> nodePaths = new ArrayList<>();
    /**
     * 对话消息列表
     */
    private List<ToolIntentAnswer> toolAnswers;
    /**
     * 执行过程列表
     */
    private ActionMessage action;

    /**
     * 初始化Dag
     *
     * @param dag
     */
    public synchronized void initDag(Dag dag) {
        this.dag = dag;
        initDagInfo(dag);
        initDagLog(dag);
    }

    /**
     * 初始化图数据
     *
     * @param dag
     */
    public synchronized void initDagInfo(Dag dag) {
        if (Objects.isNull(this.dagInfo)) {
            this.dagInfo = new DagInfo(dag.getWorkflow().getWorkflowId(), dag.getWorkflow().getWorkflowName());
            this.dagInfo.getDagStatus().set(DagStatusEnum.RUNNING.getValue());
            this.dagInfo.setStartTime(LocalDateTime.now());
        }
    }

    /**
     * 初始化图日志信息
     *
     * @param dag
     */
    public synchronized void initDagLog(Dag dag) {
        if (Objects.isNull(this.dagLog)) {
            this.dagLog = new DagLog(dag.getWorkflow().getWorkflowId());
        }
    }

    /**
     * 初始化节点数据
     *
     * @param nodeId
     */
    public synchronized void initNodeInfo(String nodeId, String nodeName) {
        if (Objects.isNull(this.dagInfo) && Objects.nonNull(this.dag)) {
            initDagInfo(this.dag);
        }
        if (!Objects.isNull(this.dagInfo) && !this.dagInfo.getNodeInfoMap().containsKey(nodeId)) {
            this.dagInfo.getNodeInfoMap().put(nodeId, new NodeInfo(nodeId, nodeName));
        }
    }

    /**
     * 初始化节点日志信息
     *
     * @param nodeId
     */
    public synchronized void initNodeLog(String nodeId) {
        if (Objects.isNull(this.dagLog)) {
            initDagLog(this.dag);
        }
        if (!this.dagLog.getNodeLogMap().containsKey(nodeId)) {
            this.dagLog.getNodeLogMap().put(nodeId, new NodeLog(nodeId));
        }
    }

    /**
     * 获取节点执行情况
     *
     * @param nodeId
     * @return
     */
    public synchronized NodeInfo getNodeInfo(String nodeId) {
        if (!this.dagInfo.getNodeInfoMap().containsKey(nodeId)) {
            this.dagInfo.getNodeInfoMap().put(nodeId, new NodeInfo(nodeId, null));
        }
        return this.dagInfo.getNodeInfoMap().get(nodeId);
    }

    /**
     * 获取节点日志信息
     *
     * @param nodeId
     * @return
     */
    public synchronized NodeLog getNodeLog(String nodeId) {
        if (!this.dagLog.getNodeLogMap().containsKey(nodeId)) {
            this.dagLog.getNodeLogMap().put(nodeId, new NodeLog(nodeId));
        }
        return this.dagLog.getNodeLogMap().get(nodeId);
    }
    /**
     * 获取会话ID
     *
     * @return
     */
    @JSONField(serialize = false)
    public String getSessionId() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getSessionId).orElse(null);
    }

    /**
     * 获取上行消息ID
     *
     * @return
     */
    @JSONField(serialize = false)
    public String getMessageId() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getMessageId).orElse(null);
    }

    /**
     * 获取下线消息ID
     *
     * @return
     */
    @JSONField(serialize = false)
    public String getDownMessageId() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getDownMessageId).orElse(null);
    }

    /**
     * 获取debug
     *
     * @return
     */
    @JSONField(serialize = false)
    public Boolean getDebug() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getDebug).orElse(false);
    }

    /**
     * 获取环境，默认为测试环境
     *
     * @return
     */
    @JSONField(serialize = false)
    public EnvTypeEnum getEnv() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getEnv).orElse(EnvTypeEnum.TEST);
    }


    /**
     * 获取输入参数
     *
     * @return
     */
    @JSONField(serialize = false)
    public Map<String, Object> getInputParamMap() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getInputParamMap).orElse(null);
    }

    /**
     * 获取输入全局变量
     *
     * @return
     */
    @JSONField(serialize = false)
    public Map<String, Object> getGlobalVarMap() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getGlobalVarMap).orElse(null);
    }

    /**
     * 获取输入参数
     *
     * @return
     */
    @JSONField(serialize = false)
    public WorkflowTypeEnum getWorkflowType() {
        if (StringUtils.isBlank(this.dag.getWorkflow().getWorkflowType())) {
            return WorkflowTypeEnum.WORKFLOW;
        }
        return WorkflowTypeEnum.getByCode(this.dag.getWorkflow().getWorkflowType());
    }

    /**
     * 获取工作流ID
     *
     * @return
     */
    @JSONField(serialize = false)
    public String getWorkflowId() {
        return Optional.ofNullable(this.dag).map(Dag::getWorkflow).map(Workflow::getWorkflowId).orElse(null);
    }

    /**
     * 获取节点路径
     *
     * @return
     */
    @JSONField(serialize = false)
    public String getNodePath() {
        return Joiner.on("###").join(this.nodePaths);
    }

    /**
     * 获取appCode
     *
     * @return
     */
    @JSONField(serialize = false)
    public String getAppCode() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getAppCode).orElse(null);
    }

    /**
     * 获取agentCode
     *
     * @return
     */
    @JSONField(serialize = false)
    public String getAgentCode() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getAgentCode).orElse(null);
    }

    /**
     * 获取用户ID
     *
     * @return
     */
    @JSONField(serialize = false)
    public String getUserId() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getUserId).orElse(null);
    }

    /**
     * 获取对话类型,默认为对话类型
     * @return
     */
    @JSONField(serialize = false)
    public String getChatType() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getChatType).orElse(ChatTypeEnum.CHAT.getCode());
    }

    /**
     * 获取交互类型
     *
     * @return
     */
    @JSONField(serialize = false)
    public InteractionTypeEnum getInteractionType() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getInteractionType).orElse(InteractionTypeEnum.SYNC);
    }

    /**
     * 获取异步回调接口
     *
     * @return
     */
    @JSONField(serialize = false)
    public MessageCallback<ToolMessageResponse> getMessageCallback() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getMessageCallback).orElse(null);
    }

    /**
     * 获取异步回调接口
     *
     * @return
     */
    @JSONField(serialize = false)
    public IActionCallBackService<ActionMessage> getActionCallback() {
        return Optional.ofNullable(this.dagParam).map(DagParam::getActionCallBack).orElse(null);
    }

    @JSONField(serialize = false)
    public List<Action> getCalculateActions() {
        return Optional.ofNullable(this.getActionCallback()).map(callback -> callback.onCalculate(this.action))
                .map(ActionMessage::getActions).orElse(Collections.emptyList());
    }

    /**
     * 添加对话消息
     *
     * @param toolAnswer
     */
    public synchronized void addToolAnswer(ToolIntentAnswer toolAnswer) {
        if (CollectionUtils.isEmpty(this.toolAnswers)) {
            this.toolAnswers = new ArrayList<>();
        }
        if (Objects.nonNull(toolAnswer)) {
            this.toolAnswers.add(toolAnswer);
        }
    }

    /**
     * 批量添加对话消息
     *
     * @param toolAnswers
     */
    public synchronized void addToolAnswers(List<ToolIntentAnswer> toolAnswers) {
        if (CollectionUtils.isEmpty(this.toolAnswers)) {
            this.toolAnswers = new ArrayList<>();
        }
        if (CollectionUtils.isNotEmpty(toolAnswers)) {
            this.toolAnswers.addAll(toolAnswers);
        }
    }

    /**
     * 批量添加执行过程
     *
     * @param actions
     */
    public synchronized void addActions(List<Action> actions) {
        if (this.action == null) {
            this.action = ActionMessage.builder().actions(new ArrayList<>()).build();
        }
        if (CollectionUtils.isNotEmpty(actions)) {
            this.action.getActions().addAll(actions);
        }
    }

    /**
     * 清理执行的节点
     * @param nodeExecuteId 节点执行ID
     * @return
     */
    public synchronized Integer removeRunningNode(String nodeExecuteId){
        //从运行节点Map中移除
        if (this.runningNodeMap.containsKey(nodeExecuteId)) {
            this.runningNodeMap.remove(nodeExecuteId);
        }
        return this.runningNodeMap.size();
    }

    /**
     * 初始化节点相关信息
     * 1、初始化节点运行数据信息NodeInfo，存储节点运行过程中的状态、数据等
     * 2、初始化节点运行日志信息NodeLog，存储节点运行过程中的日志
     * 3、初始化上游节点参数，由上游节点带过来的一些参数信息
     * 4、将当前线程写入到运行线程Map中
     */
    public synchronized void initNodeContext(DagNode node){
        //记录线程
        this.runningThreadMap.put(Thread.currentThread(), node);
        //初始化节点运行数据
        this.initNodeInfo(node.getNodeId(), node.getNodeType().getDesc());
        //初始化节点日志数据
        this.initNodeLog(node.getNodeId());
        //记录前面触发节点参数信息
        NodeParam nodeParam = DagHolder.getNodeParam();
        if (Objects.isNull(nodeParam)) {
            nodeParam = new NodeParam(node.getNodeId(), node.getNodeId(), false);
        }
        //保留最开始的节点状态
        NodeInfo nodeInfo = this.getNodeInfo(node.getNodeId());
        nodeInfo.setRunId(UUID.randomUUID().toString());
        nodeInfo.setPreNodeStatus(nodeInfo.getNodeStatus().get());
        nodeInfo.getNodeParamMap().put(nodeParam.getPreNodeId(), nodeParam);
    }
}
