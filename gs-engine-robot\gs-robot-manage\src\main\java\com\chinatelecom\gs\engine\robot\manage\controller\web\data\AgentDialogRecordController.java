package com.chinatelecom.gs.engine.robot.manage.controller.web.data;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.util.IdUtil;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.DataResourceAccess;
import com.chinatelecom.gs.engine.robot.manage.common.check.AgentCodeCheck;
import com.chinatelecom.gs.engine.robot.manage.common.utils.IPageUtils;
import com.chinatelecom.gs.engine.robot.manage.data.domain.convert.VoBeanConverter;
import com.chinatelecom.gs.engine.robot.manage.data.domain.dto.AgentDialogRecordDTO;
import com.chinatelecom.gs.engine.robot.manage.data.domain.dto.AgentDialogRecordQueryDTO;
import com.chinatelecom.gs.engine.robot.manage.data.domain.request.AgentDialogRecordExportRequest;
import com.chinatelecom.gs.engine.robot.manage.data.domain.request.AgentDialogRecordQueryRequest;
import com.chinatelecom.gs.engine.robot.manage.data.domain.response.AgentDialogDetailQueryPageResponse;
import com.chinatelecom.gs.engine.robot.manage.data.domain.response.AgentDialogDetailQueryResponse;
import com.chinatelecom.gs.engine.robot.manage.data.domain.response.AgentDialogRecordExportResponse;
import com.chinatelecom.gs.engine.robot.manage.data.domain.response.AgentDialogRecordQueryResponse;
import com.chinatelecom.gs.engine.robot.manage.data.service.AgentDialogMessageManageService;
import com.chinatelecom.gs.engine.robot.manage.data.service.AgentDialogRecordManageService;
import com.chinatelecom.gs.engine.robot.manage.data.service.AgentDialogService;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.data.AgentDialogRecordInfoDTO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.data.IntentDetailDTO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.data.ThumbTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.data.enums.MessageSendEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.MessageFeedbackRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.Page;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.PageImpl;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@RestController
@Tag(name = "agent会话记录", description = "agent dialog record")
@RequestMapping({Constants.ROBOT_PREFIX + Constants.WEB_PREFIX + "/dialogRecord", Constants.ROBOT_PREFIX + Constants.API_PREFIX + "/dialogRecord"})
public class AgentDialogRecordController {

    private static final String AGENT_TYPE = "agentType";

    /**
     * 点踩选项
     */
    @Value("#{'${app.agent.thumb.options.down:答案错误,出现乱码,存在敏感信息,存在偏见和歧视,内容没什么帮助}'.split(',')}")
    private List<String> downOptions;
    /**
     * 点赞选项
     */
    //@Value("#{'${app.agent.thumb.options.up:回答准确可靠,答案全面实用,简洁清晰}'.split(',')}")
    //private List<String> upOptions;

    @Value("${app.agent.thumb.tip:感谢您的反馈和帮助}")
    private String thumbTip;

    @Resource
    private AgentDialogService agentDialogService;

    @Resource
    private AgentDialogRecordManageService agentDialogRecordManageService;

    @Resource
    private AgentDialogMessageManageService agentDialogMessageManageService;

    @Autowired
    private DataResourceAccess dataResourceAccess;

    @Autowired
    private AgentBasicConfigService agentBasicInfoService;

    @AgentCodeCheck
    @Operation(summary = "分页查询会话记录")
    @PlatformRestApi(name = "分页查询会话记录", groupName = "agent会话记录")
    @GetMapping("/page")
    @AuditLog(businessType = "agent会话记录管理", operType = "分页查询会话记录", operDesc = "分页查询会话记录", objId = "#queryParam.agentCode")
    public Result<PageImpl<AgentDialogRecordQueryResponse>> getDialogRecord(
            @Valid AgentDialogRecordQueryRequest queryParam) {
        agentBasicInfoService.checkAgentAuth(queryParam.getAgentCode(), true);
        AgentDialogRecordQueryDTO queryDTO = VoBeanConverter.INSTANCE.convert(queryParam);
        Page<AgentDialogRecordDTO> recordPage = agentDialogService.listByPage(queryDTO);
        return Result.success(IPageUtils.convert(recordPage, VoBeanConverter.INSTANCE::convert));
    }

    @AgentCodeCheck
    @Operation(summary = "查询全部会话记录")
    @PlatformRestApi(name = "查询全部会话记录", groupName = "agent会话记录")
    @GetMapping("/list")
    @AuditLog(businessType = "agent会话记录管理", operType = "查询全部会话记录", operDesc = "查询全部会话记录", objId = "#request.agentCode")
    public Result<List<AgentDialogRecordQueryResponse>> queryAll(@Valid AgentDialogRecordExportRequest request) {
        AgentDialogRecordQueryDTO queryDTO = VoBeanConverter.INSTANCE.convert(request);
        List<AgentDialogRecordDTO> list = agentDialogService.list(queryDTO);
        return Result.success(Optional.of(list)
                .orElse(Collections.emptyList())
                .stream()
                .map(VoBeanConverter.INSTANCE::convert)
                .collect(Collectors.toList()));
    }

    @AgentCodeCheck
    @Operation(summary = "导出会话记录")
    @PlatformRestApi(name = "导出会话记录", groupName = "agent会话记录")
    @GetMapping("/export")
    @AuditLog(businessType = "agent会话记录管理", operType = "导出会话记录", operDesc = "导出会话记录", objId = "#request.agentCode")
    @HideFromApiTypes(ApiType.OPENAPI)
    public void export(@Valid AgentDialogRecordExportRequest request, HttpServletResponse response) throws IOException {
        String appId = request.getAgentCode();
        request.setAgentCode(appId);
        agentBasicInfoService.checkAgentAuth(request.getAgentCode(), true);
        List<AgentDialogRecordExportResponse> resultData = Optional.of(
                        agentDialogService.list(VoBeanConverter.INSTANCE.convert(request))).
                orElse(Collections.emptyList()).stream().map(VoBeanConverter.INSTANCE::convert).map(dto -> {
                    AgentDialogRecordExportResponse result = VoBeanConverter.INSTANCE.convert(dto);
                    result.setDialogDuration(secondsToString(Integer.valueOf(dto.getDialogDuration())));
                    return result;
                }).collect(Collectors.toList());
        OutputStream outputStream = null;
        try {
            // 创建导出参数
            ExportParams params = new ExportParams("会话记录", "sheet1", ExcelType.XSSF);
            // 导出Excel文件
            Workbook workbook = ExcelExportUtil.exportExcel(params, AgentDialogRecordExportResponse.class, resultData);
            String fileName = URLEncoder.encode("会话记录.xlsx", "UTF-8");
            // 输出文件流
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            workbook.close();
        } finally {
            if (outputStream != null) {
                outputStream.close();
            }
        }
    }

    /**
     * 返回时间
     *
     * @param seconds
     * @return
     */
    private String secondsToString(Integer seconds) {
        if (seconds == null || seconds == 0) {
            return "0秒";
        }
        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        int remainingSeconds = seconds % 60;
        // 根据情况构造输出字符串
        String output = "";
        if (hours > 0) {
            output += hours + "小时";
        }
        if (hours > 0 || minutes > 0) {
            output += minutes + "分";
        }
        output += remainingSeconds + "秒";
        return output;
    }

    @AgentCodeCheck
    @Operation(summary = "查询会话详情")
    @PlatformRestApi(name = "查询会话详情", groupName = "agent会话记录")
    @GetMapping("/detail")
    @AuditLog(businessType = "agent会话记录管理", operType = "查询会话详情", operDesc = "查询会话详情", objId = "#agentCode")
    public Result<AgentDialogDetailQueryResponse> getDialogDetail(@RequestParam(name = "agentCode") String agentCode,
                                                                  @RequestParam(name = "sessionId") String sessionId){
        return Result.success(
                VoBeanConverter.INSTANCE.convert(agentDialogService.queryBySessionId(agentCode, sessionId)));
    }

    @AgentCodeCheck
    @Operation(summary = "分页查询会话详情")
    @PlatformRestApi(name = "分页查询会话详情", groupName = "agent会话记录")
    @GetMapping("/detailByPage")
    @AuditLog(businessType = "agent会话记录管理", operType = "分页查询会话详情", operDesc = "分页查询会话详情", objId = "#agentCode")
    public Result<AgentDialogDetailQueryPageResponse> getDialogDetail(@RequestParam(name = "agentCode") String agentCode,
                                                                      @RequestParam(name = "sessionId") String sessionId,
                                                                      @RequestParam(name = "maxTimeStamp", required = false) Long maxTimeStamp,
                                                                      @RequestParam(name = "pageNo") Integer pageNo,
                                                                      @RequestParam(name = "pageSize") Integer pageSize) {
        return Result.success(agentDialogService.queryBySessionIdByPage(agentCode, sessionId, maxTimeStamp, pageNo, pageSize));
    }

    @AgentCodeCheck
    @Operation(summary = "查询会话详情v2")
    @PlatformRestApi(name = "查询会话详情v2", groupName = "agent会话记录")
    @GetMapping("/detailV2")
    @AuditLog(businessType = "agent会话记录管理", operType = "查询会话详情v2", operDesc = "查询会话详情v2", objId = "#agentCode")
    public Result<AgentDialogDetailQueryResponse> getDialogDetailV2(@RequestParam(name = "agentCode") String agentCode,
                                                                    @RequestParam(name = "sessionId") String sessionId, @RequestParam(name = "limit") Integer limit) {
        return Result.success(
                VoBeanConverter.INSTANCE.convert(agentDialogService.queryBySessionIdV2(agentCode, sessionId, limit)));
    }

    @AgentCodeCheck
    @Operation(summary = "分页查询会话详情V3")
    @PlatformRestApi(name = "分页查询会话详情", groupName = "agent会话记录")
    @GetMapping("/detailV3")
    @AuditLog(businessType = "agent会话记录管理", operType = "分页查询会话详情", operDesc = "分页查询会话详情", objId = "#agentCode")
    public Result<AgentDialogDetailQueryPageResponse> getDialogDetailV3(@RequestParam(name = "agentCode") String agentCode,
                                                                        @RequestParam(name = "sessionId") String sessionId,
                                                                        @RequestParam(name = "limit", required = false) Integer limit,
                                                                        @RequestParam(name = "maxTimeStamp", required = false) Long maxTimeStamp,
                                                                        @RequestParam(name = "pageNo", required = false) Integer pageNo,
                                                                        @RequestParam(name = "pageSize", required = false) Integer pageSize) {
        return Result.success(agentDialogService.queryBySessionIdByPageV3(agentCode, sessionId, limit, maxTimeStamp, pageNo, pageSize));
    }

    @Operation(summary = "测试会话埋点")
    @PlatformRestApi(name = "测试会话埋点", groupName = "agent会话记录")
    @HideFromApiTypes(ApiType.OPENAPI)
    @GetMapping("/log")
    @AuditLog(businessType = "agent会话记录管理", operType = "测试会话埋点", operDesc = "测试会话埋点", objId = "null")
    public Result<?> testLog() {
        AgentDialogRecordInfoDTO question = buildQuestion();
        agentDialogRecordManageService.save(question);
        AgentDialogRecordInfoDTO answer = buildAnswer(question.getMessageId());
        agentDialogRecordManageService.save(answer);
        return Result.success(null);
    }

    private AgentDialogRecordInfoDTO buildQuestion() {
        AgentDialogRecordInfoDTO agentDialogRecordDTO = new AgentDialogRecordInfoDTO();
        agentDialogRecordDTO.setMessageSend(MessageSendEnum.CUSTOMER);
        agentDialogRecordDTO.setTenantId("0");
        agentDialogRecordDTO.setCustomerId("heweixingtest");
        agentDialogRecordDTO.setSessionId("heweixing123456");
        agentDialogRecordDTO.setMessageId(IdUtil.fastSimpleUUID());
        agentDialogRecordDTO.setUpMessageId("");
        agentDialogRecordDTO.setAgentCode("f88813b781854d69bb8f4686f67a81ea");
        agentDialogRecordDTO.setEntryCode("default");
        agentDialogRecordDTO.setMessageTime(System.currentTimeMillis());
        agentDialogRecordDTO.setMessageType("RICH_TEXT");
        agentDialogRecordDTO.setContent("这是问题");
        return agentDialogRecordDTO;
    }

    private AgentDialogRecordInfoDTO buildAnswer(String messageId) {
        AgentDialogRecordInfoDTO agentDialogRecordDTO = new AgentDialogRecordInfoDTO();
        agentDialogRecordDTO.setMessageSend(MessageSendEnum.AGENT);
        agentDialogRecordDTO.setTenantId("0");
        agentDialogRecordDTO.setCustomerId("heweixingtest1");
        agentDialogRecordDTO.setSessionId("heweixing1234561");
        agentDialogRecordDTO.setMessageId(IdUtil.fastSimpleUUID());
        agentDialogRecordDTO.setUpMessageId(messageId);
        agentDialogRecordDTO.setAgentCode("f88813b781854d69bb8f4686f67a81ea");
        agentDialogRecordDTO.setEntryCode("default");
        agentDialogRecordDTO.setMessageTime(System.currentTimeMillis());
        agentDialogRecordDTO.setMessageType("RICH_TEXT");
        agentDialogRecordDTO.setContent("这是回答");
        List<IntentDetailDTO> intentDetails = Lists.newArrayList();
        IntentDetailDTO intentDetail = new IntentDetailDTO();
        intentDetail.setIntentId("heweixing12345");
        intentDetail.setIntentName("知识点标题");
        intentDetail.setDialogEngineType("FAQ");
        intentDetail.setIntentResponseEnum("DIRECT");
        intentDetail.setDataSourceTypeEnum("ROBOT");
        intentDetail.setAgentCode("12345heweixing");
        intentDetail.setScore(1.0);
        intentDetail.setKmsCode("heweixing123456");
        intentDetail.setRetrieveType("faq");
        intentDetails.add(intentDetail);
        agentDialogRecordDTO.setIntents(intentDetails);
        return agentDialogRecordDTO;
    }


    @AgentCodeCheck
    @Operation(summary = "message点赞点踩")
    @PlatformRestApi(name = "message点赞点踩", groupName = "agent会话记录")
    @PostMapping("/thumb/set")
    @AuditLog(businessType = "agent会话记录管理", operType = "message点赞点踩", operDesc = "message点赞点踩", objId = "#request.agentCode")
    public Result<String> setThumbType(@RequestBody MessageFeedbackRequest request) {
        String res = agentDialogService.setThumbType(request);
        if (res == null) {
            return Result.failed(Result.INVALID_PARAM, "参数有误");
        } else {
            return Result.success(thumbTip);
        }
    }


    @Operation(summary = "点赞点踩反馈选项")
    @PlatformRestApi(name = "点赞点踩反馈选项", groupName = "agent会话记录")
    @PostMapping("/thumb/options")
    @AuditLog(businessType = "agent会话记录管理", operType = "点赞点踩反馈选项", operDesc = "点赞点踩反馈选项", objId = "#request.agentCode")
    public Result<List<String>> options(@RequestBody MessageFeedbackRequest request) {
        String thumbType = request.getThumbType();
        if (ThumbTypeEnum.DOWN.getOperation().equals(thumbType)) {
            return Result.success(this.downOptions);
        } else {
            return Result.success(Collections.emptyList());
        }
        //return Result.success(agentDialogMessageManageService.getOptions(thumbType));
    }
}
