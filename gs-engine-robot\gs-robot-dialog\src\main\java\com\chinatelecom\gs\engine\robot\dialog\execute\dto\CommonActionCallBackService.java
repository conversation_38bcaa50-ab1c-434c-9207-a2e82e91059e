package com.chinatelecom.gs.engine.robot.dialog.execute.dto;

import com.chinatelecom.gs.engine.core.sdk.vo.msg.MessageCallback;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.SystemIntentEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.common.Header;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntentAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.ToolMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.AnswerResponseType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.ToolAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.Action;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.ActionMessage;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.IActionCallBackService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.DialogMessageTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.SendMessageTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.model.dag.DagHolder;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@NoArgsConstructor
@Slf4j
public class CommonActionCallBackService implements IActionCallBackService<ActionMessage> {

    private MessageCallback<ToolMessageResponse> messageCallback;

    private Header header;

    public CommonActionCallBackService(MessageCallback<ToolMessageResponse> messageCallback, Header header) {
        this.messageCallback = messageCallback;
        this.header = header;
    }

    @Override
    public void onStart(ActionMessage actionMessage) {
        if (Objects.isNull(actionMessage) || CollectionUtils.isEmpty(actionMessage.getActions())) {
            return;
        }
        lockAndInvoke(buildMessageResponse(actionMessage));
    }

    @Override
    public void onComplete(ActionMessage actionMessage) {
        if (Objects.isNull(actionMessage) || CollectionUtils.isEmpty(actionMessage.getActions())) {
            return;
        }
        lockAndInvoke(buildMessageResponse(actionMessage));
        addActions(actionMessage.getActions());
    }

    @Override
    public void onError(ActionMessage actionMessage) {
        if (Objects.isNull(actionMessage) || CollectionUtils.isEmpty(actionMessage.getActions())) {
            return;
        }
        lockAndInvoke(buildMessageResponse(actionMessage));
    }

    @Override
    public ActionMessage onCalculate(ActionMessage actionMessage) {
        if (Objects.isNull(actionMessage) || CollectionUtils.isEmpty(actionMessage.getActions())) {
            return actionMessage;
        }

        List<Action> allActions = actionMessage.getActions();

        List<Action> resultList = allActions.stream()
                .collect(Collectors.groupingBy(Action::getCode))
                .values().stream()
                .map(items -> items.stream().max(Comparator.comparingLong(Action::getSeqId)).get())
                .collect(Collectors.toList());
        actionMessage.setActions(resultList);
        return actionMessage;
    }

    private void lockAndInvoke(ToolMessageResponse msg) {
        try {
            if (getMessageCallBack() == null) {
                return;
            }
            getMessageCallBack().lock();
            getMessageCallBack().invoke(msg);
        } catch (Exception e) {
            log.error("处理过程消息失败", e);
        } finally {
            getMessageCallBack().unlock();
        }
    }

    private MessageCallback getMessageCallBack() {
        return this.messageCallback;
    }

    public ToolMessageResponse buildMessageResponse(ActionMessage actionMessage) {
        ToolAnswer toolAnswer = new ToolAnswer();
        toolAnswer.setAnswerType(AnswerTypeEnum.MARKDOWN);
        toolAnswer.setResponseType(AnswerResponseType.LLM);
        if (ObjectUtils.isEmpty(actionMessage.getMessageId())) {
            toolAnswer.setMessageId(header.getTrack().getDownMessageId());
        } else {
            toolAnswer.setMessageId(actionMessage.getMessageId());
        }
        toolAnswer.setActions(actionMessage.getActions());

        ToolIntentAnswer toolIntentAnswer = new ToolIntentAnswer();
        toolIntentAnswer.setToolIntentId(SystemIntentEnum.LLM.getCode());
        toolIntentAnswer.setDialogEngineType(DialogEngineType.LLM);
        toolIntentAnswer.setToolAnswer(toolAnswer);

        ToolMessageResponse toolMessageResponse = ToolMessageResponse.builder()
                .downMsgId(header.getTrack().getDownMessageId())
                .sessionId(header.getTrack().getSessionId())
                .userId(header.getUser().getUserId())
                .eventType(SendMessageTypeEnum.ADD)
                .messageType(DialogMessageTypeEnum.PART)
                .upMsgId(header.getTrack().getMessageId())
                .toolAnswer(toolIntentAnswer)
                .build();
        return toolMessageResponse;
    }

    private void addActions(List<Action> actions) {
        if (DagHolder.getContext() != null) {
            DagHolder.getContext().addActions(actions);
        }
    }
}
