package com.chinatelecom.gs.plugin.mcp.sdk.client.transport;

import com.chinatelecom.gs.plugin.mcp.sdk.client.transport.ResponseSubscribers.ResponseEvent;
import com.chinatelecom.gs.plugin.mcp.sdk.spec.*;
import com.chinatelecom.gs.plugin.mcp.sdk.util.Assert;
import com.chinatelecom.gs.plugin.mcp.sdk.util.Utils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.reactivestreams.Publisher;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.io.IOException;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;


@Slf4j
public class HttpClientStreamableHttpTransport implements McpClientTransport {

    private static final String DEFAULT_ENDPOINT = "/mcp/";
    private static final String MESSAGE_EVENT_TYPE = "message";
    private static final String APPLICATION_JSON = "application/json";
    private static final String TEXT_EVENT_STREAM = "text/event-stream";
    public static int NOT_FOUND = 404;
    public static int METHOD_NOT_ALLOWED = 405;
    public static int BAD_REQUEST = 400;

    private final CloseableHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final URI baseUri;
    private final String endpoint;
    private final boolean openConnectionOnStartup;
    private final boolean resumableStreams;
    private final AtomicReference<DefaultMcpTransportSession> activeSession = new AtomicReference<>();


    private final AtomicReference<Function<Mono<McpSchema.JSONRPCMessage>, Mono<McpSchema.JSONRPCMessage>>> handler = new AtomicReference<>();
    private final AtomicReference<Consumer<Throwable>> exceptionHandler = new AtomicReference<>();
    private Map<String, String> headers;

    public HttpClientStreamableHttpTransport(ObjectMapper objectMapper, String baseUri, String endpoint, boolean resumableStreams, boolean openConnectionOnStartup, Map<String, String> headers) {
        this.objectMapper = objectMapper;
        this.baseUri = URI.create(baseUri);
        this.endpoint = endpoint;
        this.resumableStreams = resumableStreams;
        this.openConnectionOnStartup = openConnectionOnStartup;
        this.httpClient = HttpClients.createDefault();
        this.activeSession.set(createTransportSession());
        this.headers = headers;
    }

    public static Builder builder(String baseUri, Map<String, String> headers) {
        return new Builder(baseUri, headers);
    }

    public static Builder builder(String baseUri) {
        return new Builder(baseUri, null);
    }

    @Override
    public Mono<Void> connect(Function<Mono<McpSchema.JSONRPCMessage>, Mono<McpSchema.JSONRPCMessage>> handler) {
        return Mono.deferContextual(ctx -> {
            this.handler.set(handler);
            if (this.openConnectionOnStartup) {
                log.info("Eagerly opening connection on startup");
                return this.reconnect(null).onErrorComplete(t -> {
                    log.warn("Eager connect failed ", t);
                    return true;
                }).then();
            }
            return Mono.empty();
        });
    }

    private DefaultMcpTransportSession createTransportSession() {
        Function<String, Publisher<Void>> onClose = sessionId -> sessionId == null ? Mono.empty() : createDelete(sessionId);
        return new DefaultMcpTransportSession(onClose);
    }

    private Publisher<Void> createDelete(String sessionId) {
        return Mono.fromCallable(() -> {
            HttpGet request = new HttpGet(Utils.resolveUri(this.baseUri, this.endpoint));
            request.setHeader("Cache-Control", "no-cache");
            request.setHeader("mcp-session-id", sessionId);
            try (CloseableHttpResponse response = httpClient.execute(request)) {
                return null;
            }
        });
    }

    public void setExceptionHandler(Consumer<Throwable> handler) {
        log.info("Exception handler registered");
        this.exceptionHandler.set(handler);
    }

    private void handleException(Throwable t) {
        log.info("Handling exception for session {}", sessionIdOrPlaceholder(this.activeSession.get()), t);
        if (t instanceof McpTransportSessionNotFoundException) {
            McpTransportSession<?> invalidSession = this.activeSession.getAndSet(createTransportSession());
            log.info("Server does not recognize session {}. Invalidating.", invalidSession.sessionId());
            invalidSession.close();
        }
        Consumer<Throwable> handler = this.exceptionHandler.get();
        if (handler != null) {
            handler.accept(t);
        }
    }

    @Override
    public Mono<Void> closeGracefully() {
        return Mono.defer(() -> {
            log.info("Graceful close triggered");
            DefaultMcpTransportSession currentSession = this.activeSession.getAndSet(createTransportSession());
            if (currentSession != null) {
                return currentSession.closeGracefully();
            }
            return Mono.empty();
        });
    }

    private Mono<Disposable> reconnect(McpTransportStream<Disposable> stream) {
        return Mono.deferContextual(ctx -> {
            if (stream != null) {
                log.info("Reconnecting stream {} with lastId {}", stream.streamId(), stream.lastId());
            } else {
                log.info("Reconnecting with no prior stream");
            }
            final AtomicReference<Disposable> disposableRef = new AtomicReference<>();
            final McpTransportSession<Disposable> transportSession = this.activeSession.get();

            HttpGet request = new HttpGet(Utils.resolveUri(this.baseUri, this.endpoint));
            request.setHeader("Accept", TEXT_EVENT_STREAM);
            request.setHeader("Cache-Control", "no-cache");

            if (transportSession != null && transportSession.sessionId().isPresent()) {
                request.setHeader("mcp-session-id", transportSession.sessionId().get());
            }
            if (stream != null && stream.lastId().isPresent()) {
                request.setHeader("last-event-id", stream.lastId().get());
            }
            if (headers != null && !headers.isEmpty()) {
                headers.forEach((key, value) -> {
                    if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
                        request.setHeader(key, value);
                    }
                });
            }

            Flux<ResponseEvent> mappedResponse = Flux.create(sseSink -> {
                try {
                    CloseableHttpResponse response = httpClient.execute(request);
                    String contentValue = null;
                    if (response != null) {
                        Header contentType = response.getEntity().getContentType();
                        contentValue = contentType.getValue();
                    }
                    if (!TEXT_EVENT_STREAM.equals(contentValue)) {
                        ResponseSubscribers.sseToBodySubscriber(response, sseSink);
                    } else {
                        //会维持一个GET的ping连接
                        response.close();
                        sseSink.complete();
                    }
                } catch (IOException e) {
                    sseSink.error(e);
                }
            });

            Flux<McpSchema.JSONRPCMessage> jsonrpcMessageFlux = mappedResponse.map(responseEvent -> (ResponseSubscribers.SseResponseEvent) responseEvent)
                    .flatMap(responseEvent -> {
                        int statusCode = responseEvent.responseInfo().getStatusLine().getStatusCode();
                        if (statusCode >= 200 && statusCode < 300) {
                            if (MESSAGE_EVENT_TYPE.equals(responseEvent.getSseEvent().getEvent())) {
                                try {
                                    McpSchema.JSONRPCMessage message = McpSchema.deserializeJsonRpcMessage(this.objectMapper, responseEvent.getSseEvent().getData());
                                    List<McpSchema.JSONRPCMessage> messages = new ArrayList<>();
                                    messages.add(message);
                                    Tuple2<Optional<String>, Iterable<McpSchema.JSONRPCMessage>> idWithMessages = Tuples.of(Optional.ofNullable(responseEvent.getSseEvent().getId()), messages);
                                    McpTransportStream<Disposable> sessionStream = stream != null ? stream : new DefaultMcpTransportStream<>(this.resumableStreams, this::reconnect);
                                    return Flux.from(sessionStream.consumeSseStream(Flux.just(idWithMessages)));
                                } catch (IOException ioException) {
                                    return Flux.error(new McpError("Error parsing JSON-RPC message: " + responseEvent.getSseEvent().getData()));
                                }
                            }
                        } else if (statusCode == METHOD_NOT_ALLOWED) {
                            log.info("The server does not support SSE streams, using request-response mode.");
                            return Flux.empty();
                        } else if (statusCode == NOT_FOUND) {
                            String sessionIdRepresentation = sessionIdOrPlaceholder(transportSession);
                            McpTransportSessionNotFoundException exception = new McpTransportSessionNotFoundException("Session not found for session ID: " + sessionIdRepresentation);
                            return Flux.error(exception);
                        } else if (statusCode == BAD_REQUEST) {
                            String sessionIdRepresentation = sessionIdOrPlaceholder(transportSession);
                            McpTransportSessionNotFoundException exception = new McpTransportSessionNotFoundException("Session not found for session ID: " + sessionIdRepresentation);
                            return Flux.error(exception);
                        }
                        return Flux.error(new McpError("Received unrecognized SSE event type: " + responseEvent.getSseEvent().getEvent()));
                    });

            Flux<McpSchema.JSONRPCMessage> connectionFlux = jsonrpcMessageFlux
                    .flatMap(jsonrpcMessage -> this.handler.get().apply(Mono.just(jsonrpcMessage)))
                    .onErrorMap(CompletionException.class, t -> t.getCause())
                    .onErrorComplete(t -> {
                        this.handleException(t);
                        return true;
                    })
                    .doFinally(s -> {
                        Disposable ref = disposableRef.getAndSet(null);
                        if (ref != null) {
                            transportSession.removeConnection(ref);
                        }
                    })
                    .contextWrite(ctx);

            Disposable connection = connectionFlux.subscribe();

            disposableRef.set(connection);
            transportSession.addConnection(connection);
            return Mono.just(connection);
        });
    }

    interface ResponseHandler<T> {
        void handle(HttpResponse response) throws IOException;
    }

    private ResponseHandler<Void> toSendMessageBodySubscriber(FluxSink<ResponseEvent> sink) {
        return response -> {
            String contentType = "";
            HttpEntity entity = response.getEntity();
            if (entity != null && entity.getContentType() != null) {
                contentType = entity.getContentType().getValue().toLowerCase();
            }

            if (contentType.contains(TEXT_EVENT_STREAM)) {
                log.info("Received SSE stream response, using line subscriber:{}", response);
                ResponseSubscribers.sseToBodySubscriber(response, sink);
            } else if (contentType.contains(APPLICATION_JSON)) {
                log.info("Received response, using string subscriber:{}", response);
                ResponseSubscribers.aggregateBodySubscriber(response, sink);
            } else {
                log.info("Received Bodyless response, using discarding subscriber:{}", response);
                ResponseSubscribers.bodilessBodySubscriber(response, sink);
            }
        };
    }

    public String toString(McpSchema.JSONRPCMessage message) {
        try {
            return this.objectMapper.writeValueAsString(message);
        } catch (IOException e) {
            throw new RuntimeException("Failed to serialize JSON-RPC message", e);
        }
    }

    @Override
    public Mono<Void> sendMessage(McpSchema.JSONRPCMessage sendMessage) {
        Mono<Object> accept = Mono.create(messageSink -> {
            log.info("Sending message {}", sendMessage);
            final AtomicReference<Disposable> disposableRef = new AtomicReference<>();
            final McpTransportSession<Disposable> transportSession = this.activeSession.get();
            HttpPost request = new HttpPost(Utils.resolveUri(this.baseUri, this.endpoint));
            request.setHeader("Accept", TEXT_EVENT_STREAM + ", " + APPLICATION_JSON);
            request.setHeader("Content-Type", APPLICATION_JSON);
            request.setHeader("Cache-Control", "no-cache");

            if (transportSession != null && transportSession.sessionId().isPresent()) {
                request.setHeader("mcp-session-id", transportSession.sessionId().get());
            }

            if (headers != null && !headers.isEmpty()) {
                headers.forEach((key, value) -> {
                    if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
                        request.setHeader(key, value);
                    }
                });
            }

            String jsonBody = this.toString(sendMessage);
            request.setEntity(new StringEntity(jsonBody, "UTF-8"));

            Flux<ResponseEvent> responseEventFlux = Flux.create(responseEventSink -> {
                try {
                    CloseableHttpResponse response = httpClient.execute(request);
                    toSendMessageBodySubscriber(responseEventSink).handle(response);
                } catch (IOException e) {
                    responseEventSink.error(e);
                }
            });

            Flux<McpSchema.JSONRPCMessage> messageFlux = responseEventFlux.flatMap(responseEvent -> {
                HttpResponse httpResponse = responseEvent.responseInfo();
                Header sessionIdHeader = httpResponse.getFirstHeader("mcp-session-id");
                if (transportSession.markInitialized(sessionIdHeader.getValue())) {
                    reconnect(null).contextWrite(messageSink.contextView()).subscribe();
                }
                String sessionRepresentation = sessionIdOrPlaceholder(transportSession);

                int statusCode = httpResponse.getStatusLine().getStatusCode();
                if (statusCode >= 200 && statusCode < 300) {
                    String contentType = httpResponse.getFirstHeader("Content-Type").getValue().toLowerCase();
                    if (StringUtils.isBlank(contentType)) {
                        log.info("No content type returned for POST in session {}", sessionRepresentation);
                        messageSink.success();
                        return Flux.empty();
                    } else if (contentType.contains(TEXT_EVENT_STREAM)) {
                        Flux<ResponseSubscribers.SseEvent> sseFlux = Flux.just(((ResponseSubscribers.SseResponseEvent) responseEvent).getSseEvent());
                        Flux<McpSchema.JSONRPCMessage> jsonrpcMessageFlux = sseFlux.flatMap(sseEvent -> {
                            try {
                                McpSchema.JSONRPCMessage message = McpSchema.deserializeJsonRpcMessage(this.objectMapper, sseEvent.getData());
                                List<McpSchema.JSONRPCMessage> messages = new ArrayList<>();
                                messages.add(message);
                                Tuple2<Optional<String>, Iterable<McpSchema.JSONRPCMessage>> idWithMessages = Tuples.of(Optional.ofNullable(sseEvent.getId()), messages);
                                McpTransportStream<Disposable> sessionStream = new DefaultMcpTransportStream<>(this.resumableStreams, this::reconnect);
                                log.info("Connected stream {}", sessionStream.streamId());
                                messageSink.success();
                                return Flux.from(sessionStream.consumeSseStream(Flux.just(idWithMessages)));
                            } catch (IOException ioException) {
                                return Flux.error(new McpError("Error parsing JSON-RPC message: " + sseEvent.getData()));
                            }
                        });
                        return jsonrpcMessageFlux;
                    } else if (contentType.contains(APPLICATION_JSON)) {
                        messageSink.success();
                        String data = ((ResponseSubscribers.AggregateResponseEvent) responseEvent).getData();
                        try {
                            return Mono.just(McpSchema.deserializeJsonRpcMessage(objectMapper, data));
                        } catch (IOException e) {
                            return Mono.error(e);
                        }
                    }
                    log.info("Unknown media type {} returned for POST in session {}", contentType, sessionRepresentation);
                    return Flux.error(new RuntimeException("Unknown media type returned: " + contentType));
                } else if (statusCode == NOT_FOUND) {
                    McpTransportSessionNotFoundException exception = new McpTransportSessionNotFoundException("Session not found for session ID: " + sessionRepresentation);
                    return Flux.error(exception);
                } else if (statusCode == BAD_REQUEST) {
                    McpTransportSessionNotFoundException exception = new McpTransportSessionNotFoundException("Bad request for session ID: " + sessionRepresentation);
                    return Flux.error(exception);
                }
                return Flux.error(new RuntimeException("Failed to send message: " + responseEvent));
            });

            Disposable connection = messageFlux.flatMap(jsonRpcMessage -> this.handler.get().apply(Mono.just(jsonRpcMessage)))
                    .onErrorMap(CompletionException.class, t -> t.getCause())
                    .onErrorComplete(t -> {
                        this.handleException(t);
                        messageSink.error(t);
                        return true;
                    })
                    .doFinally(s -> {
                        log.info("SendMessage finally: {}", s);
                        Disposable ref = disposableRef.getAndSet(null);
                        if (ref != null) {
                            transportSession.removeConnection(ref);
                        }
                    })
                    .contextWrite(messageSink.contextView())
                    .subscribe();

            disposableRef.set(connection);
            transportSession.addConnection(connection);
        });
        return accept.then();
    }


    private static String sessionIdOrPlaceholder(McpTransportSession<?> transportSession) {
        return transportSession.sessionId().orElse("[missing_session_id]");
    }

    @Override
    public <T> T unmarshalFrom(Object data, TypeReference<T> typeRef) {
        return this.objectMapper.convertValue(data, typeRef);
    }

    public static class Builder {
        private final String baseUri;
        private ObjectMapper objectMapper;
        private String endpoint = DEFAULT_ENDPOINT;
        private boolean resumableStreams = true;
        private boolean openConnectionOnStartup = false;
        private Map<String, String> headers;

        private Builder(String baseUri, Map<String, String> headers) {
            Assert.hasText(baseUri, "baseUri must not be empty");
            this.baseUri = baseUri;
            this.headers = headers;
        }

        public Builder objectMapper(ObjectMapper objectMapper) {
            Assert.notNull(objectMapper, "ObjectMapper must not be null");
            this.objectMapper = objectMapper;
            return this;
        }

        public Builder endpoint(String endpoint) {
            Assert.hasText(endpoint, "endpoint must be a non-empty String");
            this.endpoint = endpoint;
            return this;
        }

        public Builder resumableStreams(boolean resumableStreams) {
            this.resumableStreams = resumableStreams;
            return this;
        }

        public Builder openConnectionOnStartup(boolean openConnectionOnStartup) {
            this.openConnectionOnStartup = openConnectionOnStartup;
            return this;
        }

        public HttpClientStreamableHttpTransport build() {
            ObjectMapper objectMapper = this.objectMapper != null ? this.objectMapper : new ObjectMapper();
            return new HttpClientStreamableHttpTransport(objectMapper, baseUri, endpoint, resumableStreams, openConnectionOnStartup, headers);
        }
    }
}