# 命名兼容性指南

## 概述

为了同时支持Dify标准的下划线命名和Java客户端的驼峰命名习惯，我们实现了优雅的命名兼容性方案。

## 🎯 核心特性

- **输入兼容**: 同时支持下划线和驼峰格式的JSON输入
- **输出统一**: 始终输出下划线格式，保持Dify标准兼容性
- **向后兼容**: 不影响现有Dify客户端的调用
- **Java友好**: Java客户端可以使用熟悉的驼峰命名

## 📋 支持的字段映射

| 下划线格式 | 驼峰格式 | 说明 |
|-----------|---------|------|
| `knowledge_id` | `knowledgeId` | 知识库ID |
| `retrieval_setting` | `retrievalSetting` | 检索设置 |
| `top_k` | `topK` | 返回结果数量 |
| `score_threshold` | `scoreThreshold` | 相似度阈值 |
| `metadata_condition` | `metadataCondition` | 元数据筛选条件 |
| `logical_operator` | `logicalOperator` | 逻辑操作符 |
| `comparison_operator` | `comparisonOperator` | 比较操作符 |

## 🔧 技术实现

使用Jackson的`@JsonProperty`和`@JsonAlias`注解组合：

```java
@JsonProperty("knowledge_id")           // 输出时使用下划线
@JsonAlias({"knowledgeId", "knowledge_id"})  // 输入时支持两种格式
private String knowledge_id;
```

## 📝 使用示例

### 1. Dify标准格式（下划线）

```json
{
    "knowledge_id": "kb-001",
    "query": "人工智能",
    "retrieval_setting": {
        "top_k": 5,
        "score_threshold": 0.8
    },
    "metadata_condition": {
        "logical_operator": "and",
        "conditions": [
            {
                "name": ["category"],
                "comparison_operator": "is",
                "value": "DOCUMENT"
            }
        ]
    }
}
```

### 2. Java客户端格式（驼峰）

```json
{
    "knowledgeId": "kb-001",
    "query": "人工智能",
    "retrievalSetting": {
        "topK": 5,
        "scoreThreshold": 0.8
    },
    "metadataCondition": {
        "logicalOperator": "and",
        "conditions": [
            {
                "name": ["category"],
                "comparisonOperator": "is",
                "value": "DOCUMENT"
            }
        ]
    }
}
```

### 3. 混合格式（也支持）

```json
{
    "knowledgeId": "kb-001",           // 驼峰
    "query": "人工智能",
    "retrieval_setting": {             // 下划线
        "topK": 5,                     // 驼峰
        "score_threshold": 0.8         // 下划线
    }
}
```

## 🚀 Java客户端使用示例

```java
// 创建请求对象
DifySearchRequest request = new DifySearchRequest();
request.setKnowledge_id("kb-java-client");
request.setQuery("Java开发");

// 设置检索参数
RetrievalSetting setting = new RetrievalSetting();
setting.setTop_k(10);
setting.setScore_threshold(0.6);
request.setRetrieval_setting(setting);

// 设置元数据筛选条件
MetadataConditionItem condition = new MetadataConditionItem();
condition.setName(Arrays.asList("title", "description"));
condition.setComparison_operator("contains");
condition.setValue("Java");

MetadataCondition metadataCondition = new MetadataCondition();
metadataCondition.setLogical_operator("and");
metadataCondition.setConditions(Collections.singletonList(condition));
request.setMetadata_condition(metadataCondition);

// 发送请求
ResponseEntity<?> response = restTemplate.postForEntity(
    "/openapi/dify/retrieval", 
    request, 
    Object.class
);
```

## 🧪 测试验证

运行测试类验证兼容性：

```bash
mvn test -Dtest=NamingCompatibilityTest
```

测试覆盖场景：
- ✅ 下划线格式输入解析
- ✅ 驼峰格式输入解析  
- ✅ 混合格式输入解析
- ✅ 输出格式一致性（始终下划线）
- ✅ Java客户端使用场景

## 📊 响应格式

无论输入使用何种命名格式，响应始终使用下划线格式：

```json
{
    "records": [
        {
            "content": "文档内容",
            "score": 0.98,
            "title": "文档标题",
            "metadata": {
                "path": "/docs/example.pdf",
                "description": "示例文档",
                "create_time": 1640995200000,
                "knowledge_base": "示例知识库"
            }
        }
    ]
}
```

## ⚡ 性能说明

- **零性能损失**: Jackson注解在编译时处理，运行时无额外开销
- **内存友好**: 不需要额外的对象转换
- **序列化高效**: 直接使用Jackson原生机制

## 🔄 迁移指南

### 现有Dify客户端
- **无需修改**: 继续使用下划线格式
- **完全兼容**: 所有现有请求都能正常工作

### 新Java客户端
- **可选择**: 可以使用驼峰格式或下划线格式
- **推荐**: 在Java代码中使用驼峰格式提高可读性
- **灵活**: 可以混合使用两种格式

## 🛠️ 扩展其他VO类

如需为其他VO类添加命名兼容性，按以下模式添加注解：

```java
@JsonProperty("original_field_name")
@JsonAlias({"camelCaseFieldName", "original_field_name"})
private String original_field_name;
```

## 📋 最佳实践

1. **API设计**: 新API优先考虑命名兼容性
2. **文档说明**: 在API文档中明确说明支持的命名格式
3. **测试覆盖**: 为每种命名格式编写测试用例
4. **客户端SDK**: 在SDK中使用符合语言习惯的命名风格
