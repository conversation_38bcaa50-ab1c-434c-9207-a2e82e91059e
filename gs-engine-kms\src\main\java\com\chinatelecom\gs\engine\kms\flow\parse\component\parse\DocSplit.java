package com.chinatelecom.gs.engine.kms.flow.parse.component.parse;

import com.alibaba.ttl.TtlRunnable;
import com.chinatelecom.gs.engine.kms.dto.knowledge.KnowledgeParseResult;
import com.chinatelecom.gs.engine.kms.dto.knowledge.KnowledgeSplitDTO;
import com.chinatelecom.gs.engine.kms.es.repository.KmsParseTraceEsRepository;
import com.chinatelecom.gs.engine.kms.flow.parse.component.context.KnowledgeParseConfigContext;
import com.chinatelecom.gs.engine.kms.flow.parse.component.context.KnowledgeParseContext;
import com.chinatelecom.gs.engine.kms.flow.parse.dto.ParseTraceLog;
import com.chinatelecom.gs.engine.kms.model.dto.KnowledgeDTO;
import com.chinatelecom.gs.engine.kms.repository.KnowledgeRepository;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeStatus;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelecom.gs.engine.kms.sdk.enums.SplitIndexType;
import com.chinatelecom.gs.engine.kms.sdk.utils.SetUtils;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeParseConfig;
import com.chinatelecom.gs.engine.kms.service.SplitService;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;


@Slf4j
@LiteflowComponent(id = "DocSplit", name = "文档切分")
public class DocSplit extends BaseNodeParseComponent {

    protected static final Set<KnowledgeType> SPLIT_TYPES = SetUtils.of(KnowledgeType.PDF, KnowledgeType.WORD, KnowledgeType.PPT,
            KnowledgeType.TEXT, KnowledgeType.VIDEO, KnowledgeType.AUDIO, KnowledgeType.IMAGE, KnowledgeType.MD, KnowledgeType.EXCEL);

    @Resource
    private SplitService splitService;

    @Resource
    private KnowledgeRepository knowledgeRepository;


    @Resource
    @Qualifier("splitImgPoolExecutor")
    private ExecutorService splitImgPoolExecutor;

    @Resource
    private KmsParseTraceEsRepository kmsParseTraceEsRepository;


    @Override
    public boolean isAccess() {
        KnowledgeDTO knowledgeDTO = getKnowledgeDTO();
        return SPLIT_TYPES.contains(knowledgeDTO.getType());
    }

    @Override
    public void process() throws Exception {
        KnowledgeDTO knowledgeDTO = getKnowledgeDTO();
        log.info("开始执行切分任务：name:{}", knowledgeDTO.getName());

        KnowledgeParseResult parseResult = getKnowledgeParseResult();
        KnowledgeParseContext parseInfo = getKnowledgeParseInfo();

        Boolean lightWeightFlag = Optional.ofNullable(parseInfo).map(KnowledgeParseContext::getKnowledgeParseConfig).map(KnowledgeParseConfigContext::getKnowledgeBaseConfig)
                .map(KnowledgeParseConfig::getParserConfig).map(KnowledgeParseConfig.ParserConfig::getLightWeightFlag).orElse(false);
        SplitIndexType splitIndexType = SplitIndexType.TEXT;
        if (Boolean.TRUE.equals(lightWeightFlag)) {
            splitIndexType = SplitIndexType.LIGHTWEIGHT;
        }
        if (Objects.isNull(parseInfo)) {
            log.error("切分任务失败,parseInfo is null, name:{}", knowledgeDTO.getName());
            parseResult.setIndexingStatus(KnowledgeStatus.fail);
            parseResult.setImageIndexingStatus(KnowledgeStatus.fail);
            parseResult.setKnowledgeTip("parseInfo is null");
            return;
        }
        KnowledgeSplitDTO splitDTO = buildKnowledgeSplitDTO(knowledgeDTO, splitIndexType, parseInfo);
        try {
            getParseTrace().setStartTextParseTime(System.currentTimeMillis());
            splitService.split(splitDTO);
            getParseTrace().setEndTextParseTime(System.currentTimeMillis());
            parseResult.setIndexingStatus(KnowledgeStatus.success);
        } catch (BizException e) {
            log.error("切分任务失败 , name:{}", knowledgeDTO.getName(), e);
            parseResult.setIndexingStatus(KnowledgeStatus.fail);
            parseResult.setImageIndexingStatus(KnowledgeStatus.fail);
            parseResult.setKnowledgeTip(e.getUserTip());
            return;
        } catch (Exception e) {
            log.error("切分任务失败, name:{}", knowledgeDTO.getName(), e);
            parseResult.setIndexingStatus(KnowledgeStatus.fail);
            parseResult.setImageIndexingStatus(KnowledgeStatus.fail);
            parseResult.setKnowledgeTip("执行切分任务失败，请稍后重试");
            return;
        }

        if (parseResult.getIndexingStatus() == KnowledgeStatus.success && Boolean.FALSE.equals(lightWeightFlag)) {
            splitImgPoolExecutor.submit(TtlRunnable.get(() -> {
                try {
                    splitImage(knowledgeDTO, parseInfo);
                } catch (Exception e) {
                    log.error("图片类切分失败", e);
                }
            }));
        }

    }

    private Boolean splitImage(KnowledgeDTO knowledgeDTO, KnowledgeParseContext parseInfo) {
        KnowledgeParseResult result = new KnowledgeParseResult();
        ParseTraceLog parseTraceLog = parseInfo.getParseTrace();
        try {
            // 提交图片切分任务
            parseTraceLog.setStartImageParseTime(System.currentTimeMillis());
            log.info("开始提交图片类切片任务，knowledgeCode:{}, name:{}", knowledgeDTO.getCode(), knowledgeDTO.getName());
            KnowledgeSplitDTO imageSplitDTO = buildKnowledgeSplitDTO(knowledgeDTO, SplitIndexType.IMAGE, parseInfo);
            splitService.split(imageSplitDTO);
            log.info("图片类切片任务完成，knowledgeCode:{}, name:{}", knowledgeDTO.getCode(), knowledgeDTO.getName());
            result.setImageIndexingStatus(KnowledgeStatus.success);
        } catch (Exception e) {
            log.info("切分任务失败, name:{}", knowledgeDTO.getName(), e);
            result.setImageIndexingStatus(KnowledgeStatus.fail);
        }
        knowledgeRepository.updateKnowParseResult(knowledgeDTO.getCode(), result);
        // 存储图像解析耗时结果
        parseTraceLog.setEndImageParseTime(System.currentTimeMillis());
        KnowledgeStatus status = result.getImageIndexingStatus();
        parseTraceLog.setImageIndexingStatus(status != null ? status.name() : null);
        kmsParseTraceEsRepository.updateImageParse(parseTraceLog);
        return true;
    }

    private KnowledgeSplitDTO buildKnowledgeSplitDTO(KnowledgeDTO knowledgeDTO, SplitIndexType splitIndexType, KnowledgeParseContext parseInfo) {
        KnowledgeSplitDTO knowledgeSplitDTO = new KnowledgeSplitDTO();
        knowledgeSplitDTO.setSplitIndexType(splitIndexType);
        knowledgeSplitDTO.setKnowledgeCode(knowledgeDTO.getCode());
        knowledgeSplitDTO.setKnowledgeParseConfig(parseInfo.getKnowledgeParseConfig());
        return knowledgeSplitDTO;
    }

}
