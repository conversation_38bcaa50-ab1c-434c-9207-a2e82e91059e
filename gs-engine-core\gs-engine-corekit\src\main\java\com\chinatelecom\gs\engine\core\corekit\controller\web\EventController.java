package com.chinatelecom.gs.engine.core.corekit.controller.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.corekit.domain.request.EventRequest;
import com.chinatelecom.gs.engine.core.corekit.service.EventService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * 事件埋点处理器
 * @USER: pengmc1
 * @DATE: 2025/5/20 14:10
 */
@Slf4j
@Tag(name = "埋点")
@RestController
@RequestMapping({Apis.BASE_PREFIX + Apis.WEB_API + Apis.EVENT, Apis.BASE_PREFIX + Apis.OPENAPI + Apis.EVENT})
public class EventController {

    @Resource
    private EventService eventService;

    @PostMapping("/save")
    @Operation(summary = "页面埋点接口")
    @PlatformRestApi(name = "页面埋点接口", groupName = "埋点")
    @AuditLog(businessType = "埋点", operType = "事件埋点", operDesc = "事件埋点", objId="#request.logId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> save(@Valid @RequestBody EventRequest request){
        return Result.success(eventService.sendEvent(request));
    }
}
