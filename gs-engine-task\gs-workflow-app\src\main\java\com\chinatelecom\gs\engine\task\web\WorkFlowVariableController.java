package com.chinatelecom.gs.engine.task.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.auth.PermissionTypeEnum;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.DataResourceAccess;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelecom.gs.engine.task.sdk.TaskApis;
import com.chinatelecom.gs.engine.task.sdk.vo.variable.*;
import com.chinatelecom.gs.workflow.core.service.WorkFlowVariableAppService;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;


/**
 * <AUTHOR>
 * @description
 * @date 2025/02/11
 */
@Slf4j
@Tag(name = "WorkFlow变量Api")
@RestController
@PermissionTag(code = {MenuConfig.DIALOG_FLOW, MenuConfig.WORKFLOW, KsMenuConfig.WORKFLOW_FLOW})
@PermissionTag(code = MenuConfig.STRATEGY, type = PermissionTypeEnum.MENU)
@RequestMapping({TaskApis.TASK_API + Constants.WEB_PREFIX + "/variable", TaskApis.TASK_API + Constants.API_PREFIX + "/variable"})
public class WorkFlowVariableController {

    private final String WORK_FLOW_TYPE = "workFlow";

    @Autowired
    private DataResourceAccess dataResourceAccess;

    @Resource
    private WorkFlowVariableAppService workFlowVariableAppService;

    @Operation(summary = "全局变量分页列表")
    @PlatformRestApi(name = "全局变量分页列表", groupName = "WorkFlow变量Api")
    @PostMapping("/page")
    @AuditLog(businessType = "WorkFlow变量Api", operType = "全局变量分页列表", operDesc = "全局变量分页列表", objId = "#pageRequest.workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Page<WorkFlowVariableDataDetailVO>> page(@Validated @RequestBody WorkFlowVariableQueryRequest pageRequest) {
        RequestInfo requestInfo = RequestContext.get();
        dataResourceAccess.getResourceData(pageRequest.getWorkFlowId(), requestInfo.getTenantId(), WORK_FLOW_TYPE, requestInfo.getUserId());
        return Result.success(workFlowVariableAppService.page(pageRequest, true));
    }

    @Operation(summary = "添加全局变量")
    @PlatformRestApi(name = "添加全局变量", groupName = "WorkFlow变量Api")
    @PostMapping("/add")
    @AuditLog(businessType = "WorkFlow变量Api", operType = "添加全局变量", operDesc = "添加全局变量", objId = "#addRequest.workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> add(@Validated @RequestBody WorkFlowVariableAddRequest addRequest) {
        RequestInfo requestInfo = RequestContext.get();
        dataResourceAccess.getResourceData(addRequest.getWorkFlowId(), requestInfo.getTenantId(), WORK_FLOW_TYPE, requestInfo.getUserId());
        return workFlowVariableAppService.add(addRequest);
    }

    @Operation(summary = "更新全局变量")
    @PlatformRestApi(name = "更新全局变量", groupName = "WorkFlow变量Api")
    @PostMapping("/update")
    @AuditLog(businessType = "WorkFlow变量Api", operType = "更新全局变量", operDesc = "更新全局变量", objId = "#editRequest.workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> update(@Validated @RequestBody WorkFlowVariableEditRequest editRequest) {
        RequestInfo requestInfo = RequestContext.get();
        dataResourceAccess.getResourceData(editRequest.getWorkFlowId(), requestInfo.getTenantId(), WORK_FLOW_TYPE, requestInfo.getUserId());
        return workFlowVariableAppService.update(editRequest);
    }

    @Operation(summary = "删除全局变量")
    @PlatformRestApi(name = "删除全局变量", groupName = "WorkFlow变量Api")
    @PostMapping("/delete")
    @AuditLog(businessType = "WorkFlow变量Api", operType = "删除全局变量", operDesc = "删除全局变量", objId = "#delRequest.workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> delete(@Validated @RequestBody WorkFlowVariableDelRequest delRequest) {
        RequestInfo requestInfo = RequestContext.get();
        dataResourceAccess.getResourceData(delRequest.getWorkFlowId(), requestInfo.getTenantId(), WORK_FLOW_TYPE, requestInfo.getUserId());
        return workFlowVariableAppService.delete(delRequest);
    }

    @Operation(summary = "查询全局变量详情")
    @PlatformRestApi(name = "查询全局变量详情", groupName = "WorkFlow变量Api")
    @GetMapping("/detail")
    @AuditLog(businessType = "WorkFlow变量Api", operType = "查询全局变量详情", operDesc = "查询全局变量详情", objId = "#variableCode")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<WorkFlowVariableDataDetailVO> detail(@RequestParam("workFlowId") @NotBlank(message = "workflow编码不能为空") String workFlowId,
                                                       @RequestParam("variableCode") @NotBlank(message = "变量编码不能为空") String variableCode) {
        RequestInfo requestInfo = RequestContext.get();
        dataResourceAccess.getResourceData(workFlowId, requestInfo.getTenantId(), WORK_FLOW_TYPE, requestInfo.getUserId());
        return Result.success(workFlowVariableAppService.getDetail(variableCode, true));
    }
}
