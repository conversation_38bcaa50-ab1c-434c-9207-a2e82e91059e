package com.chinatelecom.gs.engine.robot.dialog.execute.service.config;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.alibaba.fastjson2.JSON;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.enums.LogStatusEnum;
import com.chinatelecom.gs.engine.common.enums.LogTypeEnum;
import com.chinatelecom.gs.engine.common.log.track.LogMessage;
import com.chinatelecom.gs.engine.common.log.track.LogMqProducer;
import com.chinatelecom.gs.engine.common.utils.CacheKeyConstant;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.manager.service.BaseConfigAppService;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.service.SafeFenceConfigService;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.robot.manage.common.cache.AgentBasicInfoCache;
import com.chinatelecom.gs.engine.robot.manage.common.enums.AgentConfigEnum;
import com.chinatelecom.gs.engine.robot.manage.common.utils.MarkdownUtils;
import com.chinatelecom.gs.engine.robot.manage.info.domain.response.QueryAgentDialogModeResponse;
import com.chinatelecom.gs.engine.robot.manage.info.enums.AgentTemplateCategoryEnum;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentInfoQueryService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.*;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.domain.ConfigDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.request.AgentConfigValuesRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.response.AgentInfoResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.model.LLMConfig;
import com.chinatelecom.gs.workflow.core.domain.param.BotWorkflowDetailRsp;
import com.chinatelecom.gs.workflow.core.workflow.core.utils.LogUtils;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AgentConfigLoader {

    public final static Cache<String, AgentConfig> configCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(1000)//最大条数
            .build();

    public final static Cache<String, AgentConfig> testCache = Caffeine.newBuilder()
            .expireAfterWrite(2, TimeUnit.SECONDS)
            .maximumSize(1000)//最大条数
            .build();

    @Resource
    private AgentBasicConfigService agentBasicConfigService;

    @Resource
    private ModelServiceClient modelServiceClient;

    @Resource
    private AgentInfoQueryService agentInfoQueryService;

    @Resource
    private AgentBasicInfoCache agentBasicInfoCache;

    @Resource
    private BaseConfigAppService baseConfigAppService;

    @Resource
    private LogMqProducer logMqProducer;

    @Resource
    private SafeFenceConfigService safeFenceConfigService;

    public AgentConfig load(String agentCode, boolean test){
        LocalDateTime startTime = LocalDateTime.now();
        if (CharSequenceUtil.isBlank(agentCode)) {
            throw new BizException("A0016", "agentCode不能为空");
        }
        AgentInfo agentInfo = this.agentBasicInfoCache.getAgentInfo(agentCode, test);
        log.info("【Agent】【Config】读取 {} 状态 {} 配置 {} ", agentCode, test, JSON.toJSONString(agentInfo));

        String cacheKey = CacheKeyConstant.PREFIX_KEY_COMMON + CacheKeyConstant.PREFIX_CONFIG + agentCode + "_" + (test ? agentInfo.getTestVersion() : agentInfo.getProdVersion());
        Cache<String, AgentConfig> cache = test ? testCache : configCache;

        AgentConfig agentConfig = cache.getIfPresent(cacheKey);
        if (Objects.isNull(agentConfig)) {
            agentConfig = this.getAgentConfig(agentInfo, test);
            cache.put(cacheKey, agentConfig);
        }

//        AgentConfig agentConfig = getAgentConfig(agentInfo, test);
        PropertyFilter filter = (object, name, value) -> !name.equals("apikey") && !name.equals("modelSecret") && !name.equals("modelApi");
        log.info("【Agent】【Config】读取 {} 状态 {} 配置 {}", agentCode, test, JSON.toJSONString(agentConfig, filter));
        try{
            LogMessage logMessage = LogUtils.buildCommonLog(LogTypeEnum.AGENT_CONFIG,agentCode,agentConfig, LogStatusEnum.SUCCESS,startTime, LocalDateTime.now(),"");
            logMessage.setName("获取机器人配置");
            logMqProducer.asyncSendLogMessage(logMessage);
        }catch (Exception e){
            log.error("埋点发生异常！", e);
        }
        return agentConfig;
    }

    public AgentInfo queryAgentInfo(String agentCode, boolean test){
        AgentInfo baseAgentInfo = null;
        try{
            log.info("查询agent基本信息，agentCode: {}, 是否测试：{}, app_code：{}", agentCode, test, RequestContext.getAndCheck() != null ? RequestContext.getAndCheck().getAppCode() : "null");
            AgentInfoResponse response = this.agentInfoQueryService.queryAgentInfo(agentCode, test);
            log.info("查询agent基本信息返回：{}", JSON.toJSONString(response));
            if(response != null){
                baseAgentInfo = new AgentInfo();
                baseAgentInfo.setAgentCode(response.getAgentCode());
                baseAgentInfo.setAgentName(response.getAgentName());
                baseAgentInfo.setTenantId(response.getTenantId());
                baseAgentInfo.setTenantName(response.getTenantName());
                baseAgentInfo.setDefaultRagAgent(response.isDefaultRagAgent());
                baseAgentInfo.setCreateId(response.getCreateId());
                baseAgentInfo.setCreateName(response.getCreateName());
                baseAgentInfo.setBotType(response.getBotType());
                Long robotEditVersionRes = this.agentInfoQueryService.queryAgentEditVersion(agentCode);
                if(Objects.nonNull(robotEditVersionRes)){
                    baseAgentInfo.setTestVersion(robotEditVersionRes);
                }
                Long robotRunningVersionRes = this.agentInfoQueryService.queryAgentRunningVersion(agentCode);
                if(Objects.nonNull(robotRunningVersionRes)){
                    baseAgentInfo.setProdVersion(robotRunningVersionRes);
                }
            }
            return baseAgentInfo;
        }catch(Exception e){
            log.error("获取agent基本信息发生错误！", e);
        }
        return baseAgentInfo;
    }

    /**
     * 单测用
     * @param agentInfo
     * @param test
     * @return
     */
    public AgentConfig processAgentConfig(AgentInfo agentInfo, boolean test) {
        return this.getAgentConfig(agentInfo, test);
    }

    private AgentConfig getAgentConfig(AgentInfo agentInfo, boolean test){
        String agentCode = agentInfo.getAgentCode();
        AgentConfig agentConfig = new AgentConfig();
        agentConfig.setAgentCode(agentCode);

        //机器人基础信息
        agentConfig.setTenantId(agentInfo.getTenantId());
        agentConfig.setTenantName(agentInfo.getTenantName());
        agentConfig.setAgentName(agentInfo.getAgentName());
        agentConfig.setProdVersion(agentInfo.getProdVersion());
        agentConfig.setTestVersion(agentInfo.getTestVersion());
        agentConfig.setDefaultRagAgent(agentInfo.isDefaultRagAgent());
        agentConfig.setBotType(agentInfo.getBotType());
        agentConfig.setTemplateCategoryId(agentInfo.getTemplateCategoryId());
        RequestInfo requestInfo = RequestContext.get();
        if (Objects.isNull(requestInfo.getExtraParam("openapi"))) {
            requestInfo.putExtraParam("createId", agentInfo.getCreateId());
        } else {
            requestInfo.putExtraParam("createId", requestInfo.getUserId());
        }
        RequestContext.set(requestInfo);
        Long version = test ? agentConfig.getTestVersion() : agentConfig.getProdVersion();

        List<String> configKeys = getAgentConfigKeys();
        boolean isSystemAgent = AgentTemplateCategoryEnum.SYSTEM.getCode().equals(agentInfo.getTemplateCategoryId());
        log.info("agentCode: {}, configKeys: {}, version: {} isSystemAgent:{}", agentCode, configKeys, version, isSystemAgent);
        List<ConfigDetailVO> agentConfigValues = this.agentBasicConfigService.batchQueryAgentConfigValues(
                new AgentConfigValuesRequest(agentCode, configKeys, version));
        Map<String, String> configValueMap = agentConfigValues.stream()
                .collect(Collectors.toMap(ConfigDetailVO::getConfigKey, ConfigDetailVO::getConfigValue, (x1, x2)->x1));

        agentConfig.setBasicConfig(
                getConfig(configValueMap, AgentConfigEnum.AGENT_BASIC_INFO_CONFIG.getCode(), AgentBasicConfig.class));
        agentConfig.setKmsConfig(
                getConfig(configValueMap, AgentConfigEnum.AGENT_KMS_CONFIG.getCode(), KmsConfig.class));
        agentConfig.setDialogConfig(
                getConfig(configValueMap, AgentConfigEnum.AGENT_DIALOG_CONFIG.getCode(), DialogConfig.class));
        if(Objects.nonNull(agentConfig.getDialogConfig()) && StringUtils.isNotBlank(agentConfig.getDialogConfig().getNoAnswerScript())){
            agentConfig.getDialogConfig().setNoAnswerMarkdownScript(MarkdownUtils.htmlToMarkdown(agentConfig.getDialogConfig().getNoAnswerScript()));
        }

        ModelConfig modelConfig = getConfig(configValueMap, AgentConfigEnum.AGENT_MODEL.getCode(), ModelConfig.class);
        if(Objects.nonNull(modelConfig)){
            modelConfig.setLlmConfig(this.getLLMDetailByModelCode(modelConfig));
            agentConfig.setModelConfig(modelConfig);
        }
        //增加多模态大模型配置
        ModelConfig multiModelConfig = getConfig(configValueMap, AgentConfigEnum.AGENT_MULTI_MODEL.getCode(), ModelConfig.class);
        if(Objects.nonNull(multiModelConfig)){
            multiModelConfig.setLlmConfig(this.getMultiLLMDetailByModelCode(multiModelConfig));
            agentConfig.setMultiModelConfig(multiModelConfig);
        }
        agentConfig.setGlobalPolicyConfig(
                getConfig(configValueMap, AgentConfigEnum.AGENT_GLOBAL_POLICY.getCode(), GlobalPolicyConfig.class));
        //增加安全围栏配置
        Boolean safeFenceSwitch = safeFenceConfigService.getSafeFenceSwitch();
        agentConfig.setSafeFenceSwitch(safeFenceSwitch);
        agentConfig.setSensitiveSwitch(safeFenceConfigService.getSensitiveSwitch());

        agentConfig.setSafeFenceConfig(
                getConfig(configValueMap, AgentConfigEnum.SAFE_FENCE_CONFIG.getCode(), SafeFenceConfig.class));
        agentConfig.setDialogModeConfig(this.getAgentDialogModeConfig(agentCode, test));

        PropertyFilter filter = (object, name, value) -> !name.equals("apikey") && !name.equals("modelSecret") && !name.equals("modelApi");
        log.info("【ConfigCenter】 读取 code {} 状态 {} 的配置为 {}", agentInfo.getAgentCode(), test,
                JSON.toJSONString(agentConfig, filter));
        return agentConfig;
    }


    private DialogModeConfig getAgentDialogModeConfig(String agentCode, boolean test) {
        QueryAgentDialogModeResponse queryAgentDialogModeResponse = this.agentBasicConfigService.queryDialogMode(agentCode, test);
        if (Objects.isNull(queryAgentDialogModeResponse)) {
            throw new BizException("botCode对应的应答模式配置不存在");
        }

        DialogModeConfig dialogModeConfig = new DialogModeConfig();
        dialogModeConfig.setReactCount(queryAgentDialogModeResponse.getReact());
        dialogModeConfig.setType(queryAgentDialogModeResponse.getType());
        dialogModeConfig.setUrl(queryAgentDialogModeResponse.getUrl());
        dialogModeConfig.setUserStrategyFlowId(Optional.ofNullable(queryAgentDialogModeResponse.getWorkflowDetailRsp()).map(BotWorkflowDetailRsp::getWorkflowId).orElse(null));
        return dialogModeConfig;
    }

    private List<String> getAgentConfigKeys(){
        List<String> configKeys = new ArrayList<>();
        configKeys.add(AgentConfigEnum.AGENT_KMS_CONFIG.getCode());
        configKeys.add(AgentConfigEnum.AGENT_DIALOG_CONFIG.getCode());
        configKeys.add(AgentConfigEnum.AGENT_MODEL.getCode());
        configKeys.add(AgentConfigEnum.AGENT_MULTI_MODEL.getCode());
        configKeys.add(AgentConfigEnum.AGENT_GLOBAL_POLICY.getCode());
        configKeys.add(AgentConfigEnum.SAFE_FENCE_CONFIG.getCode());
        return configKeys;
    }

    private <T> T getConfig(Map<String, String> configs, String key, Class<T> clazz){
        if(!configs.containsKey(key)){
            return null;
        }

        String value = configs.get(key);
        return JSON.parseObject(value, clazz);
    }

    private LLMConfig getLLMDetailByModelCode(ModelConfig modelConfig){
        ModelPageListParam modelPageListParam = modelServiceClient.queryByModelCode(modelConfig.getModelCode());
//        Result<AgentModelResponse> agentModelResponseResult = this.agentModelAppApi.queryAgentAndModelConfig(
//                modelConfig.getModelCode());
//
//        AgentModelResponse agentModelResponse = agentModelResponseResult.getData();
        LLMConfig llmConfig = new LLMConfig();
        llmConfig.setModelApi(modelPageListParam.getApiKey());
        llmConfig.setModelName(modelPageListParam.getModelCallName());
        llmConfig.setModelProvider(modelPageListParam.getModelProvider());
        llmConfig.setExternalModelConfig(modelPageListParam.getExternalModelConfig());
        llmConfig.setModelUrl(modelPageListParam.getExternalModelUrl());
        llmConfig.setModelSecret(modelPageListParam.getModelSecret());
        if(modelConfig.getModelMode().equalsIgnoreCase("PRECISION_MODE")){
            llmConfig.setTemperature(0.1);
        }else if(modelConfig.getModelMode().equalsIgnoreCase("BALANCE_MODE")){
            llmConfig.setTemperature(1);
        }else{
            llmConfig.setTemperature(
                    Objects.nonNull(modelConfig.getModelRandomness()) ? modelConfig.getModelRandomness() : 0.3);
        }
        llmConfig.setMultiple(Optional.ofNullable(modelConfig.getMemoryCount()).orElse(1));
        return llmConfig;
    }

    private LLMConfig getMultiLLMDetailByModelCode(ModelConfig modelConfig){
        ModelPageListParam modelPageListParam = modelServiceClient.queryByModelCode(modelConfig.getModelCode());
        log.info("查询多模态大模型modelCode: {}, 结果{}", modelConfig.getModelCode(), JsonUtils.toJsonString(modelPageListParam));
//        Result<AgentModelResponse> agentModelResponseResult = this.agentModelAppApi.queryAgentAndModelConfig(
//                modelConfig.getModelCode());
//
//        AgentModelResponse agentModelResponse = agentModelResponseResult.getData();
        LLMConfig llmConfig = new LLMConfig();
        llmConfig.setModelName(modelPageListParam.getModelCallName());
        llmConfig.setModelUrl(modelPageListParam.getExternalModelUrl());
        llmConfig.setExternalModelConfig(modelPageListParam.getExternalModelConfig());
        return llmConfig;
    }

    private AgentBasicConfig mock(String agentCode){
        AgentBasicConfig basicConfig = new AgentBasicConfig();
        basicConfig.setAgentCode(agentCode);
        basicConfig.setAgentName("问答助手");
        basicConfig.setAgentReply("你是一个问答助手");
        return basicConfig;
    }
}
