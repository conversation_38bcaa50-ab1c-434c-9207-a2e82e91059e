package com.chinatelecom.gs.engine.robot.manage.info.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.PageDTO;
import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.DataResourceAccess;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.robot.manage.info.dao.repository.AgentBindApiParamRepository;
import com.chinatelecom.gs.engine.robot.manage.info.dao.repository.AgentVersionInfoRepository;
import com.chinatelecom.gs.engine.robot.manage.info.dao.repository.PluginBindRepository;
import com.chinatelecom.gs.engine.robot.manage.info.dao.service.AgentBasicInfoService;
import com.chinatelecom.gs.engine.robot.manage.info.dao.service.AgentBindApiParamService;
import com.chinatelecom.gs.engine.robot.manage.info.domain.dto.AgentBindApiParamDTO;
import com.chinatelecom.gs.engine.robot.manage.info.domain.dto.PluginBindInfo;
import com.chinatelecom.gs.engine.robot.manage.info.domain.po.AgentBasicInfoPO;
import com.chinatelecom.gs.engine.robot.manage.info.domain.po.AgentBindApiParamPO;
import com.chinatelecom.gs.engine.robot.manage.info.domain.po.PluginBindPO;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.PluginBindRequest;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.UpdatePluginDefaultParamsRequest;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.manage.info.service.PluginBindConfigService;
import com.chinatelecom.gs.engine.robot.manage.info.service.PluginBindService;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.AgentStatusEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.PluginStatusEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.model.PluginDefaultParamsRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.PluginBindPageRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.QueryPluginParam;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.*;
import com.chinatelecom.gs.plugin.hub.application.service.PluginApiConfigService;
import com.chinatelecom.gs.plugin.hub.application.service.PluginMallInfoConfigService;
import com.chinatelecom.gs.plugin.hub.application.service.PluginMetaConfigService;
import com.chinatelecom.gs.plugin.hub.infra.dto.PluginMallInfoDTO;
import com.chinatelecom.gs.plugin.hub.infra.dto.PluginMetaDTO;
import com.chinatelecom.gs.plugin.hub.infra.enums.PluginServiceTypeEnum;
import com.chinatelecom.gs.plugin.hub.infra.enums.ReqRspEnum;
import com.chinatelecom.gs.plugin.hub.infra.repository.PluginApiRepository;
import com.chinatelecom.gs.plugin.hub.infra.repository.PluginMallInfoRepository;
import com.chinatelecom.gs.plugin.hub.infra.repository.PluginMetaRepository;
import com.chinatelecom.gs.plugin.hub.infra.repository.PluginVersionInfoRepository;
import com.chinatelecom.gs.plugin.hub.infra.service.PluginMetaService;
import com.chinatelecom.gs.plugin.hub.model.PluginComListRequest;
import com.chinatelecom.gs.plugin.hub.model.PluginComListResponse;
import com.chinatelecom.gs.plugin.hub.model.QueryPluginMallParam;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PluginBindConfigServiceImpl implements PluginBindConfigService {

    private static final String PLUGIN_TYPE = "pluginType";

    @Resource
    private PluginBindRepository pluginBindRepository;

    @Resource
    private PluginApiConfigService pluginApiConfigService;

    @Resource
    private PluginMetaService pluginMetaService;

    @Resource
    private PluginApiRepository pluginApiRepository;

    @Autowired
    private PluginBindService pluginBindService;

    @Autowired
    private PluginMetaConfigService pluginMetaConfigService;

    @Autowired
    private PluginMallInfoConfigService pluginMallInfoConfigService;

    @Resource
    private PluginMallInfoRepository pluginMallInfoRepository;

    @Autowired
    private DataResourceAccess dataResourceAccess;

    @Autowired
    private AgentVersionInfoRepository agentVersionInfoRepository;

    @Autowired
    private AgentBindApiParamRepository agentBindApiParamRepository;

    @Autowired
    private AgentBindApiParamService agentBindApiParamService;

    @Autowired
    private PluginBindConfigService pluginBindConfigService;

    @Autowired
    private AgentBasicInfoService agentBasicInfoService;

    @Autowired
    private AgentBasicConfigService agentBasicConfigService;

    @Autowired
    private PluginVersionInfoRepository pluginVersionInfoRepository;

    @Resource
    private ExecutorService commonExecutorService;

    @Autowired
    private PluginMetaRepository pluginMetaRepository;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    @Override
    public Boolean bind(PluginBindRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);

        //查询插件下已上线的工具
        PlatformUser user = SsoUtil.get();
        String pluginId = request.getPluginId();
        // 非公共插件，需要校验用户是否有权限访问该插件数据
        if (Boolean.FALSE.equals(request.getShelved())) {
            dataResourceAccess.getResourceData(pluginId, user.getCorpCode(), PLUGIN_TYPE, user.getUserId());
        }
        PluginMetaDTO pluginMetaDTO = pluginMetaConfigService.queryOnlinePlugin(pluginId);
        List<PluginApiInfo> pluginApiDTOS = pluginApiConfigService.queryOnlineApiByPluginId(pluginId);

        Long editVersion = agentVersionInfoRepository.getEditVersion(request.getAgentCode());

        if (CollectionUtils.isEmpty(pluginApiDTOS)) {
            throw new BizException("AB003", "插件id下没有任何上线工具");
        }

        if (PluginServiceTypeEnum.MCP.getType().equals(pluginMetaDTO.getServiceType())) {
            //mcp插件级绑定，直接绑定所有工具
            request.setApiId(null);
        }
        List<PluginBindInfo> pluginBindInfoList = new ArrayList<>();
        for (PluginApiInfo pluginApiDTO : pluginApiDTOS) {
            //工具id为空则绑定插件下的所有工具
            if (StringUtils.isEmpty(request.getApiId()) || pluginApiDTO.getApiId().equals(request.getApiId())) {
                PluginBindInfo pluginBindInfo = new PluginBindInfo();
                BeanUtil.copyProperties(request, pluginBindInfo);
                pluginBindInfo.setApiId(pluginApiDTO.getApiId());
                pluginBindInfo.setApiName(pluginApiDTO.getApiName());
                pluginBindInfo.setPluginName(pluginMetaDTO.getPluginName());
                pluginBindInfo.setAgentVersion(editVersion);
                pluginBindInfoList.add(pluginBindInfo);
            }
        }
        if (pluginBindInfoList.isEmpty()) {
            throw new BizException("AB018", "没有找到指定的工具");
        }
        pluginBindInfoList.forEach(o -> savePluginBind(o, true));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unbind(PluginBindRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);

        Long editVersion = agentVersionInfoRepository.getEditVersion(request.getAgentCode());
        PluginMetaDTO pluginMetaDTO = pluginMetaConfigService.queryOnlinePlugin(request.getPluginId());
        if (!PluginServiceTypeEnum.MCP.getType().equals(pluginMetaDTO.getServiceType())) {
            //查询插件下已上线的工具
            PluginBindInfo pluginBindInfo = new PluginBindInfo();
            pluginBindInfo.setPluginId(request.getPluginId());
            pluginBindInfo.setApiId(request.getApiId());
            pluginBindInfo.setAgentCode(request.getAgentCode());
            pluginBindInfo.setAgentVersion(editVersion);
            savePluginBind(pluginBindInfo, false);
            return Boolean.TRUE;
        } else {
            // 查询绑定表
            LambdaQueryWrapper<PluginBindPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PluginBindPO::getAgentCode, request.getAgentCode());
            queryWrapper.eq(PluginBindPO::getAgentVersion, editVersion);
            queryWrapper.eq(PluginBindPO::getPluginId, request.getPluginId());
            List<PluginBindPO> pluginBindList = pluginBindService.list(queryWrapper);
            if (CollectionUtils.isEmpty(pluginBindList)) {
                throw new BizException("AB003", "插件id下没有任何上线工具");
            }
            List<PluginBindInfo> pluginBindInfoList = new ArrayList<>();
            for (PluginBindPO pluginBindPO : pluginBindList) {
                //解绑插件下的所有工具
                PluginBindInfo pluginBindInfo = new PluginBindInfo();
                pluginBindInfo.setPluginId(request.getPluginId());
                pluginBindInfo.setApiId(pluginBindPO.getApiId());
                pluginBindInfo.setAgentCode(request.getAgentCode());
                pluginBindInfo.setAgentVersion(editVersion);
                pluginBindInfoList.add(pluginBindInfo);
            }
            if (pluginBindInfoList.isEmpty()) {
                throw new BizException("AB018", "没有找到指定的工具");
            }
            pluginBindInfoList.forEach(o -> savePluginBind(o, false));
            return Boolean.TRUE;
        }
    }

    @Override
    public Page<PluginBindPageResponse> bindPageMerge(PluginBindPageRequest request) {
        // 检查是否存在已经下架的插件
        Page<PluginBindPageResponse> convert = bindPage(request);
        // 聚合同一插件下的信息
        List<PluginBindPageResponse> records = convert.getRecords();
        List<PluginBindPageResponse> plugins = new ArrayList<>();
        List<String> pluginIds = new ArrayList<>();
        if (records != null && records.size() > 0) {
            records.forEach(bindPO->{
                if (PluginServiceTypeEnum.MCP.getType().equals(bindPO.getServiceType())) {
                    if (!pluginIds.contains(bindPO.getPluginId())) {
                        pluginIds.add(bindPO.getPluginId());
                        //新建对象，只保留插件级信息
                        PluginBindPageResponse pluginInfo = new PluginBindPageResponse();
                        pluginInfo.setPluginId(bindPO.getPluginId());
                        pluginInfo.setPluginName(bindPO.getPluginName());
                        pluginInfo.setPluginDesc(bindPO.getPluginDesc());
                        pluginInfo.setPluginIcon(bindPO.getPluginIcon());
                        pluginInfo.setStatus(bindPO.getStatus());
                        pluginInfo.setServiceType(bindPO.getServiceType());
                        pluginInfo.setServiceSubType(bindPO.getServiceSubType());
                        pluginInfo.setVersion(bindPO.getVersion());
                        pluginInfo.setCreateApiType(bindPO.getCreateApiType());
                        plugins.add(pluginInfo);
                    }
                } else {
                    plugins.add(bindPO);
                }
            });
        }
        // 重新构建分页对象结果
        PageImpl<PluginBindPageResponse> pluginBindPageRes = new PageImpl<>();
        pluginBindPageRes.setCurrent(request.getPageNo());
        pluginBindPageRes.setSize(request.getPageSize());
        pluginBindPageRes.setRecords(plugins);
        pluginBindPageRes.setTotal(plugins.size());
        Double ceil = Double.valueOf(Math.ceil((double)plugins.size() / request.getPageSize()));
        pluginBindPageRes.setPages(ceil.longValue());
        return pluginBindPageRes;
    }

    @Override
    public Page<PluginBindPageResponse> bindPage(PluginBindPageRequest request) {
        Long version;
        if (Boolean.TRUE.equals(request.getTest())) {
            version = agentVersionInfoRepository.getEditVersion(request.getAgentCode());
        } else {
            version = agentVersionInfoRepository.getPublishVersion(request.getAgentCode());
        }

        // 先查询出所有关联的插件, 过滤掉其中已经下架的插件
        LambdaQueryWrapper<PluginBindPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PluginBindPO::getAgentCode, request.getAgentCode());
        queryWrapper.eq(PluginBindPO::getAgentVersion, version);
        if (StringUtils.isNotEmpty(request.getName())) {
            queryWrapper.and(wrapper -> wrapper.like(PluginBindPO::getPluginName, request.getName())
                    .or()
                    .like(PluginBindPO::getApiName, request.getName()));
        }
        List<PluginBindPO> pluginBindList = pluginBindService.list(queryWrapper);
        List<String> shelvedPluginList = pluginBindList.stream().filter(p -> Boolean.TRUE.equals(p.getShelved())).map(PluginBindPO::getPluginId).collect(Collectors.toList());
        List<String> unshelvedPluginList = pluginBindList.stream().filter(p -> !Boolean.TRUE.equals(p.getShelved())).map(PluginBindPO::getPluginId).collect(Collectors.toList());
        // 查询市场中的插件
        List<String> existPluginList = pluginMallInfoConfigService.queryshelvedPluginById(shelvedPluginList);

        existPluginList.addAll(unshelvedPluginList);
        // 只查询市场存在的和私有的
        if (CollectionUtils.isNotEmpty(existPluginList)) {
            queryWrapper.in(PluginBindPO::getPluginId, existPluginList);
        } else {
            return new PageImpl<>();
        }

        IPage<PluginBindPO> pluginBindPOPage = pluginBindService.page(new PageDTO<>(request.getPageNo(), request.getPageSize()), queryWrapper);
        PageImpl<PluginBindPageResponse> page = new PageImpl<>();
        page.setCurrent(pluginBindPOPage.getCurrent());
        page.setTotal(pluginBindPOPage.getTotal());
        page.setSize(pluginBindPOPage.getSize());
        page.setPages(pluginBindPOPage.getPages());
        List<PluginBindPO> records = pluginBindPOPage.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<PluginBindPageResponse> convertList = Lists.newArrayList();
            CompletableFuture.allOf(records.stream().map(metaInfo -> CompletableFuture.runAsync(() -> {
                PluginBindPageResponse pluginComListResponse = covertToBindResponse(metaInfo, request.getTest());
                convertList.add(pluginComListResponse);
            }, commonExecutorService)).toArray(CompletableFuture[]::new)).join();
            page.setRecords(convertList);
        }
        return page;
    }

    @Override
    public List<PluginBindPageResponse> queryBindPluginForExecute(String agentCode, boolean test) {
        Long version;
        if (Boolean.TRUE.equals(test)) {
            version = agentVersionInfoRepository.getEditVersion(agentCode);
        } else {
            version = agentVersionInfoRepository.getPublishVersion(agentCode);
        }

        // 先查询出所有关联的插件, 过滤掉其中已经下架的插件
        LambdaQueryWrapper<PluginBindPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PluginBindPO::getAgentCode, agentCode);
        queryWrapper.eq(PluginBindPO::getAgentVersion, version);
        List<PluginBindPO> pluginBindList = pluginBindService.list(queryWrapper);
        List<String> shelvedPluginList = pluginBindList.stream().filter(p -> Boolean.TRUE.equals(p.getShelved())).map(PluginBindPO::getPluginId).collect(Collectors.toList());
        List<String> unshelvedPluginList = pluginBindList.stream().filter(p -> !Boolean.TRUE.equals(p.getShelved())).map(PluginBindPO::getPluginId).collect(Collectors.toList());
        // 查询市场中的插件
        List<String> existPluginList = pluginMallInfoConfigService.queryshelvedPluginById(shelvedPluginList);

        existPluginList.addAll(unshelvedPluginList);
        // 只查询市场存在的和私有的
        if (CollectionUtils.isNotEmpty(existPluginList)) {
            queryWrapper.in(PluginBindPO::getPluginId, existPluginList);
        } else {
            return Lists.newArrayList();
        }

        List<PluginBindPO> records = pluginBindService.list(queryWrapper);
        List<PluginBindPageResponse> convertList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(records)) {
            CompletableFuture.allOf(records.stream().map(metaInfo -> CompletableFuture.runAsync(() -> {
                PluginBindPageResponse pluginComListResponse = getPluginBindResponse(metaInfo, test);
                convertList.add(pluginComListResponse);
            }, commonExecutorService)).toArray(CompletableFuture[]::new)).join();
        }
        return convertList;
    }

    @Override
    public Page<PluginComListResponse> comList(PluginComListRequest request) {
        Page<PluginMetaInfo> pluginMetaInfoPage = null;
        if (request.isMyPlugin()) {
            //查询我的工具列表
            QueryPluginParam queryPluginParam = buildPluginParam(request);
            pluginMetaInfoPage = pluginMetaConfigService.queryPluginList(queryPluginParam);
        } else {
            //查询企业工具箱
            QueryPluginMallParam queryPluginParam = buildPluginMallParam(request);
            pluginMetaInfoPage = pluginMallInfoConfigService.queryPluginListByCondition(queryPluginParam);
        }
        PageImpl<PluginComListResponse> page = new PageImpl<>();
        page.setCurrent(pluginMetaInfoPage.getCurrent());
        page.setTotal(pluginMetaInfoPage.getTotal());
        page.setSize(pluginMetaInfoPage.getSize());
        page.setPages(pluginMetaInfoPage.getPages());
        List<PluginMetaInfo> record = pluginMetaInfoPage.getRecords();
        if (CollectionUtils.isNotEmpty(record)) {
            List<PluginComListResponse> convertList = Lists.newArrayList();
            CompletableFuture.allOf(record.stream().map(metaInfo -> CompletableFuture.runAsync(() -> {
                PluginComListResponse pluginComListResponse = convertToComListResponse(metaInfo);
                convertList.add(pluginComListResponse);
            }, commonExecutorService)).toArray(CompletableFuture[]::new)).join();
            page.setRecords(convertList);
        }
        return page;
    }

    @Override
    public Boolean publishAgent(String agentCode, Long version) {
        // 复制当前版本的关联关系， 并删除以往其他版本的关联关系
        LambdaQueryWrapper<PluginBindPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PluginBindPO::getAgentCode, agentCode);
        queryWrapper.eq(PluginBindPO::getAgentVersion, version);
        queryWrapper.eq(PluginBindPO::getYn, 0);
        List<PluginBindPO> list = pluginBindService.list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            // 复制当前版本的关联关系
            List<PluginBindPO> pluginBindPOList = list.stream().map(bind -> {
                PluginBindPO pluginBindPO = new PluginBindPO();
                BeanUtil.copyProperties(bind, pluginBindPO);
                pluginBindPO.setId(null);
                pluginBindPO.setAgentVersion(version + 1);
                return pluginBindPO;
            }).collect(Collectors.toList());
            pluginBindService.saveBatch(pluginBindPOList);
        }

        // 复制当前版本关联的工具参数
        LambdaQueryWrapper<AgentBindApiParamPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AgentBindApiParamPO::getAgentCode, agentCode);
        wrapper.eq(AgentBindApiParamPO::getAgentVersion, version);
        wrapper.eq(AgentBindApiParamPO::getYn, 0);
        List<AgentBindApiParamPO> list1 = agentBindApiParamService.list(wrapper);
        if (!CollectionUtils.isEmpty(list1)) {
            // 复制当前版本的关联关系
            List<AgentBindApiParamPO> agentBindApiParamPOList = list1.stream().map(bind -> {
                AgentBindApiParamPO agentBindApiParamPO = new AgentBindApiParamPO();
                BeanUtil.copyProperties(bind, agentBindApiParamPO);
                agentBindApiParamPO.setId(null);
                agentBindApiParamPO.setAgentVersion(version + 1);
                return agentBindApiParamPO;
            }).collect(Collectors.toList());
            agentBindApiParamService.saveBatch(agentBindApiParamPOList);
        }

        return Boolean.TRUE;
    }

    private static @NotNull QueryPluginMallParam buildPluginMallParam(PluginComListRequest request) {
        QueryPluginMallParam queryPluginParam = new QueryPluginMallParam();
        queryPluginParam.setName(request.getName());
        queryPluginParam.setPageNo(request.getPageNo());
        queryPluginParam.setPageSize(request.getPageSize());
        queryPluginParam.setSource(request.getSource());
        return queryPluginParam;
    }

    public PluginComListResponse convertToComListResponse(PluginMetaInfo pluginMetaInfo) {
        PluginComListResponse pluginComListResponse = new PluginComListResponse();
        List<PluginApiInfo> pluginApiInfos = pluginApiConfigService.queryOnlineApiByPluginId(pluginMetaInfo.getPluginId());
        BeanUtil.copyProperties(pluginMetaInfo, pluginComListResponse);
        pluginComListResponse.setApiNum(pluginApiInfos.size());
        pluginComListResponse.setApiList(pluginApiInfos.stream().map(this::covertToApiBindInfo).collect(Collectors.toList()));
        return pluginComListResponse;
    }

    private PluginApiBindInfo covertToApiBindInfo(PluginApiInfo pluginApiInfo) {
        PluginApiBindInfo pluginApiBindInfo = new PluginApiBindInfo();
        BeanUtil.copyProperties(pluginApiInfo, pluginApiBindInfo);
        LambdaQueryWrapper<PluginBindPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PluginBindPO::getApiId, pluginApiInfo.getApiId());
        PluginBindPO one = pluginBindService.getOne(queryWrapper, false);
        pluginApiBindInfo.setBind(Objects.nonNull(one));
        return pluginApiBindInfo;
    }

    private static @NotNull QueryPluginParam buildPluginParam(PluginComListRequest request) {
        QueryPluginParam queryPluginParam = new QueryPluginParam();
        queryPluginParam.setPluginName(request.getName());
        //已发布
        List<Integer> status = new ArrayList<>();
        status.add(PluginStatusEnum.PUBLISHED.getCode());
        queryPluginParam.setStatus(status);
        queryPluginParam.setPageNo(request.getPageNo());
        queryPluginParam.setPageSize(request.getPageSize());
        // 不启用的数据不显示
        queryPluginParam.setFilterUnableApi(Boolean.TRUE);
        queryPluginParam.setNeedQueryPublish(Boolean.TRUE);
        return queryPluginParam;
    }

    private PluginBindPageResponse covertToBindResponse(PluginBindPO pluginBindPO, Boolean test) {
        PluginMetaDTO pluginMetaDTO = pluginMetaConfigService.queryOnlinePlugin(pluginBindPO.getPluginId());
        PluginMallInfoDTO pluginMallInfoDTO = pluginMallInfoRepository.queryByPluginId(pluginBindPO.getPluginId());

        if (Objects.nonNull(pluginMallInfoDTO) && Boolean.TRUE.equals(pluginMallInfoDTO.getPluginSource())
                && !RequestContext.getUserId().equals(pluginMetaDTO.getCreateId()) && !(RequestContext.get().getIsAdmin()
                && superTenant.equals(RequestContext.getTenantId()))) {
            //公开市场插件,并且不是自己创建的,也不是超级租户管理员账号,不能编辑
            pluginMetaDTO.setCreateApiType(2);
        }
        if (pluginMetaDTO == null) {
            throw new BizException("AB001", "插件不存在");
        }
        String apiId = pluginBindPO.getApiId();
        if (PluginServiceTypeEnum.MCP.getType().equals(pluginMetaDTO.getServiceType())) {
            PluginApiInfo pluginApiDetail = pluginApiConfigService.getPluginApiDetail(pluginBindPO.getPluginId(), apiId, true);
            if (pluginApiDetail == null) {
                throw new BizException("AB002", "工具不存在");
            }
            PluginDefaultParamsRequest request = new PluginDefaultParamsRequest();
            request.setAgentCode(pluginBindPO.getAgentCode());
            request.setApiId(apiId);
            request.setTest(test);
            AgentBindApiParamsResponse agentBindApiParamsResponse = pluginBindConfigService.queryBotDefaultParams(request);

            PluginBindPageResponse pluginBindPageResponse = new PluginBindPageResponse();
            BeanUtil.copyProperties(pluginMetaDTO, pluginBindPageResponse);
            pluginBindPageResponse.setApiName(pluginApiDetail.getApiName());
            pluginBindPageResponse.setApiId(pluginApiDetail.getApiId());
            pluginBindPageResponse.setApiDesc(pluginApiDetail.getApiDesc());
            pluginBindPageResponse.setCreateTime(pluginApiDetail.getCreateTime());
            pluginBindPageResponse.setRequestParams(agentBindApiParamsResponse.getRequestParams());
            pluginBindPageResponse.setEnabled(pluginApiDetail.isEnabled());
            return pluginBindPageResponse;
        } else {
            // 先检查是否绑定参数
            PluginDefaultParamsRequest request = new PluginDefaultParamsRequest();
            request.setAgentCode(pluginBindPO.getAgentCode());
            request.setApiId(apiId);
            request.setTest(test);
            AgentBindApiParamsResponse agentBindApiParamsResponse = pluginBindConfigService.queryBotDefaultParams(request);

            PluginApiInfo pluginApiDetail = pluginApiConfigService.getPluginApiDetail(pluginBindPO.getPluginId(), apiId, true);

            if (Objects.isNull(pluginApiDetail)) {
                throw new BizException("AB019", "查询工具错误");
            }

            PluginBindPageResponse pluginBindPageResponse = new PluginBindPageResponse();
            BeanUtil.copyProperties(pluginMetaDTO, pluginBindPageResponse);
            pluginBindPageResponse.setApiName(pluginApiDetail.getApiName());
            pluginBindPageResponse.setApiId(pluginApiDetail.getApiId());
            pluginBindPageResponse.setApiDesc(pluginApiDetail.getApiDesc());
            pluginBindPageResponse.setCreateTime(pluginApiDetail.getCreateTime());
            pluginBindPageResponse.setRequestParams(agentBindApiParamsResponse.getRequestParams());
            pluginBindPageResponse.setResponseParams(agentBindApiParamsResponse.getResponseParams());
            pluginBindPageResponse.setEnabled(pluginApiDetail.isEnabled());
            return pluginBindPageResponse;
        }
    }

    private PluginBindPageResponse getPluginBindResponse(PluginBindPO pluginBindPO, Boolean test) {
        Long version = pluginVersionInfoRepository.getPublishVersion(pluginBindPO.getPluginId());
        PluginMetaDTO pluginMetaDTO = pluginMetaRepository.queryByIdAndVersion(pluginBindPO.getPluginId(), version);
        if (Objects.isNull(pluginMetaDTO)) {
            throw new BizException("AB001", "插件不存在");
        }

        String apiId = pluginBindPO.getApiId();
        if (PluginServiceTypeEnum.MCP.getType().equals(pluginMetaDTO.getServiceType())) {
            PluginApiInfo pluginApiDetail = pluginApiConfigService.getPluginApiDetail(pluginBindPO.getPluginId(), apiId, true);
            if (pluginApiDetail == null) {
                throw new BizException("AB002", "工具不存在");
            }
            PluginDefaultParamsRequest request = new PluginDefaultParamsRequest();
            request.setAgentCode(pluginBindPO.getAgentCode());
            request.setApiId(apiId);
            request.setTest(test);
            AgentBindApiParamsResponse agentBindApiParamsResponse = pluginBindConfigService.queryBotDefaultParams(request);

            PluginBindPageResponse pluginBindPageResponse = new PluginBindPageResponse();
            BeanUtil.copyProperties(pluginMetaDTO, pluginBindPageResponse);
            pluginBindPageResponse.setApiName(pluginApiDetail.getApiName());
            pluginBindPageResponse.setApiId(pluginApiDetail.getApiId());
            pluginBindPageResponse.setApiDesc(pluginApiDetail.getApiDesc());
            pluginBindPageResponse.setCreateTime(pluginApiDetail.getCreateTime());
            pluginBindPageResponse.setRequestParams(agentBindApiParamsResponse.getRequestParams());
            pluginBindPageResponse.setEnabled(pluginApiDetail.isEnabled());
            return pluginBindPageResponse;
        }
        // 先检查是否绑定参数
        PluginDefaultParamsRequest request = new PluginDefaultParamsRequest();
        request.setAgentCode(pluginBindPO.getAgentCode());
        request.setApiId(apiId);
        request.setTest(test);

        AgentBindApiParamsResponse agentBindApiParamsResponse = pluginBindConfigService.queryBotDefaultParamsForExecute(request);

        PluginApiInfo pluginApiDetail = pluginApiConfigService.getPluginApi(pluginMetaDTO, apiId);

        PluginBindPageResponse pluginBindPageResponse = new PluginBindPageResponse();
        BeanUtil.copyProperties(pluginMetaDTO, pluginBindPageResponse);
        pluginBindPageResponse.setApiName(pluginApiDetail.getApiName());
        pluginBindPageResponse.setApiId(pluginApiDetail.getApiId());
        pluginBindPageResponse.setApiDesc(pluginApiDetail.getApiDesc());
        pluginBindPageResponse.setCreateTime(pluginApiDetail.getCreateTime());

        pluginBindPageResponse.setRequestParams(agentBindApiParamsResponse.getRequestParams());
        pluginBindPageResponse.setResponseParams(agentBindApiParamsResponse.getResponseParams());

        pluginBindPageResponse.setDefaultResponseParams(pluginApiDetail.getRequestParams());
        pluginBindPageResponse.setDefaultResponseParams(pluginApiDetail.getResponseParams());

        pluginBindPageResponse.setEnabled(pluginApiDetail.isEnabled());
        return pluginBindPageResponse;
    }

    private void savePluginBind(PluginBindInfo dto, boolean bindOn) {
        try {
            if (bindOn) {
                LambdaQueryWrapper<PluginBindPO> wrapper = getQueryWrapper(dto);
                PluginBindPO one = pluginBindService.getOne(wrapper);
                if (one != null) {
                    one.setShelved(dto.getShelved());
                    pluginBindService.updateById(one);
                } else {
                    pluginBindRepository.save(dto);
                }
            } else {
                LambdaQueryWrapper<PluginBindPO> queryWrapper = getQueryWrapper(dto);
                pluginBindService.remove(queryWrapper);

                // 删除关联参数
                LambdaQueryWrapper<AgentBindApiParamPO> delete = new LambdaQueryWrapper<>();
                delete.eq(AgentBindApiParamPO::getAgentCode, dto.getAgentCode());
                delete.eq(AgentBindApiParamPO::getApiId, dto.getApiId());
                delete.eq(AgentBindApiParamPO::getPluginId, dto.getPluginId());
                delete.eq(AgentBindApiParamPO::getAgentVersion, dto.getAgentVersion());
                agentBindApiParamService.remove(delete);
            }
        } catch (Exception e) {
            throw new BizException("AB035", "工具绑定数据失败");
        }
    }

    private LambdaQueryWrapper<PluginBindPO> getQueryWrapper(PluginBindInfo dto) {
        LambdaQueryWrapper<PluginBindPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PluginBindPO::getPluginId, dto.getPluginId());
        queryWrapper.eq(PluginBindPO::getAgentCode, dto.getAgentCode());
        queryWrapper.eq(PluginBindPO::getApiId, dto.getApiId());
        queryWrapper.eq(PluginBindPO::getAgentVersion, dto.getAgentVersion());
        return queryWrapper;
    }

    @Override
    public AgentBindApiParamsResponse queryBotDefaultParams(PluginDefaultParamsRequest request) {
        String agentCode = request.getAgentCode();
        Long version;
        if (Boolean.TRUE.equals(request.getTest())) {
            version = agentVersionInfoRepository.getEditVersion(agentCode);
        } else {
            version = agentVersionInfoRepository.getPublishVersion(agentCode);
        }
        if (version == null) {
            throw new BizException("AD037", "bot不存在");
        }
        // 检查是否存在关联
        Integer i = queryBotRefNumByApiId(request.getApiId());
        if (i == 0) {
            throw new BizException("AB046", "未关联该工具");
        }
        // 查询关联的参数
        List<PluginApiParam> requestParamList = getApiParam(request, ReqRspEnum.REQUEST.getCode(), version);
        List<PluginApiParam> responseParamList = getApiParam(request, ReqRspEnum.RESPONSE.getCode(), version);

        if (CollectionUtils.isEmpty(requestParamList) && CollectionUtils.isEmpty(responseParamList)) {
            // 查询默认参数
            PluginApiInfo pluginApiDetail = pluginApiConfigService.getPluginApiDetail(null, request.getApiId(), true);
            if (pluginApiDetail != null) {
                requestParamList = pluginApiDetail.getRequestParams();
                responseParamList = pluginApiDetail.getResponseParams();
            }
        }

        AgentBindApiParamsResponse pluginApiInfo = new AgentBindApiParamsResponse();
        pluginApiInfo.setRequestParams(requestParamList);
        pluginApiInfo.setResponseParams(responseParamList);
        return pluginApiInfo;
    }

    @Override
    public AgentBindApiParamsResponse queryBotDefaultParamsForExecute(PluginDefaultParamsRequest request) {
        String agentCode = request.getAgentCode();
        Long version;
        if (Boolean.TRUE.equals(request.getTest())) {
            version = agentVersionInfoRepository.getEditVersion(agentCode);
        } else {
            version = agentVersionInfoRepository.getPublishVersion(agentCode);
        }
        if (version == null) {
            throw new BizException("AD037", "bot不存在");
        }

        // 查询关联的参数
        List<PluginApiParam> requestParamList = getApiParam(request, ReqRspEnum.REQUEST.getCode(), version);
        List<PluginApiParam> responseParamList = getApiParam(request, ReqRspEnum.RESPONSE.getCode(), version);

        AgentBindApiParamsResponse pluginApiInfo = new AgentBindApiParamsResponse();
        pluginApiInfo.setRequestParams(requestParamList);
        pluginApiInfo.setResponseParams(responseParamList);
        return pluginApiInfo;
    }

    private List<PluginApiParam> getApiParam(PluginDefaultParamsRequest request, int paramType, Long version) {
        List<AgentBindApiParamDTO> pluginApiRequestList = agentBindApiParamRepository.listByApiId(request.getAgentCode(), request.getApiId(), paramType, version);
        // 递归获取根参数
        List<AgentBindApiParamDTO> rootParams = pluginApiRequestList.stream().filter(param -> Objects.isNull(param.getFatherParamId())).collect(Collectors.toList());
        List<PluginApiParam> rootParamList = Lists.newArrayList();
        rootParams.forEach(param -> {
            PluginApiParam rootParam = new PluginApiParam();
            BeanUtils.copyProperties(param, rootParam);
            // 递归获取子参数
            List<PluginApiParam> subParams = recursionSubParam(pluginApiRequestList, param.getParamId());
            rootParam.setSubParameters(subParams);
            rootParamList.add(rootParam);
        });
        return rootParamList;
    }

    private List<PluginApiParam> recursionSubParam(List<AgentBindApiParamDTO> paramPOList, String parentParamId) {
        List<AgentBindApiParamDTO> subParamList = paramPOList.stream().filter(param -> Objects.equals(param.getFatherParamId(), parentParamId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subParamList)) {
            return Lists.newArrayList();
        }
        List<PluginApiParam> list = Lists.newArrayList();
        subParamList.forEach(param -> {
            PluginApiParam subParam = new PluginApiParam();
            BeanUtils.copyProperties(param, subParam);
            String paramId = param.getParamId();
            List<PluginApiParam> subParams = recursionSubParam(paramPOList, paramId);
            subParam.setSubParameters(subParams);
            subParam.setDefaultValue(null);
            list.add(subParam);
        });
        return list;
    }

    @Override
    public Integer queryBotRefNumByApiId(String apiId) {
        return pluginBindRepository.queryBindCountByApiId(apiId);
    }

    @Override
    public Integer queryBotRefNumByPluginId(String pluginId, Boolean shelved) {
        return pluginBindRepository.queryBindCountByPluginId(pluginId, shelved);
    }

    private void updateAgentStatus(AgentBasicInfoPO agentBasicInfoPO) {
        RequestInfo userInfo = RequestContext.get();
        Integer updateStatus = Objects.equals(agentBasicInfoPO.getStatus(),
                AgentStatusEnum.PUBLISHED.getCode()) ? AgentStatusEnum.MODIFY.getCode() : agentBasicInfoPO.getStatus();
        agentBasicInfoPO.setStatus(updateStatus);
        agentBasicInfoPO.setUpdateId(userInfo.getUserId());
        agentBasicInfoPO.setUpdateName(userInfo.getUserName());
        agentBasicInfoPO.setUpdateTime(LocalDateTime.now());
        agentBasicInfoService.updateById(agentBasicInfoPO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBotDefaultParams(UpdatePluginDefaultParamsRequest request) {
        String agentCode = request.getAgentCode();
        Long editVersion = agentVersionInfoRepository.getEditVersion(agentCode);
        if (editVersion == null) {
            throw new BizException("AD007", "bot不存在");
        }

        if (CollectionUtils.isEmpty(request.getRequestParams()) && CollectionUtils.isEmpty(request.getResponseParams())) {
            throw new BizException("AD008", "参数不能为空");
        }
        AgentBasicInfoPO agentBasicInfoPO = agentBasicInfoService.queryByAgentCodeAndVersion(agentCode, editVersion);
        if (agentBasicInfoPO == null) {
            throw new BizException("AD007", "bot不存在");
        }
        // 删除旧数据
        agentBindApiParamRepository.deleteOldData(agentCode, editVersion, request.getPluginId(), request.getApiId());

        // 保存新数据
        if (CollectionUtils.isNotEmpty(request.getRequestParams())) {
            saveParam(request, editVersion, 1);
        }
        if (CollectionUtils.isNotEmpty(request.getResponseParams())) {
            saveParam(request, editVersion, 2);
        }

        updateAgentStatus(agentBasicInfoPO);
        return Boolean.TRUE;
    }

    private void saveParam(UpdatePluginDefaultParamsRequest request, Long editVersion, int reqRspType) {
        List<PluginApiParam> params = null;
        if (reqRspType == 1) {
            params = request.getRequestParams();
        } else if (reqRspType == 2) {
            params = request.getResponseParams();
        }

        List<AgentBindApiParamDTO> requestList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(params)) {
            params.forEach(res -> {
                AgentBindApiParamDTO pluginApiParamPO = new AgentBindApiParamDTO();
                BeanUtils.copyProperties(res, pluginApiParamPO);
                pluginApiParamPO.setAgentCode(request.getAgentCode());
                pluginApiParamPO.setParamId(IdGenerator.getId(null));
                pluginApiParamPO.setApiId(request.getApiId());
                pluginApiParamPO.setPluginId(request.getPluginId());
                pluginApiParamPO.setReqRspType(reqRspType);
                if (reqRspType == 2) {
                    pluginApiParamPO.setLocation(4);
                }
                pluginApiParamPO.setAgentVersion(editVersion);
                // 新增子参数
                List<AgentBindApiParamDTO> subParams = recursionSubParam(res.getSubParameters(), request.getAgentCode(), request.getApiId(),
                        request.getPluginId(), reqRspType, pluginApiParamPO.getParamId(), editVersion, pluginApiParamPO.getLocation());
                requestList.add(pluginApiParamPO);
                requestList.addAll(subParams);
            });
        }
        if (CollectionUtils.isNotEmpty(requestList)) {
            agentBindApiParamRepository.saveBatch(requestList);
        }
    }

    /**
     * 递归的获取子参数的数据
     *
     * @param subParameters 子参数集合
     * @param agentCode
     * @param apiId         工具id
     * @param pluginId      插件id
     * @param paramType     参数类型
     * @param parentParamId 父参数id
     * @param editVersion   版本
     * @param location      参数所在位置
     * @return
     */
    private List<AgentBindApiParamDTO> recursionSubParam(List<PluginApiParam> subParameters, String agentCode, String apiId,
                                                         String pluginId, int paramType, String parentParamId, Long editVersion, int location) {
        if (CollectionUtils.isEmpty(subParameters)) {
            return Lists.newArrayList();
        }
        List<AgentBindApiParamDTO> subParamsList = Lists.newArrayList();
        subParameters.forEach(subParam -> {
            AgentBindApiParamDTO pluginApiParamDTO = new AgentBindApiParamDTO();
            BeanUtils.copyProperties(subParam, pluginApiParamDTO);
            pluginApiParamDTO.setParamId(IdGenerator.getId(null));
            pluginApiParamDTO.setAgentCode(agentCode);
            pluginApiParamDTO.setPluginId(pluginId);
            pluginApiParamDTO.setReqRspType(paramType);
            pluginApiParamDTO.setApiId(apiId);
            pluginApiParamDTO.setFatherParamId(parentParamId);
            pluginApiParamDTO.setLocation(location);
            pluginApiParamDTO.setAgentVersion(editVersion);
            // 递归子参数
            List<AgentBindApiParamDTO> subParams = recursionSubParam(subParam.getSubParameters(), agentCode, apiId, pluginId, paramType,
                    pluginApiParamDTO.getParamId(), editVersion, pluginApiParamDTO.getLocation());
            subParamsList.add(pluginApiParamDTO);
            subParamsList.addAll(subParams);
        });
        return subParamsList;
    }
}
