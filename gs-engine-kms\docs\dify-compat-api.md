# Dify兼容API文档

## 概述

本文档描述了完善后的Dify兼容API实现，支持完整的元数据条件筛选和标准化错误处理。

## 接口地址

```
POST /openapi/dify/retrieval
```

## 新增功能

### 1. 元数据条件筛选 (metadata_condition)

支持复杂的元数据筛选条件，包括：

#### 逻辑操作符
- `and`: 所有条件都必须满足（默认）
- `or`: 任一条件满足即可

#### 比较操作符
- `contains`: 包含某个值
- `not contains`: 不包含某个值
- `start with`: 以某个值开头
- `end with`: 以某个值结尾
- `is` / `=`: 等于某个值
- `is not` / `≠`: 不等于某个值
- `empty`: 为空
- `not empty`: 不为空
- `>`: 大于
- `<`: 小于
- `≥`: 大于等于
- `≤`: 小于等于
- `before`: 在某个日期之前
- `after`: 在某个日期之后

#### 支持的元数据字段
- `category`: 知识类型
- `tag` / `tags`: 标签列表
- `create_time`: 创建时间
- `update_time`: 更新时间
- `publish_time`: 发布时间
- `file_type`: 文件类型
- `knowledge_base`: 知识库名称
- `title`: 标题
- `description`: 描述
- `path`: 文件路径
- `knowledge_code`: 知识编码
- `knowledge_base_code`: 知识库编码
- `create_name`: 创建者姓名

### 2. 增强的错误处理

#### 标准错误格式
```json
{
    "error_code": 1001,
    "error_msg": "无效的 Authorization 头格式。预期格式为 `Bearer <api-key>`。"
}
```

#### 错误码说明
- `1001`: 无效的 Authorization 头格式
- `1002`: 授权失败
- `2001`: 知识库不存在
- `500`: 内部服务器错误

#### HTTP状态码
- `200`: 成功
- `400`: 请求参数错误
- `403`: 访问被拒绝
- `500`: 内部服务器错误

### 3. 增强的元数据响应

响应中的metadata字段现在包含更多信息：

```json
{
    "records": [{
        "content": "文档内容",
        "score": 0.98,
        "title": "文档标题",
        "metadata": {
            "path": "文件路径",
            "description": "文档描述",
            "category": "DOCUMENT",
            "tags": ["AI", "机器学习"],
            "create_time": 1640995200000,
            "update_time": 1640995200000,
            "knowledge_base": "知识库名称",
            "file_type": "DOCUMENT"
        }
    }]
}
```

## 使用示例

### 基本查询
```json
{
    "knowledge_id": "kb-001",
    "query": "人工智能",
    "retrieval_setting": {
        "top_k": 5,
        "score_threshold": 0.5
    }
}
```

### 带元数据筛选的查询
```json
{
    "knowledge_id": "kb-001",
    "query": "人工智能",
    "retrieval_setting": {
        "top_k": 5,
        "score_threshold": 0.5
    },
    "metadata_condition": {
        "logical_operator": "and",
        "conditions": [
            {
                "name": ["category"],
                "comparison_operator": "is",
                "value": "DOCUMENT"
            },
            {
                "name": ["tags"],
                "comparison_operator": "contains",
                "value": "AI"
            }
        ]
    }
}
```

### 复杂筛选示例
```json
{
    "knowledge_id": "kb-001",
    "query": "深度学习",
    "retrieval_setting": {
        "top_k": 10,
        "score_threshold": 0.3
    },
    "metadata_condition": {
        "logical_operator": "or",
        "conditions": [
            {
                "name": ["title", "description"],
                "comparison_operator": "contains",
                "value": "神经网络"
            },
            {
                "name": ["tags"],
                "comparison_operator": "contains",
                "value": "深度学习"
            },
            {
                "name": ["create_time"],
                "comparison_operator": "after",
                "value": "1640995200000"
            }
        ]
    }
}
```

## 注意事项

1. **认证方式**: 支持`Authorization: Bearer <api-key>`头部认证，其中api-key为Base64编码的`tenantId@userId`格式
2. **字段匹配**: `name`字段支持数组，可以同时匹配多个字段
3. **类型转换**: 系统会自动处理数值和时间类型的转换
4. **性能考虑**: 复杂的metadata条件会在搜索结果返回后进行过滤，可能影响性能
5. **兼容性**: 完全兼容原有的Dify API规范

## 测试建议

建议使用提供的单元测试来验证各种筛选场景：
- 基本字符串匹配
- 数组字段匹配
- 空值检查
- 逻辑操作符组合
- 多字段名称匹配
