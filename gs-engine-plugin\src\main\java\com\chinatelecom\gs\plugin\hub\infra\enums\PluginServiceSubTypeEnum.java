package com.chinatelecom.gs.plugin.hub.infra.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 */

@Getter
public enum PluginServiceSubTypeEnum {

    STDIO(1, "stdio"),
    SSE(2, "sse"),
    STREAMABLEHTTP(3, "streamablehttp");

    private final Integer code;
    private final String type;

    PluginServiceSubTypeEnum(Integer code, String type) {
        this.code = code;
        this.type = type;
    }

}
