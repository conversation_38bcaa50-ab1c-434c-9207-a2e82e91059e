package com.chinatelecom.gs.engine.robot.manage.controller.web.manage;

import cn.hutool.core.text.CharSequenceUtil;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.cache.lock.DistributedLock;
import com.chinatelecom.gs.engine.common.cache.lock.ZLock;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.AgentBasicInfoRequest;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.AgentTemplateRequest;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.PublishAgentBasicInfoRequest;
import com.chinatelecom.gs.engine.robot.manage.info.domain.response.*;
import com.chinatelecom.gs.engine.robot.manage.info.enums.ScopeEnum;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.enums.ResultCode;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.RuntimeStatusEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.domain.ConfigDetailVO;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.request.AgentDefaultConfigValuesRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.request.AgentInfoRequest;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.UpdateAgentDialogModeRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.response.AgentBasicInfoResponse;
import com.chinatelecom.gs.engine.robot.manage.info.domain.response.QueryAgentDialogModeResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.page.PageImpl;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.BufferedOutputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * (AgentBasicInfo)表控制层
 *
 * <AUTHOR>
 * @since 2024-07-04 08:55:04
 */
@Slf4j
@Tag(name = "机器人信息管理", description = "机器人信息管理")
@RestController
@RequestMapping({Constants.ROBOT_PREFIX + Constants.WEB_PREFIX, Constants.ROBOT_PREFIX + Constants.API_PREFIX})
public class AgentBasicInfoController {

    /**
     * 服务对象
     */
    @Resource
    private AgentBasicConfigService agentBasicConfigService;

    @Resource
    private DistributedLock distributedLock;

    private static final LinkedHashMap<String, String> iconMap = new LinkedHashMap<>();

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @PermissionTag(code = {MenuConfig.ROBOT_MARKET, KsMenuConfig.ROBOT_MARKET})
    @Operation(summary = "查询机器人分类", method = "GET")
    @PlatformRestApi(name = "查询机器人分类", groupName = "机器人信息管理")
    @GetMapping("/queryBotType")
    @AuditLog(businessType = "机器人信息管理", operType = "查询机器人分类", operDesc = "查询机器人分类", objId = "null")
    public Result<List<BotTypeEntity>> queryBotType() {
        return Result.success(agentBasicConfigService.queryBotType());
    }

    @PermissionTag(code = {MenuConfig.ROBOT_MARKET, KsMenuConfig.ROBOT_MARKET})
    @Operation(summary = "查询公共机器人列表")
    @PlatformRestApi(name = "查询公共机器人列表", groupName = "机器人信息管理")
    @PostMapping("/queryAgentTemplateList")
    @AuditLog(businessType = "机器人信息管理", operType = "查询公共机器人列表", operDesc = "查询公共机器人列表", objId = "#agentTemplateRequest.scope")
    public Result<PageImpl<AgentAndConfInfoResponse>> queryAgentTemplateList(
            @Valid @RequestBody AgentTemplateRequest agentTemplateRequest) {
        return Result.success(agentBasicConfigService.queryAgentTemplateList(agentTemplateRequest));
    }

    @PermissionTag(code = {MenuConfig.ROBOT_MARKET, KsMenuConfig.ROBOT_MARKET})
    @Operation(summary = "下架机器人")
    @PlatformRestApi(name = "下架公共机器人", groupName = "机器人信息管理")
    @GetMapping("/removeFromShelf")
    @AuditLog(businessType = "机器人信息管理", operType = "下架公共机器人", operDesc = "下架公共机器人", objId = "#agentCode")
    public Result<Boolean> removeFromShelf(
            @Valid @NotBlank(message = "机器人编码不能为空") String agentCode) {
        return Result.success(agentBasicConfigService.removeFromShelf(agentCode));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "查询我的机器人列表", method = "POST")
    @PlatformRestApi(name = "查询我的机器人列表", groupName = "机器人信息管理")
    @PostMapping("/queryAgentList")
    @AuditLog(businessType = "机器人信息管理", operType = "查询我的机器人列表", operDesc = "查询我的机器人列表", objId = "null")
    public Result<PageImpl<AgentBasicInfoResponse>> queryAgentList(@Valid @RequestBody AgentInfoRequest agentInfoRequest) {
        if (agentInfoRequest.getSourceSystem() == null) {
            AppSourceType appSourceType = RequestContext.getAppSourceType();
            BizAssert.notNull(appSourceType, "AD042", "无法获取操作来源信息");
            agentInfoRequest.setSourceSystem(appSourceType);
        }

        return Result.success(agentBasicConfigService.queryAgentList(agentInfoRequest));
    }

    @Operation(summary = "查询默认配置接口", method = "GET")
    @PlatformRestApi(name = "查询默认配置接口", groupName = "机器人信息管理")
    @GetMapping("/queryDefaultConfig")
    @AuditLog(businessType = "机器人信息管理", operType = "查询默认配置接口", operDesc = "查询默认配置接口", objId = "null")
    public Result<AgentDefaultConfResponse> queryDefaultConfig() {
        return Result.success(agentBasicConfigService.queryDefaultConfig());
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "对话系统查询机器人", method = "GET")
    @PlatformRestApi(name = "对话系统查询机器人", groupName = "机器人信息管理")
    @GetMapping("/queryAgentAndDialog")
    @AuditLog(businessType = "机器人信息管理", operType = "对话系统查询机器人", operDesc = "对话系统查询机器人", objId = "null")
    public Result<List<AgentAndDialogResponse>> queryAgentAndDialog(@RequestParam(name = "botType", required = false) Integer botType) {
        return Result.success(agentBasicConfigService.queryAgentAndDialog(botType));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.STATISTIC_BOT, KsMenuConfig.BOT})
    @Operation(summary = "机器人列表接口", method = "POST")
    @PlatformRestApi(name = "查询所有机器人", groupName = "机器人信息管理")
    @PostMapping("/queryAllAgent")
    @AuditLog(businessType = "机器人信息管理", operType = "查询所有机器人", operDesc = "查询所有机器人", objId = "null")
    public Result<List<AgentBasicInfoResponse>> queryAllAgent() {
        return Result.success(agentBasicConfigService.queryAllAgent());
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @PermissionTag(code = {MenuConfig.DIALOG_FLOW, MenuConfig.WORKFLOW, KsMenuConfig.WORKFLOW_FLOW})
    @Operation(summary = "查询技能", method = "GET")
    @PlatformRestApi(name = "查询技能", groupName = "机器人信息管理")
    @GetMapping("/queryPluginAndWorkflow")
    @AuditLog(businessType = "机器人信息管理", operType = "查询技能", operDesc = "查询技能", objId = "#agentCode")
    public Result<List<AgentSkillResponse>> queryPluginAndWorkflow(@RequestParam(value = "agentCode") String agentCode,
                                                                   @RequestParam(value = "name", required = false) String name) {
        return Result.success(agentBasicConfigService.queryPluginAndWorkflow(agentCode, name));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "查询机器人", method = "GET")
    @PlatformRestApi(name = "查询机器人", groupName = "机器人信息管理")
    @GetMapping("/queryAgent")
    @AuditLog(businessType = "机器人信息管理", operType = "查询机器人", operDesc = "查询机器人", objId = "#agentCode")
    public Result<QueryAgentInfoResponse> queryAgent(@RequestParam(value = "agentCode") String agentCode) {
        return Result.success(agentBasicConfigService.queryAgent(agentCode));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "保存机器人", method = "POST")
    @PlatformRestApi(name = "保存机器人", groupName = "机器人信息管理")
    @PostMapping("/saveAgent")
    @AuditLog(businessType = "机器人信息管理", operType = "保存机器人", operDesc = "保存机器人", objId = "null")
    public Result<EditAgentBasicResponse> saveAgent(@Valid @RequestBody AgentBasicInfoRequest agentBasicInfoParam) throws Exception {
        ZLock zLock = distributedLock.tryLock("saveAgent" + RequestContext.getUserId(), 3, 10, TimeUnit.SECONDS);
        if (zLock == null) {
            throw new BizException("AD043", "保存失败，请稍后重试");
        }
        try {
            if (agentBasicInfoParam.getSourceSystem() == null) {
                AppSourceType appSourceType = RequestContext.getAppSourceType();
                BizAssert.notNull(appSourceType, "AD042", "无法获取操作来源信息");
                agentBasicInfoParam.setSourceSystem(appSourceType);
            }
        } catch (Exception e) {
            throw new BizException("A0000", e.getMessage(), e.getMessage());
        } finally {
            distributedLock.unlock(zLock);
        }
        return Result.success(agentBasicConfigService.saveOrUpdate(agentBasicInfoParam));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "删除机器人", method = "DELETE")
    @PlatformRestApi(name = "删除机器人", groupName = "机器人信息管理")
    @DeleteMapping("/delete/agent")
    @AuditLog(businessType = "机器人信息管理", operType = "删除机器人", operDesc = "删除机器人", objId = "#agentCode")
    public Result<Boolean> deleteAgent(@RequestParam(value = "agentCode") String agentCode) {
        return Result.success(agentBasicConfigService.deleteAgent(agentCode));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "自动生成开场白", method = "GET")
    @PlatformRestApi(name = "自动生成开场白", groupName = "机器人信息管理")
    @GetMapping("/generatePrologue")
    @AuditLog(businessType = "机器人信息管理", operType = "自动生成开场白", operDesc = "自动生成开场白", objId = "#agentCode")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<AgentPrologueResponse> generatePrologue(@RequestParam(value = "agentCode") String agentCode) {
        return Result.success(agentBasicConfigService.generatePrologue(agentCode));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "复制机器人", method = "GET")
    @PlatformRestApi(name = "复制机器人", groupName = "机器人信息管理")
    @PostMapping("/copyAgent")
    @AuditLog(businessType = "机器人信息管理", operType = "复制机器人", operDesc = "复制机器人", objId = "#agentCode")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<String> copyAgent(@RequestParam(value = "agentCode") String agentCode) {
        return Result.success(agentBasicConfigService.copyAgent(agentCode));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "发布机器人")
    @PlatformRestApi(name = "发布机器人", groupName = "机器人信息管理")
    @PostMapping("/publish")
    @AuditLog(businessType = "机器人信息管理", operType = "发布机器人", operDesc = "发布机器人", objId = "#request.agentCode")
    public Result<Long> publish(@Valid @RequestBody PublishAgentBasicInfoRequest request) {
        String agentCode = request.getAgentCode();
        ScopeEnum scope = ScopeEnum.valueOf(request.getScope());
        if (ScopeEnum.UNPUBLISHED.equals(scope)) {
            throw new BizException("A0001", "无效的发布范围");
        }
        PlatformUser user = SsoUtil.get();
        if ((!Boolean.TRUE.equals(user.getIsAdmin())) && (ScopeEnum.PUBLIC.equals(scope) || ScopeEnum.PUBLIC_AND_CONFIG.equals(scope))) {
            // !Boolean.TRUE.equals(user.getIsAdmin()) 这样写的目的是防止isAdmin为null
            // 管理员才能发布公共机器人
            throw new BizException("A0087", "非管理员不能操作");
        }
        List<String> tagCodeList = request.getTagCodeList();
        Long res;
        try {
            res = agentBasicConfigService.publish(agentCode, scope, RuntimeStatusEnum.RUNNING_PROD.getValue(), true, tagCodeList);
        } catch (BizException e) {
            return Result.failed(e.getCode(), e.getMessage());
        }

        if (res == null) {
            return Result.failed(ResultCode.SYSTEM_EXCEPTION.getCode(), "publish failed");
        }
        return Result.success(res);
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "查询默认配置", method = "POST")
    @PlatformRestApi(name = "查询默认配置", groupName = "机器人信息管理")
    @PostMapping("/batchQueryDefaultAgentConfigValues")
    @AuditLog(businessType = "机器人信息管理", operType = "查询默认配置", operDesc = "查询默认配置", objId = "null")
    public Result<List<ConfigDetailVO>> batchQueryDefaultAgentConfigValues(@Valid AgentDefaultConfigValuesRequest request) {
        return Result.success(agentBasicConfigService.batchQueryDefaultAgentConfigValues(request));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "查询应答模式", method = "POST")
    @PlatformRestApi(name = "查询应答模式", groupName = "机器人信息管理")
    @GetMapping("/queryDialogMode")
    @AuditLog(businessType = "机器人信息管理", operType = "查询应答模式", operDesc = "查询应答模式", objId = "null")
    public Result<QueryAgentDialogModeResponse> queryDialogMode(@RequestParam(name = "agentCode") String agentCode) {
        agentBasicConfigService.checkAgentAuth(agentCode, true);
        return Result.success(agentBasicConfigService.queryDialogMode(agentCode, true));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "更新应答模式", method = "POST")
    @PlatformRestApi(name = "更新应答模式", groupName = "机器人信息管理")
    @PostMapping("/updateDialogMode")
    @AuditLog(businessType = "机器人信息管理", operType = "更新应答模式", operDesc = "更新应答模式", objId = "null")
    public Result<Boolean> updateDialogMode(@Valid UpdateAgentDialogModeRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(agentBasicConfigService.updateDialogMode(request));
    }

    @Operation(summary = "下载接口文档", method = "GET")
    @PlatformRestApi(name = "下载接口文档", groupName = "数据库管理")
    @GetMapping("/downloadApiFile")
    @AuditLog(businessType = "机器人信息管理", operType = "下载接口文档", operDesc = "下载接口文档", objId = "null")
    public void downloadFile(HttpServletResponse response) {
        agentBasicConfigService.downloadFile(response);
    }

    static {
        iconMap.put("ds", "/usr/src/app/telecom/defaultDs.png");
        iconMap.put("xw", "/usr/src/app/telecom/defaultXw.png");
        iconMap.put("img_excel.png", "/usr/src/app/telecom/img_excel.png");
        iconMap.put("img_ppt.png", "/usr/src/app/telecom/img_ppt.png");
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "查询默认机器人图标", method = "POST")
    @PlatformRestApi(name = "查询默认机器人图标", groupName = "机器人信息管理")
    @GetMapping("/defaultIcon")
    @AuditLog(businessType = "机器人信息管理", operType = "查询默认机器人图标", operDesc = "查询默认机器人图标", objId = "null")
    public void usageGuidelines(@RequestParam(name = "code", required = false) String code, HttpServletResponse response) {
        String filename = "/usr/src/app/telecom/default.png";
        String filePath = iconMap.get(code);
        filename = CharSequenceUtil.isBlank(filePath) ? filename : filePath;
        try (FileInputStream is = new FileInputStream(filename)) {
            response.setCharacterEncoding("utf-8");
            OutputStream os = new BufferedOutputStream(response.getOutputStream());
            byte[] bytes;
            while (is != null && is.read(bytes = new byte[1024]) != -1) {
                os.write(bytes);
            }
            os.flush();
            os.close();
        } catch (IOException ioe) {
            log.error("下载默认机器人头像异常", ioe);
        }
    }
}
