package com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.processor;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.chinatelecom.gs.engine.common.cache.memory.MessageResponseCache;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.LLMResult;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.MessageRoleEnum;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.Function;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.Tool;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.ToolCall;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.AgentStrategyConfig;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.StrategyNodeContext;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.StrategyNodeRequest;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.action.engine.inner.tool.SourceInfosFilterService;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.llm.LLMInvokeUtil;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.secure.AgentSafeFenceCheck;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.toolselect.CandidateSelectService;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.toolselect.ToolUtils;
import com.chinatelecom.gs.engine.robot.manage.info.domain.dto.StrategyNodeSetting;
import com.chinatelecom.gs.engine.robot.sdk.dto.FunctionTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.DialogState;
import com.chinatelecom.gs.engine.robot.sdk.enums.IntentRetrieveType;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.plugin.ExecutePluginRpcApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.AgentConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.GlobalPolicyConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.ModelConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntent;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntentAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.AnswerResponseType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.ToolAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.ToolMixerAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.ExecutePluginApiRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.ExecutePluginApiResponse;
import com.chinatelecom.gs.plugin.hub.application.service.PluginApiConfigService;
import com.chinatelecom.gs.workflow.core.workflow.core.DagEngine;
import com.chinatelecom.gs.workflow.core.workflow.core.context.DagContext;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.DagStatusEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.EnvTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.model.dag.DagParam;
import com.chinatelecom.gs.workflow.core.workflow.core.model.result.DagResult;
import com.chinatelecom.gs.workflow.core.workflow.core.utils.DagParamUtils;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MultiRecallMixStrategyProcessor extends AbstractStrategyProcessor {

    @Autowired
    private DagEngine dagEngine;

    @Autowired
    private ExecutePluginRpcApi executePluginRpcApi;

    @Autowired
    private PluginApiConfigService pluginApiConfigService;

    @Autowired
    private AgentSafeFenceCheck agentSafeFenceCheck;

    @Autowired
    private ExecutorService commonExecutorService;

    @Resource
    private SourceInfosFilterService sourceInfosFilterService;

    @Value("${platform.sourceInfo.waitTime:3}")
    private Long waitTime;

    @Resource
    private CandidateSelectService candidateSelectService;

    @Override
    public ToolMixerAnswer doExecute(StrategyNodeContext strategyNodeContext, DagContext context) {
        log.info("执行意图召回策略");

        StrategyNodeRequest nodeRequest = strategyNodeContext.getStrategyNodeRequest();
        AgentStrategyConfig request = strategyNodeContext.getAgentStrategyConfig();
        StrategyNodeSetting nodeSetting = strategyNodeContext.getStrategyNodeSetting();

        LLMInvokeUtil llmInvokeUtil = SpringContextUtils.getBean(LLMInvokeUtil.class);


        List<WrapLLMMessage> useHistories = sessionHistoryCacheService.getChatHistoryFromCache(context.getSessionId(),
                context.getMessageId(),
                Optional.ofNullable(request.getAgentConfig()).map(AgentConfig::getModelConfig).map(ModelConfig::getMemoryCount).orElse(null),
                true, true);

        Map<String, List<ToolIntentAnswer>> candidatesByType = new HashMap<>();

        if (!nodeRequest.isSelectedContents()) {
            //增加本次使用rag生成，还是选择某个tool的判断
            candidatesByType = this.candidateSelectService.selectCandidates(strategyNodeContext, context, useHistories, llmInvokeUtil);
        } else {
            for (ToolIntentAnswer recallIntent : nodeRequest.getRecallContents()) {
                if (recallIntent.getDialogEngineType().equals(DialogEngineType.KMS) || recallIntent.getDialogEngineType().equals(DialogEngineType.FILE)) {
                    candidatesByType.computeIfAbsent(FunctionTypeEnum.KNOWLEDGE.getCode(), k -> new ArrayList<>()).add(recallIntent);
                } else if (ToolUtils.biAnswer(recallIntent) || recallIntent.getDialogEngineType().equals(DialogEngineType.DATABASE)) {
                    candidatesByType.computeIfAbsent(FunctionTypeEnum.TABLE.getCode(), k -> new ArrayList<>()).add(recallIntent);
                } else if (recallIntent.getDialogEngineType().equals(DialogEngineType.PLUGIN) || recallIntent.getDialogEngineType().equals(DialogEngineType.WORKFLOW)) {
                    candidatesByType.computeIfAbsent(FunctionTypeEnum.TOOL.getCode(), k -> new ArrayList<>()).add(recallIntent);
                } else {
                    candidatesByType.computeIfAbsent(FunctionTypeEnum.OTHER.getCode(), k -> new ArrayList<>()).add(recallIntent);
                }
            }
        }

        //因为这里的history在role为use的时候设置了处理的tool，所以需要去掉
        useHistories.forEach(history -> {
            if (history.getRole().equalsIgnoreCase(MessageRoleEnum.user.getCode())) {
                history.setTool_call_id(null);
            }
        });


        //闲聊场景
        if (CollectionUtils.isEmpty(candidatesByType) ||
                (candidatesByType.size() == 1
                        && candidatesByType.containsKey(FunctionTypeEnum.OTHER.getCode())
                        && CollectionUtils.isEmpty(candidatesByType.get(FunctionTypeEnum.OTHER.getCode())))) {
            GlobalPolicyConfig globalPolicyConfig = request.getAgentConfig().getGlobalPolicyConfig();
            if (Objects.nonNull(globalPolicyConfig)) {
                Boolean clientChatLLMSwitch = strategyNodeContext.getAgentStrategyConfig().getHeader().getClient().getChatLLMSwitch();
                Boolean chatLLMSwitch = Objects.nonNull(clientChatLLMSwitch) ? clientChatLLMSwitch : globalPolicyConfig.getChatLLMSwitch();
                if (Boolean.FALSE.equals(chatLLMSwitch)) {
                    log.info("【策略节点】大模型闲聊开关关闭，直接返回");
                    return null;
                }
            }
        }


        List<ToolMixerAnswer> multiAnswers = new ArrayList<>();
        for (Map.Entry<String, List<ToolIntentAnswer>> entry : candidatesByType.entrySet()) {
            String functionType = entry.getKey();
            List<ToolIntentAnswer> candidateIntents = entry.getValue();
            ToolMixerAnswer toolMixerAnswer = getSingleTypeAnswer(candidateIntents, functionType, llmInvokeUtil, useHistories, strategyNodeContext, context);
            if (Objects.nonNull(toolMixerAnswer)) {
                multiAnswers.add(toolMixerAnswer);
            }
        }

        if (CollectionUtils.isEmpty(multiAnswers)) {
            return null;
        } else if (multiAnswers.size() == 1) {
            return multiAnswers.get(0);
        }

        multiAnswers.forEach(context::addToolAnswer);
        return mixAllAnswer(multiAnswers, llmInvokeUtil, useHistories, strategyNodeContext, context);
    }

    private ToolMixerAnswer mixAllAnswer(List<ToolMixerAnswer> answers,
                                         LLMInvokeUtil llmInvokeUtil,
                                         List<WrapLLMMessage> histories,
                                         StrategyNodeContext nodeContext,
                                         DagContext context) {

        StrategyNodeRequest nodeRequest = nodeContext.getStrategyNodeRequest();
        AgentStrategyConfig request = nodeContext.getAgentStrategyConfig();
        StrategyNodeSetting nodeSetting = nodeContext.getStrategyNodeSetting();

        List<ToolIntentAnswer> answerAsKnowledge  = answers.stream().map(answer -> {
            ToolIntentAnswer toolIntentAnswer = new ToolIntentAnswer();
            toolIntentAnswer.setDialogEngineType(DialogEngineType.KMS);
            toolIntentAnswer.setRetrieveType(IntentRetrieveType.DOC);
            toolIntentAnswer.setToolIntentName(answer.getToolIntentName());
            toolIntentAnswer.setSimilarContent(answer.getToolAnswer().getContent().toString());
            return toolIntentAnswer;
        }).collect(Collectors.toList());

        LLMResult result = llmInvokeUtil.invoke(nodeRequest.getUserQuery(), request, nodeSetting, answerAsKnowledge, histories, context);

        if (Objects.isNull(result) || Objects.isNull(result.getLlmMessage()) || StringUtils.isBlank(result.getLlmMessage().getContent())) {
            log.warn("调用大模型融合返回结果为空!!!");
            return null;
        }

        return buildToolAnswerByLLMResult(llmInvokeUtil, result, nodeRequest.getAgentCode(), answers.stream().map(an -> (ToolIntentAnswer) an).collect(Collectors.toList()));
    }


    private ToolMixerAnswer getSingleTypeAnswer(List<ToolIntentAnswer> candidateIntents,
                                                String functionType,
                                                LLMInvokeUtil llmInvokeUtil,
                                                List<WrapLLMMessage> histories,
                                                StrategyNodeContext nodeContext,
                                                DagContext context) {

        StrategyNodeRequest nodeRequest = nodeContext.getStrategyNodeRequest();
        AgentStrategyConfig request = nodeContext.getAgentStrategyConfig();
        StrategyNodeSetting nodeSetting = nodeContext.getStrategyNodeSetting();

        //如果只有一个tool，且
        if (!CollectionUtils.isEmpty(candidateIntents) && candidateIntents.size() == 1) {
            ToolIntentAnswer toolChoice = candidateIntents.get(0);

            //tool是workflow/plugin， 且tool的参数为空，则直接调用
            if (ToolUtils.noParamTool(toolChoice)) {
                ToolMixerAnswer toolMixerAnswer = invokeForToolCallSelection(context, null, toolChoice, nodeRequest, llmInvokeUtil, request);
                if (toolMixerAnswer != null) {
                    return toolMixerAnswer;
                }

            }

            //如果tool是teleBI，直接调用teleBI
            if (ToolUtils.biAnswer(toolChoice)) {
                ToolMixerAnswer biAnswer = invokeBI(toolChoice, nodeContext, context);
                if (biAnswer != null) {
                    return biAnswer;
                }
            }
        }

        if (functionType.equalsIgnoreCase(FunctionTypeEnum.KNOWLEDGE.getCode()) || functionType.equalsIgnoreCase(FunctionTypeEnum.OTHER.getCode())) {
            return getKnowledgeAnswer(candidateIntents, llmInvokeUtil, nodeRequest, request, nodeSetting, context);
        } else if (functionType.equalsIgnoreCase(FunctionTypeEnum.TOOL.getCode())) {
            return getToolAnswer(candidateIntents, llmInvokeUtil, histories, nodeRequest, request, nodeSetting, context);
        }
        return null;
    }


    private ToolMixerAnswer getToolAnswer(List<ToolIntentAnswer> candidateIntents,
                                          LLMInvokeUtil llmInvokeUtil,
                                          List<WrapLLMMessage> histories,
                                          StrategyNodeRequest nodeRequest,
                                          AgentStrategyConfig request,
                                          StrategyNodeSetting nodeSetting,
                                          DagContext context) {

        Map<String, String> parsedIntentIds = ToolUtils.toFunctionIntentMap(candidateIntents);
        LLMResult result = llmInvokeUtil.invoke(nodeRequest.getUserQuery(), request, nodeSetting, candidateIntents, histories, context);

        if (Objects.isNull(result) || Objects.isNull(result.getLlmMessage())) {
            log.warn("调用大模型返回结果为空!!!");
            return null;
        }

        WrapLLMMessage llmMessage = result.getLlmMessage();

        //大模型tool填参话术
        if (CollectionUtils.isEmpty(llmMessage.getToolCalls())) {
            if (!StringUtils.isBlank(llmMessage.getContent())) {
                return buildToolAnswerByLLMResult(llmInvokeUtil, result, nodeRequest.getAgentCode(), candidateIntents);
            } else {
                log.error("大模型没有输出任何业务流或插件调用结果!!!");
                return null;
            }
        }

        ToolCall tc = llmMessage.getToolCalls().get(0);
        ToolIntent toolIntent = ToolUtils.parseToolFunctionName(tc.getFunction().getName(), parsedIntentIds);
        if (toolIntent == null) {
            log.error("解析functionName失败，functionName：{}", tc.getFunction().getName());
            return null;
        }

        return invokeForToolCallSelection(context, llmMessage, toolIntent, nodeRequest, llmInvokeUtil, request);
    }


    private @Nullable ToolMixerAnswer invokeForToolCallSelection(DagContext context, WrapLLMMessage llmMessage, ToolIntent toolChoice,
                                                                 StrategyNodeRequest nodeRequest, LLMInvokeUtil llmInvokeUtil, AgentStrategyConfig request) {
        ToolCall toolCall = null;
        if (Objects.isNull(llmMessage)) {
            //这里补一个toolCall到history里面去，大模型才不会报错
            List<Tool> tools = llmInvokeUtil.buildTools(Lists.newArrayList(toolChoice));
            Tool tool = tools.get(0);
            toolCall = new ToolCall();
            String callId = UUID.randomUUID().toString();
            toolCall.setId(callId);
            toolCall.setType("function");
            Function function = new Function();
            function.setName(tool.getFunction().getName());
            toolCall.setFunction(function);
            WrapLLMMessage toolCallMessage = new WrapLLMMessage();
            toolCallMessage.setRole(MessageRoleEnum.assistant.getCode());
            toolCallMessage.setToolCalls(Collections.singletonList(toolCall));
            MessageResponseCache toolCallRes = BeanUtil.toBean(toolCallMessage, MessageResponseCache.class);
            sessionHistoryCacheService.addSessionMessageResponse(context.getSessionId(), context.getMessageId(), toolCallRes);
        } else {
            toolCall = llmMessage.getToolCalls().get(0);
            MessageResponseCache toolCallRes = BeanUtil.toBean(llmMessage, MessageResponseCache.class);
            sessionHistoryCacheService.addSessionMessageResponse(context.getSessionId(), context.getMessageId(), toolCallRes);
        }


        ToolIntentAnswer directFlowAnswer = null;
        if (Objects.equals(DialogEngineType.WORKFLOW, toolChoice.getDialogEngineType())) {
            directFlowAnswer = executeWorkflow(toolChoice.getToolIntentId(), context, toolCall, nodeRequest.getUserQuery(), nodeRequest.getUserFile());
        } else if (Objects.equals(DialogEngineType.PLUGIN, toolChoice.getDialogEngineType())) {
            directFlowAnswer = executeApi(toolChoice, toolCall, context.getSessionId(), context.getEnv().equals(EnvTypeEnum.TEST), request.getBusinessCode());
        }

        if (Objects.isNull(directFlowAnswer)) {
            return null;
        }


        if (directFlowAnswer.getToolAnswer().getResponseType().equals(AnswerResponseType.DIRECT)) {
            ToolMixerAnswer toolMixerAnswer = new ToolMixerAnswer(directFlowAnswer);
            toolMixerAnswer.setSourceToolAnswers(Lists.newArrayList(directFlowAnswer));
            return toolMixerAnswer;
        }


        ToolMixerAnswer toolMixerAnswer = getLLMAnswerAfterToolResult(nodeRequest, context, toolCall, directFlowAnswer, request, llmInvokeUtil, null);
        if (Objects.nonNull(toolMixerAnswer)) {
            return toolMixerAnswer;
        }
        return null;
    }

    private ToolMixerAnswer getKnowledgeAnswer(List<ToolIntentAnswer> candidateIntents,
                                               LLMInvokeUtil llmInvokeUtil,
                                               StrategyNodeRequest nodeRequest,
                                               AgentStrategyConfig request,
                                               StrategyNodeSetting nodeSetting,
                                               DagContext context) {

        ToolIntentAnswer topKmsIntent = candidateIntents.stream().filter(intent -> intent.getDialogEngineType().equals(DialogEngineType.KMS)).findFirst().orElse(null);
        //top1是多模态，走一次多模态
        if (Objects.nonNull(topKmsIntent) && AnswerResponseType.VLLM.equals(topKmsIntent.getResponseType())) {
            LLMResult llmResult = llmInvokeUtil.invokeMulti(nodeRequest.getQuery(), request, topKmsIntent);
            ToolMixerAnswer toolMixerAnswer = buildToolAnswerByLLMResult(llmInvokeUtil, llmResult, nodeRequest.getAgentCode(), Lists.newArrayList(topKmsIntent));
            if (Objects.nonNull(toolMixerAnswer)) {
                return toolMixerAnswer;
            }
        }

        //正常走知识RAG
        List<ToolIntentAnswer> validCandidates = new ArrayList<>();
        CountDownLatch countDownLatch = new CountDownLatch(1);
        List<ToolIntentAnswer> finalCandidateIntents = candidateIntents;
        commonExecutorService.execute(() -> {
            try {
                validCandidates.addAll(this.sourceInfosFilterService.filter(nodeRequest.getQuery(), finalCandidateIntents, request, false));
            } catch (Exception e) {
                if (e instanceof BizException && "C0005".equals(((((BizException) e).getCode())))) {
                    log.error("大模型调用命中安全中心检测", e);
                } else {
                    log.error("大模型校验来源失败！", e);
                }
            } finally {
                countDownLatch.countDown();
            }
        });

        LLMResult result = llmInvokeUtil.invoke(nodeRequest.getQuery(), request, nodeSetting, candidateIntents, Collections.emptyList(), context);

        if (Objects.nonNull(result.getInterrupt()) && result.getInterrupt().equals(2)) {
            ToolIntentAnswer toolIntentAnswer = llmInvokeUtil.buildLLMSecurityAnswer(result);
            return new ToolMixerAnswer(toolIntentAnswer);
        }

        WrapLLMMessage llmMessage = result.getLlmMessage();
        // 第一次请求大模型就有结果，直接使用并输出
        if (!StringUtils.isBlank(llmMessage.getContent())) {
            List<ToolIntentAnswer> sourceIntents = candidateIntents.stream()
                    .filter(intent -> intent.getDialogEngineType().equals(DialogEngineType.KMS) || intent.getDialogEngineType().equals(DialogEngineType.FILE)
                            || intent.getDialogEngineType().equals(DialogEngineType.DATABASE)).collect(Collectors.toList());
            try {
                boolean await = countDownLatch.await(waitTime, TimeUnit.SECONDS);
                if (await) {
                    sourceIntents = validCandidates;
                } else {
                    log.error("等待大模型校验来源线程超时，");
                }
            } catch (InterruptedException e) {
                log.error("等待大模型校验来源异常！", e);
                Thread.currentThread().interrupt();
            }

            log.info("【策略节点】大模型输出返回!!!!");
            ToolMixerAnswer toolMixerAnswer = buildToolAnswerByLLMResult(llmInvokeUtil, result, nodeRequest.getAgentCode(), sourceIntents);
            if (Objects.nonNull(toolMixerAnswer)) {
                return toolMixerAnswer;
            }
        }

        return null;
    }


    private ToolMixerAnswer buildToolAnswerByLLMResult(LLMInvokeUtil llmInvokeUtil, LLMResult llmResult, String agentCode, List<ToolIntentAnswer> sourceCandidates) {
        if (Objects.isNull(llmResult) || Objects.isNull(llmResult.getLlmMessage())) {
            log.warn("大模型返回结果为空!!!");
            return null;
        }

        if (Objects.nonNull(llmResult.getInterrupt()) && llmResult.getInterrupt().equals(2)) {
            ToolIntentAnswer toolIntentAnswer = llmInvokeUtil.buildLLMSecurityAnswer(llmResult);
            return new ToolMixerAnswer(toolIntentAnswer);
        }

        ToolIntentAnswer toolIntentAnswer = llmInvokeUtil.buildLLMAnswer(llmResult, agentCode);
        ToolMixerAnswer toolMixerAnswer = new ToolMixerAnswer(toolIntentAnswer);
        toolMixerAnswer.setSourceToolAnswers(sourceCandidates);
        return toolMixerAnswer;
    }


    private @Nullable ToolMixerAnswer getLLMAnswerAfterToolResult(StrategyNodeRequest nodeRequest, DagContext context, ToolCall tc, ToolIntentAnswer toolIntentAnswer, AgentStrategyConfig request, LLMInvokeUtil llmInvokeUtil, String messageId) {
        List<WrapLLMMessage> useHistories;
        LLMResult result;
        WrapLLMMessage toolResult = new WrapLLMMessage();
        toolResult.setRole(MessageRoleEnum.tool.getCode());
        toolResult.setName(tc.getFunction().getName());
        toolResult.setContent(JSON.toJSONString(toolIntentAnswer.getToolAnswer().getOutputs()));
        toolResult.setTool_call_id(tc.getId());

        MessageResponseCache toolResponse = BeanUtil.toBean(toolResult, MessageResponseCache.class);

        useHistories = sessionHistoryCacheService.getChatHistoryFromCache(context.getSessionId(),
                context.getMessageId(),
                Optional.ofNullable(request.getAgentConfig()).map(AgentConfig::getModelConfig).map(model -> model.getMemoryCount() + 1).orElse(null),
                false, false);

        sessionHistoryCacheService.addSessionMessageResponse(context.getSessionId(), context.getMessageId(), toolResponse);

        result = llmInvokeUtil.invokeForToolResult(request, Lists.newArrayList(toolIntentAnswer), toolResult, useHistories, messageId);
        if (result != null && result.getLlmMessage() != null) {
            ToolIntentAnswer mixedAnswer = llmInvokeUtil.buildLLMAnswer(result, nodeRequest.getAgentCode());
            ToolMixerAnswer toolMixerAnswer = new ToolMixerAnswer(mixedAnswer);
            toolMixerAnswer.setSourceToolAnswers(Lists.newArrayList(toolIntentAnswer));
            return toolMixerAnswer;
        }
        return null;
    }

    protected ToolIntentAnswer executeWorkflow(String workflowId, DagContext context, ToolCall toolCall, String userQuery, Object userFile) {
        Map<String, Object> inputParamMap = new HashMap<>();
        if (Objects.nonNull(toolCall)) {
            JSONObject argsObj = new JSONObject();
            String args = toolCall.getFunction().getArguments();
            if (StringUtils.isNotBlank(args)) {
                argsObj = JSON.parseObject(args);
            }
            inputParamMap.putAll(argsObj);
        }

        inputParamMap.put("BOT_USER_INPUT", userQuery);
        inputParamMap.put("FILE_USER", userFile);

        Map<String, Object> globalVar = new HashMap<>();
        globalVar.put("BOT_USER_INPUT", userQuery);
        globalVar.put("FILE_USER", userFile);

        DagParam childDagParam = DagParamUtils.buildChildParam(context);
        childDagParam.setGlobalVarMap(globalVar);
        childDagParam.setInputParamMap(inputParamMap);
        DagContext childDagContext = new DagContext();
        childDagContext.setMainFlow(false);
        childDagContext.setDialogStatus(context.getDialogStatus());
        childDagContext.setDagParam(childDagParam);
        DagResult dagResult = dagEngine.execute(workflowId, childDagContext);

        ToolIntentAnswer current = dagResult.getToolAnswer();
        if (Objects.nonNull(current)) {
            if (dagResult.getDagStatus().equals(DagStatusEnum.PAUSE)) {
                current.setDialogState(DialogState.WEAK_PROGRESS);
            } else {
                current.setDialogState(DialogState.COMPLETE);
            }
        }

        if (!CollectionUtils.isEmpty(childDagContext.getToolAnswers())) {
            List<ToolIntentAnswer> historyAnswers = new ArrayList<>(childDagContext.getToolAnswers());
            if (Objects.nonNull(current)) {
                //当前产生了answer，已经推出去过了，那就不把子流程产生的最后一笔，也就是相同的answer加进去
                historyAnswers.remove(historyAnswers.size() - 1);
            }
            if (!CollectionUtils.isEmpty(historyAnswers)) {
                context.addToolAnswers(historyAnswers);
            }
        }

        if (Objects.nonNull(current)) {
            current.setDmInfo(toolCall);
            return current;
        }

        return null;
    }

    protected ToolIntentAnswer executeApi(ToolIntent intent, ToolCall toolCall, String sessionId, boolean test, String agentCode) {
        ExecutePluginApiRequest request = new ExecutePluginApiRequest();
        request.setApiId(intent.getToolIntentId());

        JSONObject argsObj = new JSONObject();
        if (Objects.nonNull(toolCall)) {
            String args = toolCall.getFunction().getArguments();
            if (StringUtils.isNotBlank(args)) {
                argsObj = JSON.parseObject(args);
            }
        }

        request.setInputs(argsObj);
        request.setSessionId(sessionId);
        request.setAgentTest(test);
        request.setAgentCode(agentCode);
        Result<ExecutePluginApiResponse> result = executePluginRpcApi.executeApi(request);
        if (result == null || result.getData() == null || !result.getData().isSuccess()) {
            if (result != null && result.getData() != null) {
                log.warn("插件调用失败：{}", result.getData().getErrorMsg());
            } else {
                log.warn("插件调用没有输出结果");
            }
            return null;
        }

        ToolAnswer toolAnswer = new ToolAnswer();
        Map<String, Object> outputs = Maps.newHashMap();
        outputs.put("responseParam", result.getData().getResponseParam());
        toolAnswer.setOutputs(outputs);
        toolAnswer.setAnswerType(AnswerTypeEnum.PLAIN_TEXT);
        toolAnswer.setResponseType(AnswerResponseType.LLM);

        ToolIntentAnswer toolIntentAnswer = new ToolIntentAnswer();
        toolIntentAnswer.setDmInfo(toolCall);
        toolIntentAnswer.setToolIntentId(intent.getToolIntentId());
        toolIntentAnswer.setToolIntentName(intent.getToolIntentName());
        toolIntentAnswer.setDialogEngineType(DialogEngineType.PLUGIN);
        toolIntentAnswer.setToolAnswer(toolAnswer);
        return toolIntentAnswer;
    }

    private ToolMixerAnswer invokeBI(ToolIntentAnswer toolChoice, StrategyNodeContext strategyNodeContext, DagContext context) {
        ToolIntentAnswer biRealAnswer = executeBiAnswer(toolChoice, strategyNodeContext, context);
        if (Objects.nonNull(biRealAnswer)) {
            ToolMixerAnswer toolMixerAnswer = new ToolMixerAnswer(biRealAnswer);
            toolMixerAnswer.setSourceToolAnswers(Lists.newArrayList(biRealAnswer));
            return toolMixerAnswer;
        }

        return null;
    }

    private ToolIntentAnswer executeBiAnswer(ToolIntentAnswer biIntent, StrategyNodeContext strategyNodeContext, DagContext context) {
        ExecutePluginApiRequest request = new ExecutePluginApiRequest();
        request.setApiId(biIntent.getToolIntentId());
        Map<String, Object> inputs = new HashMap<>();
        inputs.put("query", strategyNodeContext.getStrategyNodeRequest().getQuery());
        inputs.put("selectTable", biIntent.getExtraData().get("selectTable"));
        request.setInputs(inputs);
        request.setSessionId(context.getSessionId());
        request.setMessageId(context.getMessageId());
        request.setDownMessageId(context.getDownMessageId());
        request.setMessageCallback(context.getMessageCallback());
        request.setAgentCode(context.getAgentCode());
        request.setAgentTest(context.getEnv().equals(EnvTypeEnum.TEST));
        ExecutePluginApiResponse executePluginApiResponse = pluginApiConfigService.executePluginApi(request, false);
        if (executePluginApiResponse == null || !executePluginApiResponse.isSuccess()) {
            log.warn("BI 插件调用没有输出结果");
            return null;
        }

        JSONObject object = JSONObject.parseObject(executePluginApiResponse.getResponseParam());
        if (object != null && object.containsKey("data")) {
            JSONObject data = object.getJSONObject("data");
            if (Objects.isNull(data)) {
                return null;
            }
            ToolIntentAnswer toolIntentAnswer = data.getObject("data", ToolIntentAnswer.class);
            toolIntentAnswer.setToolIntentId(biIntent.getToolIntentId());
            toolIntentAnswer.setDialogEngineType(DialogEngineType.PLUGIN);
            toolIntentAnswer.setToolIntentName(biIntent.getToolIntentName());
            ToolAnswer toolAnswer = toolIntentAnswer.getToolAnswer();
            Map<String, Object> outputs = new HashMap<>();
            outputs.putAll(object);
            toolAnswer.setOutputs(outputs);
            return toolIntentAnswer;
        }
        return null;
    }

}
