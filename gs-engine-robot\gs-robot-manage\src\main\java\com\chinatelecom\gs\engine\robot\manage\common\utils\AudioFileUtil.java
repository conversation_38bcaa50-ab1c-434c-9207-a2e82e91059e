package com.chinatelecom.gs.engine.robot.manage.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025年06月04日
 */
@Slf4j
public class AudioFileUtil {

    public static final String STORAGE_DIR = "/usr/src/app/telecom/";

    private static final String FFMPEG_PATH = "ffmpeg";

    private static final long TIMEOUT = 30; // 超时时间（秒）

    /**
     * 将音频字节流保存为 WAV 文件
     *
     * @param audioBytes 音频字节流
     * @return 存储的文件路径
     * @throws IOException 文件写入失败
     */
    public static String saveWavFile(byte[] audioBytes) throws IOException {
        // 创建存储目录（如不存在）
        Path storagePath = Paths.get(STORAGE_DIR);
        if (!Files.exists(storagePath)) {
            Files.createDirectories(storagePath);
        }

        // 生成唯一文件名（避免重复）
        String fileName = UUID.randomUUID() + ".wav";
        Path filePath = storagePath.resolve(fileName);

        // 写入文件（使用 try-with-resources 自动关闭资源）
        try (FileOutputStream fos = new FileOutputStream(filePath.toFile())) {
            fos.write(audioBytes);
        }

        return filePath.toString(); // 返回文件路径
    }

    /**
     * ffmpeg -i input.wav -f s16le -ac 1 -ar 16000 output.pcm
     *
     * @param inputPath  String
     * @param outputPath String
     * @param sampleRate Long
     * @return boolean
     */
    public static boolean convertWithTimeout(String inputPath, String outputPath, Long sampleRate) {
        try {
            ProcessBuilder pb = new ProcessBuilder(
                    FFMPEG_PATH,
                    "-i", inputPath,
                    "-f", "s16le",
                    "-ac", "1",
                    "-ar", sampleRate.toString(),
                    outputPath
            );
            pb.redirectErrorStream(true); // 合并标准输出和错误输出

            Process process = pb.start();

            // 异步读取输出流
            ExecutorService executor = Executors.newSingleThreadExecutor();
            Future<?> stdoutFuture = executor.submit(() -> {
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(process.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        log.info("FFmpeg 输出: " + line);
                    }
                } catch (IOException ignored) {
                    log.error("FFmpeg 输出异常", ignored);
                }
            });

            // 设置超时
            boolean finished = process.waitFor(TIMEOUT, TimeUnit.SECONDS);
            if (!finished) {
                log.info("转换超时，正在终止 FFmpeg 进程...");
                process.destroyForcibly();
                return false;
            }

            stdoutFuture.cancel(true);
            executor.shutdownNow();

            int exitCode = process.exitValue();
            return exitCode == 0;

        } catch (InterruptedException e) {
            log.info("执行 FFmpeg 命令被中断");
            Thread.currentThread().interrupt();
        } catch (IOException e) {
            log.info("执行 FFmpeg 命令失败: " + e.getMessage());
            return false;
        }
        return false;
    }
}
