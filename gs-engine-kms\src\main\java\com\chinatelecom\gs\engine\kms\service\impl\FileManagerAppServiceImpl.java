package com.chinatelecom.gs.engine.kms.service.impl;

import com.alibaba.fastjson2.JSON;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.s3.CloudStorageDao;
import com.chinatelecom.gs.engine.common.s3.ObjectMetadataReq;
import com.chinatelecom.gs.engine.common.s3.ObjectMetadataRes;
import com.chinatelecom.gs.engine.common.utils.*;
import com.chinatelecom.gs.engine.core.manager.param.FileManagerUploadParam;
import com.chinatelecom.gs.engine.core.manager.vo.FileManagerUploadVO;
import com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.kms.dto.EmbeddingInfo;
import com.chinatelecom.gs.engine.kms.flow.parse.component.context.KnowledgeParseConfigContext;
import com.chinatelecom.gs.engine.kms.rpc.split.DocSplitRpc;
import com.chinatelecom.gs.engine.kms.rpc.split.dto.SplitRequest;
import com.chinatelecom.gs.engine.kms.rpc.split.dto.SplitResponse;
import com.chinatelecom.gs.engine.kms.rpc.split.dto.SplitResult;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelecom.gs.engine.kms.sdk.enums.SplitIndexType;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeParseConfig;
import com.chinatelecom.gs.engine.kms.service.FileManagerAppService;
import com.chinatelecom.gs.engine.kms.service.ModelAppService;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.net.MediaType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.cos.*;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.interactive.action.PDAction;
import org.apache.pdfbox.pdmodel.interactive.action.PDActionJavaScript;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024年12月26日
 */
@RefreshScope
@Service
@Validated
@Slf4j
public class FileManagerAppServiceImpl implements FileManagerAppService {

    @Value("${app.default.checkFileXssSwitch:false}")
    private boolean checkFileXssSwitch;

    @Resource
    private CloudStorageDao cloudStorageDao;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Resource
    private DocSplitRpc docSplitRpc;

    @Resource
    private ModelAppService modelAppService;

    @Override
    public FileManagerUploadVO upload(FileManagerUploadParam param) {
        MultipartFile file = param.getFile();
        if (file == null) {
            BizAssert.throwBizException("A0001", "文件不存在");
        }
        String fileName = Optional.ofNullable(file.getOriginalFilename()).orElse("");
        if (StringUtils.isBlank(fileName)) {
            BizAssert.throwBizException("AA004", "文件名称不能为空");
        }
        if (fileName.length() > gsGlobalConfig.getKmsBizConfig().getFileNameMaxLength()) {
            BizAssert.throwBizException("AA002", "文件名称超过长度：{}", gsGlobalConfig.getKmsBizConfig().getFileNameMaxLength());
        }
        Long maxFileSize = param.getMaxFileSize();
        long maxSize = gsGlobalConfig.getKmsBizConfig().getFileSizeMaxLength();
        if (Objects.nonNull(maxFileSize) && maxFileSize > 0) {
            maxSize = maxFileSize;
        }
        if (file.getSize() > maxSize * 1024 * 1024) {
            throw new BizException("AA015", ArrayUtils.toArray(maxSize), "文件大小超过限制超过大小：{}", gsGlobalConfig.getKmsBizConfig().getFileSizeMaxLength());
        }

        KnowledgeType type = KnowledgeType.fromFileName(fileName);
        if (type == null) {
            throw new BizException("AA003", "文件类型不支持");
        }
        String fileKey = String.format("upload/%s/%s%s", type.name().toLowerCase(), IdGenerator.id(), FileNameUtils.getFileSuffixWithPoint(fileName));

        InputStream fileInputStream = null;
        try {
            fileInputStream = file.getInputStream();
            cloudStorageDao.upload(fileKey, fileInputStream, file.getSize(), file.getContentType());
        } catch (IOException e) {
            BizAssert.throwBizException("AA016", "上传文件异常");
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error("关闭流异常", e);
                }
            }
        }

        return new FileManagerUploadVO(fileKey, BaseFileUrlUtils.getBaseDownloadUrl((fileKey)));
    }

    /**
     * 检查PDF文档是否包含可疑的JavaScript脚本
     * @param fileInputStream PDF文件输入流
     * @return true-包含可疑脚本 false-不包含可疑脚本
     */
    public boolean checkPdfForMaliciousScript(InputStream fileInputStream) {
        try (PDDocument document = PDDocument.load(fileInputStream)) {
            // 1. 检查页面级别JavaScript
            if (checkPageLevelJavaScript(document)) {
                return true;
            }

            // 2. 检查文档级别OpenAction JavaScript
            if (checkDocumentOpenAction(document)) {
                return true;
            }

            // 3. 检查文档级别JavaScript名称树
            if (checkJavaScriptNameTree(document)) {
                return true;
            }
            log.debug("未检测到可疑JavaScript脚本");
            return false;
        } catch (Exception e) {
            log.error("PDF解析未知错误", e);
            return false;
        }
    }

    /**
     * 检查所有页面的JavaScript动作
     */
    private boolean checkPageLevelJavaScript(PDDocument document) throws IOException {
        for (int i = 0; i < document.getNumberOfPages(); i++) {
            if (checkPageForJavaScript(document.getPage(i))) {
                log.warn("在页面{}发现可疑脚本", i);
                return true;
            }
        }
        return false;
    }

    /**
     * 检查文档OpenAction中的JavaScript
     */
    private boolean checkDocumentOpenAction(PDDocument document) throws IOException {
        PDAction action = (PDAction) document.getDocumentCatalog().getOpenAction();
        if (action instanceof PDActionJavaScript) {
            String script = ((PDActionJavaScript) action).getAction();
            if (isSuspiciousScript(script)) {
                log.error("发现文档级XSS攻击脚本: {}", script);
                return true;
            }
        }
        return false;
    }

    /**
     * 检查JavaScript名称树
     */
    private boolean checkJavaScriptNameTree(PDDocument document) {
        COSDictionary namesDict = document.getDocumentCatalog().getCOSObject().getCOSDictionary(COSName.NAMES);
        if (namesDict == null) {
            return false;
        }

        COSDictionary jsDict = namesDict.getCOSDictionary(COSName.JAVA_SCRIPT);
        if (jsDict == null || !jsDict.containsKey(COSName.NAMES)) {
            return false;
        }

        COSArray jsNames = jsDict.getCOSArray(COSName.NAMES);
        for (int i = 0; i < jsNames.size(); i++) {
            COSBase cosBase = jsNames.get(i);
            if (cosBase instanceof COSDictionary) {
                COSDictionary cosDictionary = (COSDictionary) cosBase;
                if (!cosDictionary.containsKey(COSName.JS)) {
                    continue;
                }

                String script = cosDictionary.getItem(COSName.JS).toString();
                if (isSuspiciousScript(script)) {
                    log.error("在名称树中发现XSS攻击脚本[索引{}]: {}", i, script);
                    return true;
                }
            }
            if (cosBase instanceof COSObject) {
                COSObject cosObject = (COSObject) cosBase;
                COSBase item = cosObject.getItem(COSName.JS);
                if (item instanceof COSString) {
                    String string = ((COSString) item).getString();
                    if (isSuspiciousScript(string)) {
                        log.error("在名称树中发现XSS攻击脚本[索引{}]: {}", i, string);
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 检查单个页面中是否包含 JavaScript 动作。
     *
     * @param page 页面对象
     * @return 如果页面中包含 JavaScript 返回 true，否则返回 false
     */
    private boolean checkPageForJavaScript(org.apache.pdfbox.pdmodel.PDPage page) {
        if (page.getActions() != null && page.getActions().getO() instanceof PDActionJavaScript) {
            PDActionJavaScript action = (PDActionJavaScript) page.getActions().getO();
            String script = action.getAction();
            if (isSuspiciousScript(script)) {
                log.error("发现潜在的 XSS 攻击脚本: {}", script);
                return true;
            }
        }
        return false;
    }

    private boolean isSuspiciousScript(String script) {
        if (StringUtils.isBlank(script)) {
            return false;
        }
        // 定义可疑的 JavaScript 关键字列表
        String[] suspiciousKeywords = {
                "app.alert",
                "this.submitForm",
                "this.submitURL",
                "this.gotoURL",
                "util.http",
                "eval",
                "new Function",
                "xfa.host.messageBox",
                "this.exportDataObject",
                "this.print",
                "this.saveAs"
        };

        for (String keyword : suspiciousKeywords) {
            if (script.contains(keyword)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public void download(String fileKey, String fileName, boolean inline, HttpServletResponse response) {
        download(fileKey, fileName, inline, false, response);
    }

    @Override
    public void download(String fileKey, String fileName, boolean inline, boolean rangeDownload, HttpServletResponse response) {
        String downloadFileName = StringUtils.defaultIfBlank(fileName, FileNameUtils.getFileName(fileKey));
        InputStream inputStream = null;

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String range = request.getHeader("Range");
        if (StringUtils.isNotEmpty(range) || rangeDownload) {
            ObjectMetadataReq req = new ObjectMetadataReq();
            req.setFileKey(fileKey);
            if (StringUtils.isNotEmpty(range)) {
                String[] ranges = range.replaceAll("[^0-9\\-]", "").split("-");
                req.setStart(Long.parseLong(ranges[0]));
                req.setEnd(ranges.length > 1 ? Long.parseLong(ranges[1]) : null);
            } else if (rangeDownload) {
                req.setStart(0L);
            }

            ObjectMetadataRes res = cloudStorageDao.download(req);
            response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
            response.setHeader("Content-Range", res.buildContentRange());
            response.setHeader("Accept-Ranges", "bytes");
            response.setContentLengthLong(res.buildCurrentLength());
            inputStream = res.getInputStream();
        } else {
            inputStream = cloudStorageDao.download(fileKey);
        }

        MediaType mediaType = MediaTypeUtil.fromFileSuffix(FileNameUtils.getFileSuffix(fileKey));
        if (mediaType != null) {
            response.setContentType(mediaType.toString());
        }
        response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "ETag,Accept-Ranges,Content-Length,Content-Range");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET,POST,OPTIONS");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "range,Accept-Ranges,Content-Range,Content-Type,Authorization,X-CAF-Authorization-Token,sessionToken,X-TOKEN,Cache-Control,If-Modified-Since");

        HttpFileUtils.download(inputStream, downloadFileName, inline, response);
    }

    @Override
    public InputStream download(String fileKey){
        return cloudStorageDao.download(fileKey);
    }

    @Override
    public void download(String fileKey, String fileName, HttpServletResponse response) {
        String downloadFileName = StringUtils.defaultIfBlank(fileName, FileNameUtils.getFileName(fileKey));
        InputStream inputStream = cloudStorageDao.download(fileKey);

        MediaType mediaType = MediaTypeUtil.fromFileSuffix(FileNameUtils.getFileSuffix(fileKey));
        if (mediaType != null) {
            response.setContentType(mediaType.toString());
        }
        response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "ETag,Accept-Ranges,Content-Length,Content-Range");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "GET,POST,OPTIONS");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, "Content-Type,Authorization");

        HttpFileUtils.download(inputStream, downloadFileName, true, response);
    }

    @Override
    public void delete(String fileKey) {
        cloudStorageDao.remove(fileKey);
    }

    @Override
    public void delete(List<String> fileKeys) {
        cloudStorageDao.remove(fileKeys);
    }

    @Override
    public void checkFile(FileManagerUploadVO upload, String originalFilename) {
        String fileName = FileNameUtils.getFileNameNoSuffix(originalFilename);
        String fileType = FileNameUtils.getFileSuffix(originalFilename);
        // 不支持解析的文件不进行检查
        if (KnowledgeType.from(fileType) == null) {
            return;
        }

        // pdf文件单独处理
        if (Objects.equals(fileType, "pdf")) {
            boolean result = checkPdfForMaliciousScript(download(upload.getFileKey()));
            if (result) {
                cloudStorageDao.remove(upload.getFileKey());
                throw new BizException("AE025", "文件中存在非法内容");
            }
            return;
        }

        if (!checkFileXssSwitch) {
            log.info("checkFileXssSwitch is off, 文件解析跳过xss检查");
            return;
        }

        SplitRequest splitRequest = buildSplitRequest(fileName, upload.getFileKey(), fileType);
        SplitResponse splitResponse;
        try {
            splitResponse = docSplitRpc.split(splitRequest);
        } catch (Exception e) {
            log.error("文件解析异常", e);
            cloudStorageDao.remove(upload.getFileKey());
            throw new BizException("AE019", "文件解析失败");
        }
        if (Objects.isNull(splitResponse) || StringUtils.isBlank(splitResponse.getContent())) {
            throw new BizException("AE019", "文件解析失败");
        }
        List<SplitResult> splitResults = splitResponse.getSplitResults();
        if (CollectionUtils.isNotEmpty(splitResults)) {
            for (SplitResult splitResult : splitResults) {
                String content = splitResult.getContent();
                // 检测内容是否存在xss攻击
                if (isSuspiciousScript(content)) {
                    cloudStorageDao.remove(upload.getFileKey());
                    throw new BizException("AE025", "文件中存在非法内容");
                }
            }
        }
    }

    private KnowledgeParseConfigContext getDefaultContext() {
        KnowledgeParseConfigContext context = new KnowledgeParseConfigContext();
        KnowledgeParseConfig parseConfig = JSON.parseObject(gsGlobalConfig.getKmsBaseConfig().getDefaultConfig(), KnowledgeParseConfig.class);
        if (parseConfig == null) {
            parseConfig = new KnowledgeParseConfig();
            parseConfig.setModelUrlList(new KnowledgeParseConfig.ModelUrlList());
            parseConfig.setChunkConfig(new KnowledgeParseConfig.ChunkConfig());
            parseConfig.setParserConfig(new KnowledgeParseConfig.ParserConfig());
        }
        //设置cpu解析为false
        parseConfig.getParserConfig().setCpuFlag(Boolean.FALSE);
        parseConfig.getChunkConfig().setSemanticChunkFlag(Boolean.FALSE);
        context.setKnowledgeBaseConfig(parseConfig);
        context.setReExecute(false);
        Map<ModelTypeEnum, ModelPageListParam> modelMap = modelAppService.getDefaultModel();
        for (Map.Entry<ModelTypeEnum, ModelPageListParam> entry : modelMap.entrySet()) {
            ModelPageListParam modelPageListParam = entry.getValue();
            if (modelPageListParam != null) {
                setModelUrl(entry.getKey(), parseConfig.getModelUrlList(), modelPageListParam);
            }
        }
        EmbeddingInfo embeddingInfo = modelAppService.queryDefaultEmbedding(null);
        if(Objects.nonNull(embeddingInfo)){
            ModelPageListParam modelPageListParam = modelAppService.queryByModelCode(embeddingInfo.getModelCode());
            setModelUrl(ModelTypeEnum.EMBEDDING, parseConfig.getModelUrlList(), modelPageListParam);
        }
        return context;
    }

    private SplitRequest buildSplitRequest(String fileName, String fileKey, String type) {
        SplitRequest request = new SplitRequest();
        request.setName(fileName);
        request.setKnowledgeType(KnowledgeType.from(type));
        request.setOriginalFileKey(fileKey);
        request.setIndexType(SplitIndexType.LIGHTWEIGHT);
        request.setKnowledgeParseConfig(getDefaultContext());
        return request;
    }

    private void setModelUrl(ModelTypeEnum modelTypeEnum, KnowledgeParseConfig.ModelUrlList modelUrlList, ModelPageListParam modelPageListParam) {
        switch (modelTypeEnum) {
            case OFFLINE_LLM:
                modelUrlList.setLlmUrl(modelPageListParam.getExternalModelUrl());
                break;
            case ASR:
                modelUrlList.setAsrUrl(modelPageListParam.getExternalModelUrl());
                break;
            case OCR:
                modelUrlList.setOcrUrl(modelPageListParam.getExternalModelUrl());
                break;
            case LAYOUT:
                modelUrlList.setLayoutUrl(modelPageListParam.getExternalModelUrl());
                break;
            case VLLM:
                modelUrlList.setVllmModelUrl(modelPageListParam.getExternalModelUrl());
                break;
            case RERANK:
            case ENTITY:
            case SETR:
            case CLASSIFIER:
            case EMBEDDING:
            default:
        }
    }
}
