package com.chinatelecom.gs.engine.robot.dialog.execute.service.action.engine.inner;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONPath;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.enums.InternalPluginNameEnum;
import com.chinatelecom.gs.engine.core.corekit.common.core.AnswerService;
import com.chinatelecom.gs.engine.core.corekit.common.core.RequestNodeCache;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.SystemDirectStartExtraDTO;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.secure.AgentSecureCheck;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.QueryWorkflowBindRequest;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentWorkflowConfigService;
import com.chinatelecom.gs.engine.robot.manage.info.service.PluginBindCardConfigService;
import com.chinatelecom.gs.engine.robot.sdk.dto.SourceInfo;
import com.chinatelecom.gs.engine.robot.sdk.enums.DialogState;
import com.chinatelecom.gs.engine.robot.sdk.enums.IntentRetrieveType;
import com.chinatelecom.gs.engine.robot.sdk.enums.InteractionTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.rpc.PluginBindAppApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.agent.engine.DialogEngineService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.engine.request.DialogEngineRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.engine.response.DialogEngineResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.common.Header;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.DmToolIntent;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntentAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.LLMSourceTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.*;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.request.AnswerBuildRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.model.PluginBindMappingRule;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.BindCardQueryRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.PluginBindCardResponse;
import com.chinatelecom.gs.workflow.core.domain.param.BotWorkflowDetailRsp;
import com.chinatelecom.gs.workflow.core.workflow.core.DagEngine;
import com.chinatelecom.gs.workflow.core.workflow.core.context.DagContext;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.DagStatusEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.EnvTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.WorkflowTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.model.dag.DagParam;
import com.chinatelecom.gs.workflow.core.workflow.core.model.entity.Tool;
import com.chinatelecom.gs.workflow.core.workflow.core.model.result.DagResult;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;


@Service
@Slf4j
public class UserStrategyEngineService implements DialogEngineService {


    @Resource
    private DagEngine dagEngine;

    @Resource
    private AnswerService answerService;

    @Resource
    private PluginBindAppApi pluginBindAppApi;

    @Resource
    private AgentWorkflowConfigService agentWorkflowConfigService;

    @Resource
    private RequestNodeCache requestNodeCache;

    @Resource
    private AgentSecureCheck agentSecureCheck;

    @Resource
    private PluginBindCardConfigService pluginBindCardConfigService;

    @Override
    public DialogEngineResponse getResponse(DialogEngineRequest dialogEngineRequest) {

        BotWorkflowDetailRsp strategyFlow = this.fetchCurrentStrategyFlow(dialogEngineRequest);
        if (Objects.isNull(strategyFlow)) {
            return null;
        }


        Header header = dialogEngineRequest.getHeader();
        String messageId = header.getTrack().getMessageId();
        String sessionId = header.getTrack().getSessionId();

        Map<String, Object> inputs = new HashMap<>();
        inputs.put("BOT_USER_INPUT", dialogEngineRequest.getProcessContent());
        inputs.put("FILE_USER", dialogEngineRequest.getHeader().getClient().getFileCodes());
        inputs.put("INPUT_EXTRA", dialogEngineRequest.getExtraData());

        Map<String, Object> globalVar = new HashMap<>();
        globalVar.put("BOT_USER_INPUT", dialogEngineRequest.getProcessContent());
        globalVar.put("FILE_USER", dialogEngineRequest.getHeader().getClient().getFileCodes());
        globalVar.put("INPUT_EXTRA", dialogEngineRequest.getExtraData());

        Tool directTool = this.directTool(dialogEngineRequest.getExtraData());

        DagParam param = DagParam.builder()
                .sessionId(sessionId)
                .messageId(messageId)
                .interactionType(InteractionTypeEnum.ASYNC)
                .messageCallback(dialogEngineRequest.getMessageCallback())
                .actionCallBack(dialogEngineRequest.getActionCallBack())
                .userId(RequestContext.get().getUserId())
                .appCode(RequestContext.getAppCode())
                .agentCode(dialogEngineRequest.getAgentCode())
                .isSystemAgent(dialogEngineRequest.getIsSystemAgent())
                .env(dialogEngineRequest.getHeader().getClient().getTest() ? EnvTypeEnum.TEST : EnvTypeEnum.PRODUCT)
                .globalVarMap(globalVar)
                .inputParamMap(inputs)
                .agentCode(dialogEngineRequest.getAgentCode())
                .downMessageId(header.getTrack().getDownMessageId())
                .chatType(dialogEngineRequest.getChatType())
                .safeFenceSwitch(dialogEngineRequest.getHeader().getClient().getSafeFenceSwitch())
                .chatLLMSwitch(dialogEngineRequest.getHeader().getClient().getChatLLMSwitch())
                .build();

        if (Objects.nonNull(directTool)) {
            param.setDirectTool(directTool);
        }

        DagContext dagContext = new DagContext();
        dagContext.setDagParam(param);
        DagResult dagResult = dagEngine.execute(strategyFlow.getWorkflowId(), dagContext);

        //执行完成后，找到结束节点
        if (DagStatusEnum.FINISH.equals(dagContext.getDagInfo().getDagStatusEnum())
                || DagStatusEnum.PAUSE.equals(dagContext.getDagInfo().getDagStatusEnum())) {
            List<ToolIntentAnswer> allAnswers = new ArrayList<>();
            if (!CollectionUtils.isEmpty(dagContext.getToolAnswers())) {
                allAnswers.addAll(dagContext.getToolAnswers());
            }

            ToolIntentAnswer current = dagResult.getToolAnswer();
            if (Objects.nonNull(current)
                    && Objects.nonNull(current.getToolAnswer())
                    && Objects.nonNull(current.getToolAnswer().getAnswerType())) {
                //todo  如果这里输出的answer还是需要大模型融合的，则不处理了
                if (current.getToolAnswer().getResponseType().equals(AnswerResponseType.DIRECT)) {
                    allAnswers.add(current);
                }
            }

            if (CollectionUtils.isEmpty(allAnswers)) {
                return null;
            }

            List<AgentAnswer> agentAnswers = new ArrayList<>();
            for (ToolIntentAnswer toolIntentAnswer : allAnswers) {
                ToolAnswer toolAnswer = toolIntentAnswer.getToolAnswer();
                log.info("【CUSTOM】 当前tool answer {}", JSON.toJSONString(toolAnswer));
                AnswerBuildRequest answerBuildRequest = new AnswerBuildRequest();
                answerBuildRequest.setAnswerTypeEnum(toolAnswer.getAnswerType());
                answerBuildRequest.setContent(toolAnswer.getContent());
                answerBuildRequest.setInstructions(toolAnswer.getInstructions());
                answerBuildRequest.setEnableSmartInterruption(toolAnswer.getEnableSmartInterruption());
                // 补充reasoning信息封装
                if (Objects.nonNull(toolAnswer.getReasoning())) {
                    answerBuildRequest.setReasoningContent(toolAnswer.getReasoning().getReasoningContent());
                }

                Answer answer = this.answerService.buildAnswer(answerBuildRequest);
                answer.setMessageId(toolAnswer.getMessageId());
                answer.setExtraData(toolAnswer.getExtraData());
                answer.setActions(toolAnswer.getActions());

                if (toolIntentAnswer instanceof ToolMixerAnswer) {
                    ToolMixerAnswer toolMixerAnswer = (ToolMixerAnswer) toolIntentAnswer;
                    List<SourceInfo> sourceInfos = buildSourceInfos(header, dialogEngineRequest.getAgentCode(), toolMixerAnswer.getSourceToolAnswers());
                    if (!CollectionUtils.isEmpty(sourceInfos)) {
                        answer.setSourceInfos(sourceInfos);
                    }
                } else {
                    SourceInfo sourceInfo = buildPluginSourceInfo(header, dialogEngineRequest.getAgentCode(), toolIntentAnswer);
                    if (Objects.nonNull(sourceInfo)) {
                        answer.setSourceInfos(Collections.singletonList(sourceInfo));
                    }
                }
                AgentAnswer agentAnswer = BeanUtil.toBean(answer, AgentAnswer.class);

                DmToolIntent answerIntent = new DmToolIntent();
                if(DagStatusEnum.FINISH.equals(dagContext.getDagInfo().getDagStatusEnum())){
                    answerIntent.setDialogState(DialogState.COMPLETE);
                }else{
                    answerIntent.setDialogState(DialogState.IN_PROGRESS);
                }
                answerIntent.setToolIntentId(toolIntentAnswer.getToolIntentId());
                answerIntent.setDialogState(toolIntentAnswer.getDialogState());
                answerIntent.setDialogEngineType(DialogEngineType.WORKFLOW);
                agentAnswer.setAnswerSourceType(AnswerSourceType.LLM);

                agentAnswer.setDmToolIntent(answerIntent);
                agentAnswers.add(agentAnswer);
            }


            DialogEngineResponse dialogEngineResponse = new DialogEngineResponse(true);
            dialogEngineResponse.setAgentAnswers(agentAnswers);
            return dialogEngineResponse;


        } else {
            //执行失败
            log.error("工作流执行失败: {}, sessionId:{}, messageId: {}", strategyFlow.getWorkflowId(), sessionId, messageId);
        }
        return null;
    }


    @Override
    public String getName() {
        return DialogEngineType.CUSTOM.getCode();
    }


    private Tool directTool(Map<String, Object> extraData) {
        Object toolChoice = extraData.get("toolChoice");
        if (Objects.isNull(toolChoice)) {
            return null;
        }

        String toolStr = JSONUtil.toJsonStr(toolChoice);
        SystemDirectStartExtraDTO systemDirectStartExtraDTO = JSONUtil.toBean(toolStr, SystemDirectStartExtraDTO.class);
        if (Objects.isNull(systemDirectStartExtraDTO) || StringUtils.isEmpty(systemDirectStartExtraDTO.getToolId()) || StringUtils.isEmpty(systemDirectStartExtraDTO.getToolType())) {
            return null;
        }

        return new Tool(systemDirectStartExtraDTO.getToolId(), systemDirectStartExtraDTO.getToolType());
    }


    private List<SourceInfo> buildSourceInfos(Header header, String agentCode, List<ToolIntentAnswer> toolAnswers) {
        if (CollectionUtils.isEmpty(toolAnswers)) {
            return new ArrayList<>();
        }
        Map<LLMSourceTypeEnum, TreeMap<String, SourceInfo>> sourceMaps = Maps.newTreeMap();

        Map<String, SourceInfo.Candidate> docCandidateMap = Maps.newHashMap();

        List<SourceInfo> sourceInfos = new ArrayList<>();
        for (int i = 0; i < toolAnswers.size(); i++) {
            ToolIntentAnswer toolIntentAnswer = toolAnswers.get(i);

            //二次总结的情况，把原始的sourceInfo取出来
            if (toolIntentAnswer.getDialogEngineType().equals(DialogEngineType.LLM) && toolIntentAnswer instanceof ToolMixerAnswer) {
                ToolMixerAnswer toolMixerAnswer = (ToolMixerAnswer) toolIntentAnswer;
                List<SourceInfo> childSources = buildSourceInfos(header, agentCode, toolMixerAnswer.getSourceToolAnswers());
                if (!CollectionUtils.isEmpty(childSources)) {
                    sourceInfos.addAll(childSources);
                    continue;
                }
            }


            if (toolIntentAnswer.getDialogEngineType().equals(DialogEngineType.KMS)) {
                if (Objects.isNull(toolIntentAnswer.getContent())) {
                    continue;
                }
                buildKmsSource(sourceMaps, toolIntentAnswer, i);
            } else if (toolIntentAnswer.getDialogEngineType().equals(DialogEngineType.PLUGIN)
                    || toolIntentAnswer.getDialogEngineType().equals(DialogEngineType.WORKFLOW)) {


                SourceInfo sourceInfo = buildPluginSourceInfo(header, agentCode, toolIntentAnswer);
                if (Objects.nonNull(sourceInfo)) {
                    LLMSourceTypeEnum llmSourceTypeEnum = LLMSourceTypeEnum.PLUGIN;
                    if (toolIntentAnswer.getDialogEngineType().equals(DialogEngineType.WORKFLOW)) {
                        llmSourceTypeEnum = LLMSourceTypeEnum.WORKFLOW;
                    }
                    TreeMap<String, SourceInfo> sources = sourceMaps.getOrDefault(llmSourceTypeEnum, Maps.newTreeMap());
                    sources.put(sourceInfo.getSourceCode(), sourceInfo);
                    sourceMaps.put(llmSourceTypeEnum, sources);

                }
            } else if (toolIntentAnswer.getDialogEngineType().equals(DialogEngineType.FILE)) {
                //文件应答候选项
                buildFileSource(sourceMaps, docCandidateMap, toolIntentAnswer, i);
            } else if (toolIntentAnswer.getDialogEngineType().equals(DialogEngineType.DATABASE)) {
                LLMSourceTypeEnum llmSourceTypeEnum = LLMSourceTypeEnum.DATABASE;
                TreeMap<String, SourceInfo> sources = sourceMaps.getOrDefault(llmSourceTypeEnum, Maps.newTreeMap());
                SourceInfo sourceInfo = sources.get(toolIntentAnswer.getParentCode());
                if (Objects.isNull(sourceInfo)) {
                    sourceInfo = new SourceInfo();
                    sourceInfo.setSourceType(LLMSourceTypeEnum.DATABASE.getType());
                    sourceInfo.setSourceCode(toolIntentAnswer.getParentCode());
                    sourceInfo.setSourceName(toolIntentAnswer.getParentName());
                    sourceInfo.setCandidates(new ArrayList<>());
                    sources.put(sourceInfo.getSourceCode(), sourceInfo);
                }
                if (!docCandidateMap.containsKey(toolIntentAnswer.getParentCode())) {
                    List<SourceInfo.Candidate> candidates = sourceInfo.getCandidates();
                    SourceInfo.Candidate candidate = SourceInfo.Candidate.builder()
                            .code(toolIntentAnswer.getToolIntentId())
                            .name(toolIntentAnswer.getToolIntentName())
                            .content(toolIntentAnswer.getSimilarContent())
                            .index(i)
                            .build();
                    candidates.add(candidate);
                    docCandidateMap.put(toolIntentAnswer.getParentCode(), candidate);
                }
                sourceMaps.put(llmSourceTypeEnum, sources);
            }
        }

        for (Map.Entry<LLMSourceTypeEnum, TreeMap<String, SourceInfo>> entry : sourceMaps.entrySet()) {
            TreeMap<String, SourceInfo> sourceMap = entry.getValue();
            if (CollectionUtils.isEmpty(sourceMap)) {
                continue;
            }
            sourceInfos.addAll(new ArrayList<>(sourceMap.values()));
        }

        return sourceInfos;
    }

    /**
     * 构造文件候选项
     * @param sourceMaps
     * @param docCandidateMap
     * @param toolAnswer
     * @param i
     */
    private void buildFileSource(Map<LLMSourceTypeEnum, TreeMap<String, SourceInfo>> sourceMaps, Map<String, SourceInfo.Candidate> docCandidateMap, ToolIntentAnswer toolAnswer, int i) {
        LLMSourceTypeEnum llmSourceTypeEnum = LLMSourceTypeEnum.KNOWLEDGE;
        TreeMap<String, SourceInfo> sources = sourceMaps.getOrDefault(llmSourceTypeEnum, Maps.newTreeMap());
        SourceInfo sourceInfo = sources.get(DialogEngineType.FILE.getCode());
        if (Objects.isNull(sourceInfo)) {
            sourceInfo = new SourceInfo();
            sourceInfo.setSourceType(LLMSourceTypeEnum.KNOWLEDGE.getType());
            sourceInfo.setSourceSubType(IntentRetrieveType.EXTERNAL_KMS.getCode());
            sourceInfo.setSourceCode(DialogEngineType.FILE.getCode());
            sourceInfo.setSourceName("用户上传文档");
            sourceInfo.setCandidates(new ArrayList<>());
            sources.put(sourceInfo.getSourceCode(), sourceInfo);
        }
        if (!docCandidateMap.containsKey(toolAnswer.getParentCode())) {
            List<SourceInfo.Candidate> candidates = sourceInfo.getCandidates();
            SourceInfo.Candidate candidate = SourceInfo.Candidate.builder()
                    .code(toolAnswer.getParentCode())
                    .subCode(toolAnswer.getToolIntentId())
                    //这里的name先写死
                    .name(toolAnswer.getToolIntentName())
                    .content(toolAnswer.getSimilarContent())
                    .index(i)
                    .build();
            candidates.add(candidate);
            docCandidateMap.put(toolAnswer.getParentCode(), candidate);
        }
        sourceMaps.put(llmSourceTypeEnum, sources);
    }

    private void buildKmsSource(Map<LLMSourceTypeEnum, TreeMap<String, SourceInfo>> sourceMaps, ToolIntentAnswer toolAnswer, int i) {
        LLMSourceTypeEnum llmSourceTypeEnum = LLMSourceTypeEnum.KNOWLEDGE;
        TreeMap<String, SourceInfo> sources = sourceMaps.getOrDefault(llmSourceTypeEnum, Maps.newTreeMap());
        SourceInfo sourceInfo = sources.get(toolAnswer.getParentCode());
        if (Objects.isNull(sourceInfo)) {
            sourceInfo = new SourceInfo();
            sourceInfo.setSourceType(llmSourceTypeEnum.getType());
            sourceInfo.setSourceCode(toolAnswer.getParentCode());
            sourceInfo.setSourceName(toolAnswer.getParentName());
            sourceInfo.setSourceSubType(toolAnswer.getRetrieveType().getCode());
            sourceInfo.setCandidates(new ArrayList<>());
        }

        List<SourceInfo.Candidate> candidates = sourceInfo.getCandidates();
        SourceInfo.Candidate candidate = SourceInfo.Candidate.builder()
                .subCode(toolAnswer.getSubToolIntentId())
                .code(toolAnswer.getToolIntentId())
                .name(toolAnswer.getToolIntentName())
                .index(i)
                .build();

        Map<String, Object> extraData = toolAnswer.getExtraData();
        candidate.setExtraData(extraData);

        Answer answer = (Answer) toolAnswer.getContent();
        candidate.setContent(this.answerService.getContent(answer.getAnswerType().getCode(), answer.getContent()));
        candidate.setSimilarContent(toolAnswer.getSimilarContent());
        candidates.add(candidate);
        sourceInfo.setCandidates(candidates);

        sources.put(sourceInfo.getSourceCode(), sourceInfo);
        sourceMaps.put(llmSourceTypeEnum, sources);
    }

    private SourceInfo buildPluginSourceInfo(Header header, String agentCode, ToolIntentAnswer toolIntentAnswer) {
        try {

            ToolAnswer toolAnswer = toolIntentAnswer.getToolAnswer();

            if (Objects.isNull(toolAnswer)) {
                if (biAnswer(toolIntentAnswer)) {
                    SourceInfo sourceInfo = new SourceInfo();
                    sourceInfo.setSourceCode(toolIntentAnswer.getToolIntentId());
                    sourceInfo.setSourceName(toolIntentAnswer.getToolIntentName());
                    sourceInfo.setSourceType(toolIntentAnswer.getDialogEngineType().getCode());
                    SourceInfo.Candidate candidate = new SourceInfo.Candidate();
                    candidate.setName(toolIntentAnswer.getToolIntentName());
                    candidate.setContent(toolIntentAnswer.getExtraData().get("tableName").toString());
                    sourceInfo.setCandidates(Collections.singletonList(candidate));
                    return sourceInfo;
                } else {
                    return null;
                }
            }

            Map<String, Object> outputs = toolAnswer.getOutputs();
            if (CollectionUtils.isEmpty(outputs)) {
                return null;
            }

            String outputJson = JSON.toJSONString(outputs);

            BindCardQueryRequest bindCardQueryRequest = getBindCardQueryRequest(header, agentCode, toolIntentAnswer);

            PluginBindCardResponse pluginBindCardResponse = pluginBindCardConfigService.queryBindCard(bindCardQueryRequest);

            if (pluginBindCardResponse != null) {
                SourceInfo sourceInfo = new SourceInfo();
                sourceInfo.setSourceCode(toolIntentAnswer.getToolIntentId());
                sourceInfo.setSourceName(toolIntentAnswer.getToolIntentName());
                sourceInfo.setSourceType(LLMSourceTypeEnum.WORKFLOW.getType());


                PluginBindMappingRule mappingRule = pluginBindCardResponse.getBindMappingRule();
                //单个candidate
                if (pluginBindCardResponse.getBindType().equals(0)) {
                    SourceInfo.Candidate candidate = new SourceInfo.Candidate();
                    candidate.setCode(toolIntentAnswer.getToolIntentId());
                    candidate.setName(JSONPath.eval(outputJson, "$." + mappingRule.getTitle()).toString());
                    candidate.setContent(JSONPath.eval(outputJson, "$." + mappingRule.getContent()).toString());
                    sourceInfo.setCandidates(Collections.singletonList(candidate));
                } else {
                    Object o = JSONPath.eval(outputJson, "$." + pluginBindCardResponse.getBindArray());
                    JSONArray jsonArray = (JSONArray) o;
                    List<SourceInfo.Candidate> candidates = new ArrayList<>();
                    for (int i = 0; i < jsonArray.size(); i++) {
                        String entry = jsonArray.getString(i);
                        SourceInfo.Candidate candidate = new SourceInfo.Candidate();
                        candidate.setCode(toolIntentAnswer.getToolIntentId());
                        candidate.setName(JSONPath.eval(entry, "$." + mappingRule.getTitle()).toString());
                        candidate.setContent(JSONPath.eval(entry, "$." + mappingRule.getContent()).toString());
                        candidates.add(candidate);
                    }
                    sourceInfo.setCandidates(candidates);
                }
                return sourceInfo;
            }

            SourceInfo sourceInfo = new SourceInfo();
            sourceInfo.setSourceCode(toolIntentAnswer.getToolIntentId());
            sourceInfo.setSourceName(toolIntentAnswer.getToolIntentName());
            sourceInfo.setSourceType(toolIntentAnswer.getDialogEngineType().getCode());
            SourceInfo.Candidate candidate = new SourceInfo.Candidate();
            candidate.setName(toolIntentAnswer.getToolIntentName());
            candidate.setContent(outputJson);
            sourceInfo.setCandidates(Collections.singletonList(candidate));
            return sourceInfo;

        } catch (Exception e) {
            log.error("【Bot】 workflow生成source失败 ", e);
            return null;
        }
    }

    private BindCardQueryRequest getBindCardQueryRequest(Header header, String agentCode, ToolIntentAnswer toolIntentAnswer) {
        BindCardQueryRequest bindCardQueryRequest = new BindCardQueryRequest();
        bindCardQueryRequest.setAgentCode(agentCode);
        if (toolIntentAnswer.getDialogEngineType().equals(DialogEngineType.PLUGIN)) {
            bindCardQueryRequest.setBusinessType(0);
            bindCardQueryRequest.setApiId(toolIntentAnswer.getToolIntentId());
        } else {
            bindCardQueryRequest.setBusinessType(1);
            bindCardQueryRequest.setWorkflowId(toolIntentAnswer.getToolIntentId());
        }
        bindCardQueryRequest.setTest(header.getClient().getTest());
        return bindCardQueryRequest;
    }

    private BotWorkflowDetailRsp fetchCurrentStrategyFlow(DialogEngineRequest dialogEngineRequest) {
        QueryWorkflowBindRequest queryWorkflowBindRequest = new QueryWorkflowBindRequest();
        queryWorkflowBindRequest.setAgentCode(dialogEngineRequest.getAgentCode());
        queryWorkflowBindRequest.setTest(dialogEngineRequest.getHeader().getClient().getTest());
        queryWorkflowBindRequest.setPageNo(0);
        queryWorkflowBindRequest.setPageSize(10);
        queryWorkflowBindRequest.setWorkflowTypeList(Collections.singletonList(WorkflowTypeEnum.USER_STRATEGY.getCode()));
        queryWorkflowBindRequest.setIsSystemAgent(dialogEngineRequest.getIsSystemAgent());
        IPage<BotWorkflowDetailRsp> strategyFlowPage = this.agentWorkflowConfigService.queryBindWorkflowForExecute(queryWorkflowBindRequest);
        if (Objects.nonNull(strategyFlowPage) && !CollectionUtils.isEmpty(strategyFlowPage.getRecords())) {
            return strategyFlowPage.getRecords().get(0);
        }
        return null;
    }

    private boolean biAnswer(ToolIntentAnswer toolIntentAnswer){
        return toolIntentAnswer.getDialogEngineType().equals(DialogEngineType.PLUGIN) && toolIntentAnswer.getRetrieveType().equals(IntentRetrieveType.SYSTEM)
                && toolIntentAnswer.getToolIntentName().equals(InternalPluginNameEnum.NL2SQL.getCode());
    }

}
