package com.chinatelecom.gs.engine.channel.openai.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.cloud.platform.client.rpc.UserInfo;
import com.chinatelecom.gs.engine.channel.manage.ChannelSecretManagerService;
import com.chinatelecom.gs.engine.channel.openai.dto.ChatCompletionRequest;
import com.chinatelecom.gs.engine.channel.openai.dto.ChatCompletionStreamResponse;
import com.chinatelecom.gs.engine.channel.openai.service.OpenAICompatService;
import com.chinatelecom.gs.engine.channel.service.dto.ChannelApiSecretDTO;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.InterceptorUtils;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.common.utils.UserInfoUtils;
import com.chinatelecom.gs.engine.kms.common.SseEmitterUTF8;
import com.chinatelecom.gs.engine.robot.dialog.chain.DialogCenterService;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.center.PlatformDialogCenterApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.FinalMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.MessageRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.SseMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.SendMessageTypeEnum;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.UUID;

/**
 * OpenAI API 兼容控制器
 * 实现OpenAI API规范的接口
 */
@Slf4j
@Validated
@RestController
@RequestMapping(value = {Constants.ROBOT_PREFIX + Constants.API_PREFIX + "/agent" + "/v1",
        Constants.ROBOT_PREFIX + Constants.API_PREFIX + Apis.OPEN + "/agent" + "/v1"})
@Tag(name = "OpenAIApi兼容接口", description = "OpenAI API 兼容接口")
public class OpenAICompatController {

    private static final String HEADER_STRING = "Bearer ";

    private static final String ERROR_CODE = "AD046";

    @Resource
    private DialogCenterService dialogCenterService;

    @Resource
    private OpenAICompatService openAICompatService;

    @Resource
    private ChannelSecretManagerService channelSecretManagerService;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;


    /**
     * OpenAI Chat Completion API 兼容接口
     * 根据请求中的stream参数决定是同步返回还是流式返回
     *
     * @param chatRequest OpenAI格式的请求
     * @return 同步时返回ChatCompletionResponse，流式时返回SseEmitter
     */
    @Operation(summary = "OpenAI Chat Completion API 兼容接口", description = "根据请求中的stream参数决定是同步返回还是流式返回")
    @PostMapping(value = "/chat/completions", produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_EVENT_STREAM_VALUE})
    @AuditLog(businessType = "OpenAI Chat", operType = "OpenAI Chat Completion API 兼容接口", operDesc = "OpenAI Chat Completion API 兼容接口", objId="#chatRequest.model")
    public Object chatCompletions(@Valid @RequestBody ChatCompletionRequest chatRequest,HttpServletRequest request) {
        // 设置请求上下文
        setupRequestContext(request);

        boolean isStream = chatRequest.getStream() != null && chatRequest.getStream();

        if (isStream) {
            SseEmitter sseEmitter = new SseEmitterUTF8(PlatformDialogCenterApi.TIMEOUT);

            try {
                // 将OpenAI请求转换为内部MessageRequest
                MessageRequest messageRequest = openAICompatService.convertToMessageRequest(chatRequest);
                log.debug("OPENAI-LOG 请求参数: {}", JsonUtils.toJsonString(chatRequest));
                log.debug("OPENAI-LOG 实际请求参数: {}", JsonUtils.toJsonString(messageRequest));
                // 创建自定义的SSE处理器，用于将内部响应格式转换为OpenAI流式响应格式
                OpenAICompatSseHandler sseHandler = new OpenAICompatSseHandler(sseEmitter, chatRequest);
                dialogCenterService.sseExecute(messageRequest, sseHandler);
            } catch (Exception e) {
                log.error("OPENAI-LOG Stream chat completion error", e);
                try {
                    // 发送错误响应
                    ChatCompletionStreamResponse errorResponse = new ChatCompletionStreamResponse();
                    errorResponse.setId("error-" + UUID.randomUUID().toString());
                    errorResponse.setObject("chat.completion.chunk");
                    errorResponse.setCreated(System.currentTimeMillis() / 1000);
                    errorResponse.setModel(chatRequest.getModel());

                    sseEmitter.send(errorResponse);
                    sseEmitter.complete();
                } catch (IOException ex) {
                    sseEmitter.completeWithError(ex);
                }
            }
            return sseEmitter;
        } else {
            // 同步返回
            MessageRequest messageRequest = openAICompatService.convertToMessageRequest(chatRequest);

            // 调用现有的对话服务
            FinalMessageResponse response = dialogCenterService.syncExecute(messageRequest);

            // 将内部响应转换为OpenAI响应格式
            return openAICompatService.convertToOpenAIResponse(response, chatRequest);
        }
    }


    /**
     * OpenAI兼容的SSE处理器，用于将内部响应转换为OpenAI流式响应格式
     */
    private class OpenAICompatSseHandler extends SseEmitter {
        private final SseEmitter delegate;
        private final ChatCompletionRequest originalRequest;

        public OpenAICompatSseHandler(SseEmitter delegate, ChatCompletionRequest originalRequest) {
            super();
            this.delegate = delegate;
            this.originalRequest = originalRequest;
        }

        @Override
        public void send(Object object, MediaType mediaType) throws IOException {
            if (object instanceof SseMessageResponse) {
                SseMessageResponse response = (SseMessageResponse) object;
                log.debug("OPENAI-LOG 收到SSE: {}", JsonUtils.toJsonString(response));

                ChatCompletionStreamResponse streamResponse = openAICompatService.convertToOpenAIStreamResponse(response, originalRequest);

                // 发送转换后的响应
                log.debug("OPENAI-LOG 实际发生发送的SSE: {}", JsonUtils.toJsonString(streamResponse));

                // 如果是最后一条消息，发送[DONE]标记
                if (response.getEventType() == SendMessageTypeEnum.FINISH) {
                    streamResponse.getChoices().get(0).setFinishReason("stop");
                    //delegate.send(JsonUtils.toJsonString(streamResponse));
                    delegate.send("[DONE]");
                    delegate.complete();
                } else {
                    delegate.send(JsonUtils.toJsonString(streamResponse));
                }

            } else {
                delegate.send(object);
            }
        }

        @Override
        public void send(Object object) throws IOException {
            this.send(object, null);
        }

        @Override
        public synchronized void complete() {
            delegate.complete();
        }

        @Override
        public synchronized void completeWithError(Throwable ex) {
            delegate.completeWithError(ex);
        }
    }


    /**
     * 设置请求上下文信息
     */
    private void setupRequestContext(HttpServletRequest request) throws BizException {

        String authHeader = request.getHeader("Authorization");

        if (StringUtils.isEmpty(authHeader) || !authHeader.startsWith(HEADER_STRING)) {
            // 尝试从api-key头获取
            String apiKeyHeader = request.getHeader("api-key");
            if (StringUtils.isEmpty(apiKeyHeader)) {
                log.error("缺少API密钥");
                throw new BizException("AD046", "缺少API密钥");
            }
            authHeader = HEADER_STRING + apiKeyHeader;
        }

        String apiKey = authHeader.substring(HEADER_STRING.length()).trim();

        ChannelApiSecretDTO secretDTO = channelSecretManagerService.getSecretWithSecret(apiKey);

        if (secretDTO == null) {
            log.error("无效的API密钥");
            throw new BizException("AD046", "无效的API密钥");

        }
        String userId = secretDTO.getCreateId();
        String tenantId = secretDTO.getTenantId();

        UserInfo userInfo = UserInfoUtils.getUserInfo(tenantId, userId);
        if (userInfo == null) {
            log.error("用户信息不存在");
            throw new BizException("AD046", "用户信息不存在");

        }

        // 设置请求上下文
        RequestInfo requestInfo = new RequestInfo();
        RequestContext.set(requestInfo);
        request.setAttribute("agentCode", secretDTO.getAppCode());
        requestInfo.setUserId(userId);
        requestInfo.setTenantId(tenantId);
        requestInfo.setIsSuperTenant(superTenant.equals(tenantId));
        requestInfo.setUserName(userInfo.getName());
        requestInfo.setTeam(UserInfoUtils.getUserTeam());
//        requestInfo.setAppSourceType(AppSourceType.KS);

        InterceptorUtils.setAppCode(request, requestInfo);

    }
}