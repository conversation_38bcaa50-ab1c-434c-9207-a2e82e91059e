package com.chinatelecom.gs.plugin.mcp.sandbox;

import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.util.StrUtil;
import com.chinatelecom.gs.engine.robot.sdk.v2.utils.JsonUtils;
import com.chinatelecom.gs.plugin.mcp.model.SandboxGoRequest;
import com.chinatelecom.gs.plugin.mcp.model.SandboxGoResponse;
import com.chinatelecom.gs.plugin.mcp.sandbox.api.McpScriptApi;
import com.chinatelecom.gs.plugin.mcp.sandbox.common.GoTemplateConstant;
import com.chinatelecom.gs.plugin.mcp.sandbox.common.SandboxTransportParam;
import com.chinatelecom.gs.plugin.mcp.sdk.client.transport.ServerParameters;
import com.chinatelecom.gs.plugin.mcp.sdk.spec.McpClientTransport;
import com.chinatelecom.gs.plugin.mcp.sdk.spec.McpSchema;
import com.chinatelecom.gs.plugin.mcp.sdk.util.Assert;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.jasypt.encryption.pbe.PooledPBEStringEncryptor;
import org.jasypt.encryption.pbe.config.SimpleStringPBEConfig;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
public class SandboxStdioClientTransport implements McpClientTransport{
    private final String apiKey;
    private final String npxRegistry;
    private final String jasyptPassword;
    private McpScriptApi mcpScriptApi;
    private final String token;

    private final String TEMPLATE_PATH = "template/McpGoTemplate.txt";

    private Sinks.Many<McpSchema.JSONRPCMessage> inboundSink;

    private ObjectMapper objectMapper;

    /** Parameters for configuring and starting the server process */
    private ServerParameters params;


    public SandboxStdioClientTransport(ServerParameters params, McpScriptApi mcpScriptApi, SandboxTransportParam sandboxParam) {
        this(params, new ObjectMapper(), mcpScriptApi, sandboxParam);
    }


    public SandboxStdioClientTransport(ServerParameters params, ObjectMapper objectMapper, McpScriptApi mcpScriptApi, SandboxTransportParam sandboxParam) {
        Assert.notNull(params, "The params can not be null");
        Assert.notNull(objectMapper, "The ObjectMapper can not be null");
        Assert.notNull(mcpScriptApi, "The mcpScriptApi can not be null");
        this.inboundSink = Sinks.many().unicast().onBackpressureBuffer();
        this.params = params;
        this.objectMapper = objectMapper;
        this.apiKey = sandboxParam.getApiKey();
        this.npxRegistry = sandboxParam.getNpxRegistry();
        this.token = sandboxParam.getNpmToken();

        this.mcpScriptApi = mcpScriptApi;
        this.jasyptPassword = sandboxParam.getJasyptPassword();
    }

    public void handleIncomingMessages(Function<Mono<McpSchema.JSONRPCMessage>, Mono<McpSchema.JSONRPCMessage>> inboundMessageHandler) {
        this.inboundSink.asFlux()
                .flatMap(message -> Mono.just(message)
                        .transform(inboundMessageHandler)
                        .contextWrite(ctx -> ctx.put("observation", "myObservation")))
                .subscribe();
    }

    @Override
    public Mono<Void> sendMessage(McpSchema.JSONRPCMessage message) {
        if (message instanceof McpSchema.JSONRPCRequest) {
            var request = (McpSchema.JSONRPCRequest) message;
            return sendRequest(request);
        } else if (message instanceof McpSchema.JSONRPCNotification) {
            var notification = (McpSchema.JSONRPCNotification) message;
            return sendNotification(notification);
        }
        return Mono.empty();
    }


    private McpSchema.JSONRPCMessage extractResponse(SandboxGoResponse sandboxGoResponse) {
        String stdout = null;
        if (Objects.nonNull(sandboxGoResponse) && Objects.nonNull(sandboxGoResponse.getData())) {
            stdout = sandboxGoResponse.getData().getStdout();
        }
        McpSchema.JSONRPCMessage responseMessage = null;
        if (StringUtil.isNotBlank(stdout)) {
            Pattern pattern = Pattern.compile(GoTemplateConstant.SPLIT + "(.*?)" + GoTemplateConstant.SPLIT, Pattern.DOTALL);
            Matcher matcher = pattern.matcher(stdout);
            if (matcher.find()) {
                String serverRes = matcher.group(1);
                try {
                    responseMessage = McpSchema.deserializeJsonRpcMessage(this.objectMapper, serverRes);
                } catch (IOException e) {
                    log.info("MCP-Server response解析异常：{}", e.getMessage());
                }
            }
        }
        return responseMessage;
    }

    private Mono<Void> sendRequest(McpSchema.JSONRPCRequest message) {
        SandboxGoRequest request = getRequest(message);
        // 发送请求并处理响应
        try {
            SandboxGoResponse sandboxGoResponse = this.mcpScriptApi.scriptExecutor(request, apiKey);
            McpSchema.JSONRPCMessage responseMessage = extractResponse(sandboxGoResponse);
            if (Objects.nonNull(responseMessage)) {
                this.inboundSink.tryEmitNext(responseMessage);
            }
            return Mono.empty();
        } catch (Exception e) {
            return Mono.error(new RuntimeException("Failed to enqueue message:" + e.getMessage()));
        }
    }

    private Mono<Void> sendNotification(McpSchema.JSONRPCNotification message) {
        SandboxGoRequest request = getNotificationRequest(message);

        // 发送请求并处理响应
        try {
            SandboxGoResponse sandboxGoResponse = this.mcpScriptApi.scriptExecutor(request, apiKey);
            McpSchema.JSONRPCMessage responseMessage = extractResponse(sandboxGoResponse);
            if (Objects.nonNull(responseMessage)) {
                this.inboundSink.tryEmitNext(responseMessage);
            }
            return Mono.empty();
        } catch (Exception e) {
            return Mono.error(new RuntimeException("Failed to enqueue message:" + e.getMessage()));
        } finally {
            inboundSink.tryEmitComplete();
        }
    }

    private SandboxGoRequest getRequest(McpSchema.JSONRPCRequest message) {
        String codeTemp = getTemplate();
        codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.ID, (String)message.getId());
        codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.METHOD, message.getMethod());
        String paramStr = JsonUtils.toJsonString(message.getParams());
        if (StringUtils.isNotBlank(paramStr)) {
            codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.PARAMS, paramStr);
        } else {
            codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.PARAMS, "{}");
        }

        SandboxGoRequest request = new SandboxGoRequest();
        request.setLanguage("go");
        request.setEnableNetwork(true);
        request.setCode(codeTemp);
        return request;
    }

    private SandboxGoRequest getNotificationRequest(McpSchema.JSONRPCNotification message) {
        String codeTemp = getTemplate();
        //notification没有id属性，待测试
        codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.METHOD, message.getMethod());
        String paramStr = JsonUtils.toJsonString(message.getParams());
        if (StringUtils.isNotBlank(paramStr)) {
            codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.PARAMS, paramStr);
        } else {
            codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.PARAMS, "{}");
        }

        SandboxGoRequest request = new SandboxGoRequest();
        request.setLanguage("go");
        request.setEnableNetwork(true);
        request.setCode(codeTemp);
        return request;
    }


    private String getTemplate() {
        //适用二方库
        StringBuilder tempBuilder = new StringBuilder();
        ClassPathResource resource = new ClassPathResource(TEMPLATE_PATH);
        try {
            InputStream inputStream = resource.getStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            String line;
            while ((line = reader.readLine()) != null) {
                tempBuilder.append(line).append("\n"); // 保留换行符，确保代码格式正确
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        String codeTemp = tempBuilder.toString();

        StringBuilder argsBuilder = new StringBuilder();
        argsBuilder.append("\"");
        argsBuilder.append("--registry=" + dec(npxRegistry));
        params.getArgs().forEach(arg->{
            argsBuilder.append("\",\"");
            argsBuilder.append(arg);
        });
        argsBuilder.append("\"");

        StringBuilder envsBuilder = new StringBuilder();
        envsBuilder.append("\"");
        if (!Objects.isNull(params.getEnv()) && params.getEnv().size() == 1) {
            String key = (String)params.getEnv().keySet().toArray()[0];
            String value = params.getEnv().get(key);
            envsBuilder.append(key);
            envsBuilder.append("=");
            envsBuilder.append(value);
        }
        envsBuilder.append("\"");

        codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.CMD, params.getCommand());
        codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.ARGS, argsBuilder.toString());
        codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.ENVS, envsBuilder.toString());
        //根据是否设置token值，判断脚本是否运行config命令
        if (StringUtils.isBlank(token)) {
            codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.TOKEN, "");
            codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.CONFIGON, "false");
        } else {
            codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.TOKEN, dec(token));
            codeTemp = StrUtil.replace(codeTemp, GoTemplateConstant.CONFIGON, "true");
        }
        return codeTemp;
    }


    @Override
    public Mono<Void> closeGracefully() {
        inboundSink.tryEmitComplete();
        return Mono.empty();
    }

    @Override
    public void close() {
        inboundSink.tryEmitComplete();
    }

    @Override
    public <T> T unmarshalFrom(Object data, TypeReference<T> typeRef) {
        return this.objectMapper.convertValue(data, typeRef);
    }

    @Override
    public Mono<Void> connect(Function<Mono<McpSchema.JSONRPCMessage>, Mono<McpSchema.JSONRPCMessage>> handler) {
        return Mono.empty();
    }

    /**
     * 加密
     * @param value
     * @return
     */
    private String enc(String value) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        //加密密码
        config.setPassword(jasyptPassword);
        // 默认配置
        config.setAlgorithm("PBEWITHHMACSHA512ANDAES_256");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        String encrypt = encryptor.encrypt(value);
        return encrypt;
    }

    /**
     * 解密
     * @param value
     * @return
     */
    private String dec(String value) {
        PooledPBEStringEncryptor encryptor = new PooledPBEStringEncryptor();
        SimpleStringPBEConfig config = new SimpleStringPBEConfig();
        //加密密码
        config.setPassword(jasyptPassword);
        // 默认配置
        config.setAlgorithm("PBEWITHHMACSHA512ANDAES_256");
        config.setKeyObtentionIterations("1000");
        config.setPoolSize("1");
        config.setProviderName("SunJCE");
        config.setSaltGeneratorClassName("org.jasypt.salt.RandomSaltGenerator");
        config.setIvGeneratorClassName("org.jasypt.iv.RandomIvGenerator");
        config.setStringOutputType("base64");
        encryptor.setConfig(config);
        return encryptor.decrypt(value);
    }

}
