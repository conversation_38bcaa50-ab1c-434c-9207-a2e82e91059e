package com.chinatelecom.gs.plugin.hub.infra.handler.executor.bi;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.ContentTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.DialogState;
import com.chinatelecom.gs.engine.robot.sdk.enums.IntentRetrieveType;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.plugin.BIPluginRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntentAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.ToolMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.AnswerResponseType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.ToolAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.ToolMixerAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.reason.Reason;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.DialogMessageTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.SendMessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.BufferedSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
@RefreshScope
public class BIStreamOpenApi {

    @Value("${nl2sql.host:}")
    private String biHost;

    @Value("${nl2sql.signKey:}")
    private String signKey;

    @Value("${nl2sql.openapi.chatDataUri:}")
    private String chatDataEndpoint;

    @Value("${nl2sql.openapi.streamUri:}")
    private String streamEndpoint;

    @Value("${nl2sql.isSendSql:false}")
    private Boolean isSendSql;

    @Value("${nl2sql.isSendSummary:false}")
    private Boolean isSendSummary;

    @Value(value = "${nl2sql.subText:您好,，,已,收到,您的,查询,请求,，,正在,思考,...,...,...,，,请,您,耐心,等待,。,<br/>}")
    private String subTextStr;

    private String[] SUB_TEXT;

    @PostConstruct
    public void init() {
        if (subTextStr != null && !subTextStr.isEmpty()) {
            SUB_TEXT = subTextStr.split(",");
        } else {
            SUB_TEXT = new String[]{"您好", "，", "已", "收到", "您的", "查询", "请求", "，", "正在", "思考", ".", ".", ".", "，", "请", "您", "耐心", "等待", "。", "<br/>"};
        }
    }

    private final OkHttpClient client = new OkHttpClient.Builder()
            .readTimeout(0, TimeUnit.SECONDS) // 禁用读取超时
            .callTimeout((long) 60 * 2, TimeUnit.SECONDS)
            .build();

    // 定义常量，避免硬编码
    private static final String CALLBACK_REGEX = "##CALLBACK##(.*?)##CALLBACK##";
    private static final String TEXT_MARKER = "##TEXT##";
    private static final String CALLBACK = "##CALLBACK##";
    private static final String SUMMARY = "##SUMMARY##";
    private static final String ANALYZE = "##ANALYZE##";
    private static final String TABLE_SCORE = "##TABLE_SCORE##";
    private static final LinkedHashMap<String, String> param = new LinkedHashMap<>();

    /**
     * 调用流式bi 取数
     *
     * @param context DagContext
     * @return CompletableFuture<ToolIntentAnswer>
     */
    public CompletableFuture<ToolIntentAnswer> callStreamApi(BIPluginRequest context) {
        CompletableFuture<ToolIntentAnswer> future = new CompletableFuture<>();
        String query = context.getQuery();
        param.put("question", query);

        if (query == null || query.isEmpty() || context == null) {
            log.error("输入参数无效：query={}，context={}", query, context);
            future.completeExceptionally(new IllegalArgumentException("Invalid parameters"));
            return future;
        }

        // 构建请求体
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        Map<String, Object> param = new HashMap<>();
        param.put("question", query);
        param.put("source", "CHAT");
        String usrId = RequestContext.getUserId();
        if (Objects.nonNull(RequestContext.get().getExtraParam("createId"))) {
            usrId = (String) RequestContext.get().getExtraParam("createId");
        }
        param.put("userId", usrId);
        param.put("corpCode", RequestContext.getTenantId());

        if (!ObjectUtils.isEmpty(context.getTableId())) {
            Map<String, Object> table = new HashMap<>();
            table.put("tableId", context.getTableId());
            param.put("tableIds", Collections.singletonList(table));
        }

        //todo 传了sessionId有问题，需要优化
        //param.put("sessionId", context.getSessionId());
        RequestBody body = RequestBody.create(JSONUtil.toJsonStr(param), JSON);
        long requestTime = System.currentTimeMillis();
        String format = CharSequenceUtil.format("{}#{}", requestTime, signKey);
        String encode = DigestUtils.md5DigestAsHex(format.getBytes(StandardCharsets.UTF_8));
        // 构建请求
        Request request = new Request.Builder()
                .url(biHost + streamEndpoint)
                .post(body)
                .addHeader("Connection", "keep-alive")
                .addHeader("Referer", biHost)
                .addHeader("accept", "text/event-stream")
                .addHeader("active-business", context.getActiveBusiness())
                .addHeader("sign", CharSequenceUtil.format("{}#{}", requestTime, encode))
                .build();
        log.info("请求bi:{},body:{}", request, JSONUtil.toJsonStr(param));
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                log.error("请求失败，错误信息：{}", e.getMessage(), e);
                future.completeExceptionally(e);
            }

            @Override
            public void onResponse(Call call, Response response) {
                try (ResponseBody responseBody = response.body()) {
                    long startTime = System.currentTimeMillis();
                    if (!response.isSuccessful()) {
                        String errorMsg = "请求失败，状态码：" + response.code();
                        log.info(errorMsg);
                        future.completeExceptionally(new RuntimeException(errorMsg));
                        return;
                    }

                    BufferedSource source = responseBody.source();
                    StringBuilder callBackIdBuffer = new StringBuilder();
                    StringBuilder textBuffer = new StringBuilder();
                    String callback = null;
                    int textCount = 0;
                    int callbackCount = 0;
                    int summaryCount = 0;
                    int analyzeCount = 0;
                    int tebleScoreCount = 0;
                    int startCount = 1;
                    StringBuilder analyzeText = new StringBuilder();
                    String summaryText = null;
                    String tableScore = null;
                    CountDownLatch sendTipCount = new CountDownLatch(1);
                    while (!source.exhausted()) {
                        String chunk = source.readUtf8Line();
                        if (chunk == null || chunk.isEmpty() || chunk.startsWith("id:")) {
                            // 跳过非数据行
                            continue;
                        }

                        if (chunk.startsWith("data:")) {
                            // 去掉 "data:" 前缀并去除前后空格
                            chunk = chunk.substring(5).trim();
                        }
                        log.info("接收到的数据：{}", chunk);
                        if (chunk.contains(SUMMARY)) {
                            summaryCount++;
                            if (summaryCount > 1) {
                                summaryCount = 0;
                            }
                            continue;
                        }
                        if (chunk.contains(TABLE_SCORE)) {
                            tebleScoreCount++;
                            if (tebleScoreCount == 1) {
                                log.info("第一次tableScore:{}", System.currentTimeMillis());
                            }
                            if (tebleScoreCount > 1) {
                                tebleScoreCount = 0;
                            }
                            continue;
                        }

                        if (chunk.contains(ANALYZE)) {
                            analyzeCount++;
                            if (analyzeCount > 1) {
                                analyzeCount = 0;
                            }
                            continue;
                        }
                        if (tebleScoreCount > 0) {
                            tableScore = chunk;
                            log.info("tableScore:{}", tableScore);
                        }
                        if (analyzeCount > 0) {
                            analyzeText.append(chunk);
                        }
                        if (summaryCount > 0 && Boolean.TRUE.equals(isSendSummary)) {
                            summaryText = chunk;
                        }
                        if (CharSequenceUtil.isNotBlank(tableScore) && summaryCount == 0 && startCount == 1) {
                            // send summary 推送提示语
                            String finalSummaryText = summaryText;
                            CompletableFuture.runAsync(() -> {
                                // 初始化新的字符串数组，避免数组越界问题
                                String[] firstText = new String[SUB_TEXT.length + 1];
                                System.arraycopy(SUB_TEXT, 0, firstText, 0, SUB_TEXT.length);
                                firstText[SUB_TEXT.length] = finalSummaryText;

                                for (int i = 0; i < firstText.length; i++) {
                                    try {
                                        // 模拟延迟操作，使用非阻塞方式替代 Thread.sleep（如果必要）
                                        Thread.sleep(100); // 如果需要进一步优化，可以替换为其他非阻塞机制

                                        // 调用 handleTextChunk 方法，并捕获可能的异常
                                        handleTextChunk(firstText[i], context, startTime);
                                        textBuffer.append(Objects.isNull(firstText[i]) ? "" : firstText[i]);
                                    } catch (InterruptedException e) {
                                        log.error("线程休眠失败，当前处理索引: {}", i, e);
                                        Thread.currentThread().interrupt(); // 恢复中断状态
                                    } catch (Exception e) {
                                        log.error("处理文本块失败，当前处理索引: {}, 文本内容: {}", i, firstText[i], e);
                                    }
                                }
                                sendTipCount.countDown();
                            });
                            startCount = 0;
                            textBuffer.append(Objects.nonNull(summaryText) ? summaryText : "");
                        }
                        // 处理TEXT标记

                        if (chunk.contains(TEXT_MARKER)) {
                            textCount++;
                            if (textCount == 1) {
                                log.info(" 第一次text:{}", System.currentTimeMillis());
                            }
                            if (textCount > 0 && CharSequenceUtil.isBlank(tableScore)) {
                                future.complete(null);
                                return;
                            }
                            continue;
                        }

                        if (textCount > 0 && chunk.contains(CALLBACK)) {
                            // 取callbackId
                            callbackCount++;
                            if (callbackCount == 2) {
                                // 取到callbackId
                                callbackCount = 0;
                            }
                        }
                        if (textCount > 0 && callbackCount < 1 && !chunk.contains(CALLBACK) && CharSequenceUtil.isNotBlank(tableScore)) {
                            if (textCount < 2 && callbackCount == 0 && Boolean.TRUE.equals(isSendSql)) {
                                sendTipCount.await();
                                textBuffer.append(Objects.isNull(chunk) ? "" : chunk);
                                handleTextChunk(chunk, context, startTime);
                            }
                        }

                        // 处理CALLBACK标记
                        if (handleCallbackChunk(chunk, callBackIdBuffer)) {
                            callback = callBackIdBuffer.toString().trim();
                            callBackIdBuffer.setLength(0);
                        }
                    }
                    if (CharSequenceUtil.isNotBlank(tableScore) && CharSequenceUtil.isBlank(callback)) {
                        ToolIntentAnswer toolIntentAnswer = sendEnd(context, startTime);
                        future.complete(toolIntentAnswer);
                        return;
                    }
                    log.info("reasoning：{}", textBuffer);
                    try {
                        if (CharSequenceUtil.isBlank(callback)) {
                            // 没有数据，覆盖之前的消息
                            sendTipCount.await();
                            sendCover(context);
                            future.complete(null);
                            return;
                        }
                        log.info("开始轮询查询结果");
                        ToolIntentAnswer result = processCallbackContent(callback, context, startTime, analyzeText.toString(), summaryText);
                        if (result == null) {
                            log.info("没有查询到结果");
                            ToolIntentAnswer toolIntentAnswer = sendEnd(context, startTime);
                            future.complete(toolIntentAnswer);
                            return;
                        }
                        ToolAnswer toolAnswer = result.getToolAnswer();
                        Reason reason = new Reason();
                        reason.setReasoningContent(textBuffer.toString());
                        toolAnswer.setReasoning(reason);
                        result.setToolAnswer(toolAnswer);
                        log.info("处理后的回调内容：{}", result);
                        future.complete(result);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    } catch (Exception e) {
                        future.completeExceptionally(e);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    future.completeExceptionally(e);
                }
            }
        });

        return future;
    }

    /**
     * 提取callbackId
     *
     * @param chunk  String
     * @param buffer StringBuilder
     * @return boolean
     */
    private boolean handleCallbackChunk(String chunk, StringBuilder buffer) {
        buffer.append(chunk);
        Pattern pattern = Pattern.compile(CALLBACK_REGEX);
        Matcher matcher = pattern.matcher(buffer.toString());
        if (matcher.find()) {
            String callbackContent = matcher.group(1).trim();
            log.info("callbackId：{}", callbackContent);
            buffer.setLength(0);
            buffer.append(callbackContent);
            return true;
        }
        return false;
    }

    private void handleTextChunk(String chunk, BIPluginRequest context, long startTime) {
        ToolIntentAnswer toolIntentAnswer = new ToolIntentAnswer();
        ToolAnswer toolAnswer = new ToolAnswer();
        Reason reasoning = new Reason();
        reasoning.setReasoningContent(chunk);
        reasoning.setCost(String.valueOf(System.currentTimeMillis() - startTime));
        toolAnswer.setReasoning(reasoning);
        toolAnswer.setContent("");
        toolAnswer.setAnswerType(AnswerTypeEnum.NL2SQL);
        toolAnswer.setMessageId(context.getMessageId());
        toolIntentAnswer.setToolAnswer(toolAnswer);
        toolIntentAnswer.setDialogState(DialogState.START);
        toolIntentAnswer.setRetrieveType(IntentRetrieveType.SYSTEM);
        toolIntentAnswer.setDialogEngineType(DialogEngineType.PLUGIN);
        toolIntentAnswer.setResponseType(AnswerResponseType.DIRECT);
        ToolMixerAnswer toolMixerAnswer = new ToolMixerAnswer(toolIntentAnswer);
        ToolMessageResponse toolMessageResponse = ToolMessageResponse.builder()
                .sessionId(context.getSessionId())
                .upMsgId(context.getMessageId())
                .downMsgId(context.getDownMessageId())
                .messageType(DialogMessageTypeEnum.PART)
                .eventType(SendMessageTypeEnum.ADD)
                .toolAnswer(toolMixerAnswer)
                .build();
        if (Objects.nonNull(context.getMessageCallback())) {
            context.getMessageCallback().invoke(toolMessageResponse);
        }
    }

    private void sendCover(BIPluginRequest context) {
        ToolIntentAnswer toolIntentAnswer = new ToolIntentAnswer();
        ToolAnswer toolAnswer = new ToolAnswer();
        Reason reasoning = new Reason();
        reasoning.setReasoningContent("");
        toolAnswer.setReasoning(reasoning);
        toolAnswer.setContent("");
        toolAnswer.setContentType(ContentTypeEnum.ALL.getCode());
        toolAnswer.setAnswerType(AnswerTypeEnum.MARKDOWN);
        toolAnswer.setMessageId(context.getMessageId());
        toolIntentAnswer.setToolAnswer(toolAnswer);
        toolIntentAnswer.setDialogState(DialogState.START);
        toolIntentAnswer.setRetrieveType(IntentRetrieveType.SYSTEM);
        toolIntentAnswer.setDialogEngineType(DialogEngineType.LLM);
        toolIntentAnswer.setResponseType(AnswerResponseType.LLM);
        ToolMixerAnswer toolMixerAnswer = new ToolMixerAnswer(toolIntentAnswer);
        ToolMessageResponse toolMessageResponse = ToolMessageResponse.builder()
                .sessionId(context.getSessionId())
                .upMsgId(context.getMessageId())
                .downMsgId(context.getDownMessageId())
                .messageType(DialogMessageTypeEnum.PART)
                .eventType(SendMessageTypeEnum.COVER)
                .toolAnswer(toolMixerAnswer)
                .build();
        if (Objects.nonNull(context.getMessageCallback())) {
            context.getMessageCallback().invoke(toolMessageResponse);
        }
    }

    /**
     * 轮询获取bi取数结果
     *
     * @param callTaskId 回调ID
     * @param context    上下文信息
     * @return 取数结果
     */
    private ToolIntentAnswer processCallbackContent(String callTaskId, BIPluginRequest context, long startTime, String analyzeText, String summaryText) {
        if (callTaskId == null || callTaskId.isEmpty()) {
            log.error("回调内容为空");
            return null;
        }

        log.info("提取到回调内容：{}, {}", callTaskId.trim(), JSON.toJSONString(RequestContext.get()) );
        String usrId = RequestContext.getUserId();
        if (Objects.nonNull(RequestContext.get().getExtraParam("createId"))) {
            usrId = (String) RequestContext.get().getExtraParam("createId");
        }

//        Object userId = Objects.nonNull(RequestContext.getThirdUserId()) ? RequestContext.getThirdUserId() : (String) RequestContext.get().getExtraParam("createId");
        String format = CharSequenceUtil.format(biHost + chatDataEndpoint,
                callTaskId.trim(), context.getPage(), context.getPageSize(), usrId, RequestContext.getTenantId());
        log.info("查询任务内容：url：{}", format);
        JSONObject taskResult = ChatTaskOpenPoller.pollChatTask(format,
                context.getActiveBusiness(), signKey);
        log.info("处理后的回调内容：{}", taskResult);
        if (Objects.nonNull(taskResult)) {
            taskResult.put("analyze", analyzeText);
            taskResult.put("activeBusiness", context.getActiveBusiness());
            taskResult.put("chatId", callTaskId);
            taskResult.put("summary", summaryText);
        } else {
            taskResult = new JSONObject();
            taskResult.put("analyze", "未查询到相关数据");

        }
        ToolIntentAnswer toolIntentAnswer = new ToolIntentAnswer();
        ToolAnswer toolAnswer = new ToolAnswer();
        Reason reasoning = new Reason();
        reasoning.setReasoningContent("");
        reasoning.setCost(String.valueOf(System.currentTimeMillis() - startTime));
        toolAnswer.setContent(taskResult.toString());
        toolAnswer.setAnswerType(AnswerTypeEnum.NL2SQL);
        toolAnswer.setMessageId(context.getMessageId());
        toolIntentAnswer.setToolAnswer(toolAnswer);
        toolIntentAnswer.setDialogState(DialogState.START);
        toolIntentAnswer.setRetrieveType(IntentRetrieveType.SYSTEM);
        toolIntentAnswer.setDialogEngineType(DialogEngineType.PLUGIN);
        toolIntentAnswer.setToolIntentId(callTaskId);
        toolIntentAnswer.setToolIntentName("NL2SQL");
        toolIntentAnswer.setResponseType(AnswerResponseType.DIRECT);

        ToolMixerAnswer toolMixerAnswer = new ToolMixerAnswer(toolIntentAnswer);
        ToolMessageResponse toolMessageResponse = ToolMessageResponse.builder()
                .sessionId(context.getSessionId())
                .upMsgId(context.getMessageId())
                .downMsgId(context.getDownMessageId())
                .messageType(DialogMessageTypeEnum.PART)
                .eventType(SendMessageTypeEnum.ADD)
                .toolAnswer(toolMixerAnswer)
                .build();
        if (Objects.nonNull(context.getMessageCallback())) {
            context.getMessageCallback().invoke(toolMessageResponse);
        }

        return toolIntentAnswer;
    }

    private ToolIntentAnswer sendEnd(BIPluginRequest context, long startTime) {
        ToolIntentAnswer toolIntentAnswer = new ToolIntentAnswer();
        ToolAnswer toolAnswer = new ToolAnswer();
        Reason reasoning = new Reason();
        reasoning.setReasoningContent("");
        reasoning.setCost(String.valueOf(System.currentTimeMillis() - startTime));
        JSONObject taskResult = new JSONObject();
        taskResult.put("analyze", "未查询到相关数据");
        toolAnswer.setContent(taskResult);
        toolAnswer.setAnswerType(AnswerTypeEnum.NL2SQL);
        toolAnswer.setMessageId(context.getMessageId());
        toolIntentAnswer.setToolAnswer(toolAnswer);
        toolIntentAnswer.setDialogState(DialogState.START);
        toolIntentAnswer.setRetrieveType(IntentRetrieveType.SYSTEM);
        toolIntentAnswer.setDialogEngineType(DialogEngineType.PLUGIN);
        toolIntentAnswer.setToolIntentId(IdGenerator.id());
        toolIntentAnswer.setToolIntentName("NL2SQL");
        toolIntentAnswer.setResponseType(AnswerResponseType.DIRECT);

        ToolMixerAnswer toolMixerAnswer = new ToolMixerAnswer(toolIntentAnswer);
        ToolMessageResponse toolMessageResponse = ToolMessageResponse.builder()
                .sessionId(context.getSessionId())
                .upMsgId(context.getMessageId())
                .downMsgId(context.getDownMessageId())
                .messageType(DialogMessageTypeEnum.PART)
                .eventType(SendMessageTypeEnum.ADD)
                .toolAnswer(toolMixerAnswer)
                .build();
        if (Objects.nonNull(context.getMessageCallback())) {
            context.getMessageCallback().invoke(toolMessageResponse);
        }
        return toolIntentAnswer;
    }
}

