package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelecom.gs.engine.kms.sdk.vo.dify.*;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.BaseItem;
import com.chinatelecom.gs.engine.kms.utils.MetadataConditionEvaluator;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Arrays;
import java.util.Collections;

/**
 * Dify兼容接口功能演示
 */
public class DifyCompatControllerDemo {

    public static void main(String[] args) throws Exception {
        System.out.println("=== Dify兼容接口功能演示 ===\n");

        // 演示1: 基本的metadata条件筛选
        demonstrateBasicFiltering();

        // 演示2: 复杂的逻辑条件
        demonstrateComplexFiltering();

        // 演示3: 增强的metadata响应
        demonstrateEnhancedMetadata();

        // 演示4: 错误响应格式
        demonstrateErrorResponse();
    }

    private static void demonstrateBasicFiltering() {
        System.out.println("1. 基本metadata条件筛选演示");
        System.out.println("----------------------------");

        // 创建测试数据
        BaseItem item = new BaseItem();
        item.setTitle("人工智能技术文档");
        item.setDescription("深度学习和机器学习相关内容");
        item.setKnowledgeType(KnowledgeType.DOCUMENT);
        item.setTags(Arrays.asList("AI", "机器学习", "深度学习"));
        item.setCreateTime(System.currentTimeMillis());

        // 创建筛选条件：标题包含"人工智能"
        MetadataConditionItem condition = new MetadataConditionItem();
        condition.setName(Collections.singletonList("title"));
        condition.setComparisonOperator("contains");
        condition.setValue("人工智能");

        MetadataCondition metadataCondition = new MetadataCondition();
        metadataCondition.setConditions(Collections.singletonList(condition));

        boolean result = MetadataConditionEvaluator.evaluate(item, metadataCondition);
        System.out.println("文档标题: " + item.getTitle());
        System.out.println("筛选条件: 标题包含'人工智能'");
        System.out.println("筛选结果: " + (result ? "匹配" : "不匹配"));
        System.out.println();
    }

    private static void demonstrateComplexFiltering() {
        System.out.println("2. 复杂逻辑条件演示");
        System.out.println("-------------------");

        BaseItem item = new BaseItem();
        item.setTitle("机器学习算法");
        item.setDescription("神经网络相关内容");
        item.setTags(Arrays.asList("算法", "神经网络"));

        // 条件1: 标题包含"深度学习"（不匹配）
        MetadataConditionItem condition1 = new MetadataConditionItem();
        condition1.setName(Collections.singletonList("title"));
        condition1.setComparisonOperator("contains");
        condition1.setValue("深度学习");

        // 条件2: 标签包含"神经网络"（匹配）
        MetadataConditionItem condition2 = new MetadataConditionItem();
        condition2.setName(Collections.singletonList("tags"));
        condition2.setComparisonOperator("contains");
        condition2.setValue("神经网络");

        MetadataCondition orCondition = new MetadataCondition();
        orCondition.setLogicalOperator("or");
        orCondition.setConditions(Arrays.asList(condition1, condition2));

        boolean orResult = MetadataConditionEvaluator.evaluate(item, orCondition);

        MetadataCondition andCondition = new MetadataCondition();
        andCondition.setLogicalOperator("and");
        andCondition.setConditions(Arrays.asList(condition1, condition2));

        boolean andResult = MetadataConditionEvaluator.evaluate(item, andCondition);

        System.out.println("文档标题: " + item.getTitle());
        System.out.println("文档标签: " + item.getTags());
        System.out.println("条件1: 标题包含'深度学习' -> 不匹配");
        System.out.println("条件2: 标签包含'神经网络' -> 匹配");
        System.out.println("OR逻辑结果: " + (orResult ? "匹配" : "不匹配"));
        System.out.println("AND逻辑结果: " + (andResult ? "匹配" : "不匹配"));
        System.out.println();
    }

    private static void demonstrateEnhancedMetadata() throws Exception {
        System.out.println("3. 增强metadata响应演示");
        System.out.println("------------------------");

        // 模拟增强的metadata
        Metadata metadata = new Metadata();
        metadata.setPath("/documents/ai-guide.pdf");
        metadata.setDescription("人工智能入门指南");
        metadata.addMetadata("category", "DOCUMENT");
        metadata.addMetadata("tags", Arrays.asList("AI", "入门", "指南"));
        metadata.addMetadata("create_time", System.currentTimeMillis());
        metadata.addMetadata("file_type", "PDF");
        metadata.addMetadata("knowledge_base", "AI知识库");

        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(metadata);
        System.out.println("增强的metadata JSON:");
        System.out.println(json);
        System.out.println();
    }

    private static void demonstrateErrorResponse() throws Exception {
        System.out.println("4. 错误响应格式演示");
        System.out.println("-------------------");

        // 演示不同类型的错误响应
        DifyErrorResponse[] errors = {
            new DifyErrorResponse(1001, "无效的 Authorization 头格式。预期格式为 `Bearer <api-key>`。"),
            new DifyErrorResponse(1002, "授权失败"),
            new DifyErrorResponse(2001, "知识库不存在"),
            new DifyErrorResponse(500, "内部服务器错误")
        };

        ObjectMapper mapper = new ObjectMapper();
        for (DifyErrorResponse error : errors) {
            String json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(error);
            System.out.println("错误码 " + error.getError_code() + ":");
            System.out.println(json);
            System.out.println();
        }
    }
}
