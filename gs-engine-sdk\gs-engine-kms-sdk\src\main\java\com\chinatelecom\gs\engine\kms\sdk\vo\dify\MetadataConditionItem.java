package com.chinatelecom.gs.engine.kms.sdk.vo.dify;

import lombok.Data;

import java.util.List;

/**
 * Dify元数据筛选条件项
 */
@Data
public class MetadataConditionItem {
    
    /**
     * 需要筛选的 metadata 名称数组
     */
    private List<String> name;
    
    /**
     * 比较操作符
     * 支持的操作符：
     * contains, not contains, start with, end with, is, is not, 
     * empty, not empty, =, ≠, >, <, ≥, ≤, before, after
     */
    private String comparison_operator;
    
    /**
     * 对比值，当操作符为 empty、not empty、null、not null 时可省略
     */
    private String value;
}
