package com.chinatelecom.gs.engine.kms.sdk.vo.dify;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Dify元数据筛选条件项
 * 支持下划线和驼峰命名的兼容性
 */
@Data
public class MetadataConditionItem {

    /**
     * 需要筛选的 metadata 名称数组
     */
    @JsonProperty("name")
    @JsonAlias({"name"})
    private List<String> name;

    /**
     * 比较操作符
     * 支持的操作符：
     * contains, not contains, start with, end with, is, is not,
     * empty, not empty, =, ≠, >, <, ≥, ≤, before, after
     * 支持输入：comparison_operator, comparisonOperator
     * 输出：comparison_operator
     */
    @JsonProperty("comparison_operator")
    @JsonAlias({"comparisonOperator", "comparison_operator"})
    private String comparisonOperator;

    /**
     * 对比值，当操作符为 empty、not empty、null、not null 时可省略
     */
    @JsonProperty("value")
    @JsonAlias({"value"})
    private String value;
}
