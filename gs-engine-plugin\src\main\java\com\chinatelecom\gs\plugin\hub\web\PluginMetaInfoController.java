package com.chinatelecom.gs.plugin.hub.web;

import cn.hutool.core.collection.CollUtil;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.FileUtil;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.PluginMetaRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.QueryPluginParam;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.PluginMetaInfo;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.PluginMetaResponse;
import com.chinatelecom.gs.plugin.hub.application.service.PluginMcpService;
import com.chinatelecom.gs.plugin.hub.application.service.PluginMetaConfigService;
import com.chinatelecom.gs.plugin.hub.model.ImportPluginParam;
import com.chinatelecom.gs.plugin.hub.model.PluginBatchRemoveRequest;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@Tag(name = "插件基础信息管理")
@Validated
@PermissionTag(code = {MenuConfig.MY_PLUGIN, KsMenuConfig.PLUGIN})
@RequestMapping(Constants.PLUGIN_API + Constants.WEB_PREFIX + "/plugin")
public class PluginMetaInfoController {

    @Value("#{'${app.plugin.mcp.transports:sse,stdio,streamablehttp}'.split(',')}")
    private List<String> supportedMcpTransports;

    @Autowired
    private PluginMetaConfigService pluginMetaService;

    @Autowired
    private PluginMcpService pluginMcpService;

    @Operation(summary = "创建插件", description = "创建插件基础信息")
    @PlatformRestApi(name = "创建插件", groupName = "插件基础信息管理")
    @PostMapping("/registerPluginMeta")
    @AuditLog(businessType = "插件基础信息管理", operType = "创建插件基础信息", operDesc = "创建插件基础信息", objId="#pluginMetaRequest.pluginName")
    public Result<PluginMetaResponse> create(@RequestBody PluginMetaRequest pluginMetaRequest) {
        String pluginId = pluginMetaService.createPluginMeta(pluginMetaRequest);
        return Result.success(new PluginMetaResponse(pluginId));
    }

    @Operation(summary = "插件列表查询")
    @PlatformRestApi(name = "插件列表查询", groupName = "插件基础信息管理")
    @PostMapping("/getPluginList")
    @AuditLog(businessType = "插件基础信息管理", operType = "查询插件列表", operDesc = "查询插件列表", objId="null")
    public Result<Page<PluginMetaInfo>> getPluginList(@RequestBody QueryPluginParam queryParam) {
        Page<PluginMetaInfo> pluginMetaDTOPage = pluginMetaService.queryPluginList(queryParam);
        return Result.success(pluginMetaDTOPage);
    }

    @Operation(summary = "查询插件信息", description = "根据插件ID查询插件详细信息")
    @PlatformRestApi(name = "查询插件信息", groupName = "插件基础信息管理")
    @GetMapping("/getPluginInfo")
    @AuditLog(businessType = "插件基础信息管理", operType = "查询插件详情", operDesc = "查询插件详情", objId="#pluginId")
    public Result<PluginMetaInfo> getPluginInfo(@RequestParam("pluginId") String pluginId) {
        return Result.success(pluginMetaService.getPluginMetaDetail(pluginId, false));
    }

    @Operation(summary = "更新插件基础信息", description = "更新插件的基础信息，包括名称、描述、图标等")
    @PlatformRestApi(name = "更新插件基础信息", groupName = "插件基础信息管理")
    @PostMapping("/updatePluginMeta")
    @AuditLog(businessType = "插件基础信息管理", operType = "更新插件基础信息", operDesc = "更新插件基础信息", objId="#pluginMetaRequest.pluginId")
    public Result<Boolean> update(@RequestBody PluginMetaRequest pluginMetaRequest) {
        return Result.success(pluginMetaService.updatePluginMeta(pluginMetaRequest));
    }

    @Operation(summary = "刷新插件", description = "刷新插件内容信息")
    @PlatformRestApi(name = "刷新插件", groupName = "插件基础信息管理")
    @PostMapping("/refreshPluginMeta")
    @AuditLog(businessType = "插件基础信息管理", operType = "刷新插件内容信息", operDesc = "刷新插件内容信息", objId="#pluginMetaRequest.pluginId")
    public Result<Boolean> refresh(@RequestBody PluginMetaRequest pluginMetaRequest) {
        return Result.success(pluginMetaService.refreshPluginMeta(pluginMetaRequest));
    }

    @Operation(summary = "删除插件")
    @PlatformRestApi(name = "删除插件", groupName = "插件基础信息管理")
    @DeleteMapping("/remove")
    @AuditLog(businessType = "插件基础信息管理", operType = "删除插件", operDesc = "删除插件", objId="#pluginId")
    public Result<Boolean> remove(@RequestParam("pluginId") String pluginId) {
        return Result.success(pluginMetaService.deletePlugin(pluginId));
    }

    @Operation(summary = "发布插件")
    @PlatformRestApi(name = "发布插件", groupName = "插件基础信息管理")
    @PostMapping("/publishPlugin")
    @AuditLog(businessType = "插件基础信息管理", operType = "发布插件", operDesc = "发布插件", objId="#pluginId")
    public Result<Boolean> publishPlugin(@RequestParam("pluginId") String pluginId) {
        return Result.success(pluginMetaService.publishPlugin(pluginId));
    }

    @Operation(summary = "导入插件")
    @PlatformRestApi(name = "导入插件", groupName = "插件基础信息管理")
    @PostMapping("/importPlugin")
    @AuditLog(businessType = "插件基础信息管理", operType = "导入插件", operDesc = "导入插件", objId="null")
    public Result<ImportPluginParam> importPlugin(@RequestParam("file") MultipartFile multipartFile) {
        FileUtil.checkFileType(multipartFile, Lists.newArrayList(".json",".yaml"));
        return Result.success(pluginMetaService.importPlugin(multipartFile));
    }

    @Operation(summary = "批量删除插件")
    @PlatformRestApi(name = "批量删除插件", groupName = "插件基础信息管理")
    @PostMapping("/batch-remove")
    @AuditLog(businessType = "插件基础信息管理", operType = "批量删除插件", operDesc = "批量删除插件", objId="#pluginMetaRequest.pluginName")
    public Result<Boolean> batchRemove(@RequestBody PluginBatchRemoveRequest request) {
        if (CollUtil.isEmpty(request.getIds())) {
            return Result.success(true);
        }
        return Result.success(pluginMetaService.batchDeletePlugin(request.getIds()));
    }

    @Operation(summary = "查询已支持插件")
    @PlatformRestApi(name = "查询已支持插件", groupName = "插件基础信息管理")
    @GetMapping("/getMcpModes")
    @AuditLog(businessType = "插件基础信息管理", operType = "查询已支持插件", operDesc = "查询已支持插件", objId="#null")
    public Result<List<String>> getModes() {
        return Result.success(supportedMcpTransports);
    }
}
