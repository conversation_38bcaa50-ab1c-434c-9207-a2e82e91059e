package com.chinatelecom.gs.engine.kms.vo;

import com.chinatelecom.gs.engine.kms.sdk.vo.dify.DifySearchRequest;
import com.chinatelecom.gs.engine.kms.sdk.vo.dify.MetadataCondition;
import com.chinatelecom.gs.engine.kms.sdk.vo.dify.MetadataConditionItem;
import com.chinatelecom.gs.engine.kms.sdk.vo.dify.RetrievalSetting;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 命名兼容性测试
 * 验证下划线和驼峰命名的兼容性
 */
public class NamingCompatibilityTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testUnderscoreInputCompatibility() throws Exception {
        // 测试下划线格式的JSON输入
        String underscoreJson = """
            {
                "knowledge_id": "kb-001",
                "query": "人工智能",
                "retrieval_setting": {
                    "top_k": 5,
                    "score_threshold": 0.8
                },
                "metadata_condition": {
                    "logical_operator": "and",
                    "conditions": [
                        {
                            "name": ["category"],
                            "comparison_operator": "is",
                            "value": "DOCUMENT"
                        }
                    ]
                }
            }
            """;

        DifySearchRequest request = objectMapper.readValue(underscoreJson, DifySearchRequest.class);
        
        assertEquals("kb-001", request.getKnowledgeId());
        assertEquals("人工智能", request.getQuery());
        assertEquals(5, request.getRetrievalSetting().getTopK());
        assertEquals(0.8, request.getRetrievalSetting().getScoreThreshold());
        assertEquals("and", request.getMetadataCondition().getLogicalOperator());
        assertEquals("is", request.getMetadataCondition().getConditions().get(0).getComparisonOperator());
    }

    @Test
    public void testCamelCaseInputCompatibility() throws Exception {
        // 测试驼峰格式的JSON输入
        String camelCaseJson = """
            {
                "knowledgeId": "kb-002",
                "query": "机器学习",
                "retrievalSetting": {
                    "topK": 10,
                    "scoreThreshold": 0.6
                },
                "metadataCondition": {
                    "logicalOperator": "or",
                    "conditions": [
                        {
                            "name": ["tags"],
                            "comparisonOperator": "contains",
                            "value": "AI"
                        }
                    ]
                }
            }
            """;

        DifySearchRequest request = objectMapper.readValue(camelCaseJson, DifySearchRequest.class);
        
        assertEquals("kb-002", request.getKnowledgeId());
        assertEquals("机器学习", request.getQuery());
        assertEquals(10, request.getRetrievalSetting().getTopK());
        assertEquals(0.6, request.getRetrievalSetting().getScoreThreshold());
        assertEquals("or", request.getMetadataCondition().getLogicalOperator());
        assertEquals("contains", request.getMetadataCondition().getConditions().get(0).getComparisonOperator());
    }

    @Test
    public void testMixedNamingInputCompatibility() throws Exception {
        // 测试混合命名格式的JSON输入
        String mixedJson = """
            {
                "knowledgeId": "kb-003",
                "query": "深度学习",
                "retrieval_setting": {
                    "topK": 8,
                    "score_threshold": 0.7
                },
                "metadataCondition": {
                    "logical_operator": "and",
                    "conditions": [
                        {
                            "name": ["title", "description"],
                            "comparisonOperator": "contains",
                            "value": "神经网络"
                        }
                    ]
                }
            }
            """;

        DifySearchRequest request = objectMapper.readValue(mixedJson, DifySearchRequest.class);
        
        assertEquals("kb-003", request.getKnowledgeId());
        assertEquals("深度学习", request.getQuery());
        assertEquals(8, request.getRetrievalSetting().getTopK());
        assertEquals(0.7, request.getRetrievalSetting().getScoreThreshold());
        assertEquals("and", request.getMetadataCondition().getLogicalOperator());
    }

    @Test
    public void testOutputFormatConsistency() throws Exception {
        // 测试输出格式的一致性（始终使用下划线格式）
        DifySearchRequest request = new DifySearchRequest();
        request.setKnowledgeId("kb-004");
        request.setQuery("自然语言处理");
        
        RetrievalSetting setting = new RetrievalSetting();
        setting.setTopK(15);
        setting.setScoreThreshold(0.9);
        request.setRetrievalSetting(setting);
        
        MetadataConditionItem conditionItem = new MetadataConditionItem();
        conditionItem.setName(Collections.singletonList("category"));
        conditionItem.setComparisonOperator("is");
        conditionItem.setValue("DOCUMENT");
        
        MetadataCondition condition = new MetadataCondition();
        condition.setLogicalOperator("and");
        condition.setConditions(Collections.singletonList(conditionItem));
        request.setMetadataCondition(condition);

        String json = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(request);
        
        // 验证输出格式使用下划线
        assertTrue(json.contains("\"knowledge_id\""));
        assertTrue(json.contains("\"retrieval_setting\""));
        assertTrue(json.contains("\"top_k\""));
        assertTrue(json.contains("\"score_threshold\""));
        assertTrue(json.contains("\"metadata_condition\""));
        assertTrue(json.contains("\"logical_operator\""));
        assertTrue(json.contains("\"comparison_operator\""));
        
        // 验证不包含驼峰格式
        assertFalse(json.contains("\"knowledgeId\""));
        assertFalse(json.contains("\"retrievalSetting\""));
        assertFalse(json.contains("\"topK\""));
        assertFalse(json.contains("\"scoreThreshold\""));
        assertFalse(json.contains("\"metadataCondition\""));
        assertFalse(json.contains("\"logicalOperator\""));
        assertFalse(json.contains("\"comparisonOperator\""));
        
        System.out.println("输出JSON格式:");
        System.out.println(json);
    }

    @Test
    public void testJavaClientUsage() {
        // 演示Java客户端如何使用（驼峰命名风格）
        DifySearchRequest request = new DifySearchRequest();
        request.setKnowledgeId("kb-java-client");  // Java中仍使用下划线字段名
        request.setQuery("Java客户端测试");
        
        RetrievalSetting setting = new RetrievalSetting();
        setting.setTopK(20);
        setting.setScoreThreshold(0.5);
        request.setRetrievalSetting(setting);
        
        // 创建复杂的metadata条件
        MetadataConditionItem condition1 = new MetadataConditionItem();
        condition1.setName(Arrays.asList("title", "description"));
        condition1.setComparisonOperator("contains");
        condition1.setValue("Java");
        
        MetadataConditionItem condition2 = new MetadataConditionItem();
        condition2.setName(Collections.singletonList("tags"));
        condition2.setComparisonOperator("contains");
        condition2.setValue("编程");
        
        MetadataCondition metadataCondition = new MetadataCondition();
        metadataCondition.setLogicalOperator("or");
        metadataCondition.setConditions(Arrays.asList(condition1, condition2));
        request.setMetadataCondition(metadataCondition);
        
        // 验证对象创建成功
        assertNotNull(request);
        assertEquals("kb-java-client", request.getKnowledgeId());
        assertEquals(2, request.getMetadataCondition().getConditions().size());
        
        System.out.println("Java客户端创建的请求对象:");
        System.out.println("Knowledge ID: " + request.getKnowledgeId());
        System.out.println("Query: " + request.getQuery());
        System.out.println("Top K: " + request.getRetrievalSetting().getTopK());
        System.out.println("Conditions count: " + request.getMetadataCondition().getConditions().size());
    }
}
