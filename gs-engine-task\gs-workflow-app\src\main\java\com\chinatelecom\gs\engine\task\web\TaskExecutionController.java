package com.chinatelecom.gs.engine.task.web;

import cn.hutool.core.text.CharSequenceUtil;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.auth.PermissionTypeEnum;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.utils.IdGenerator;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.SseMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.BotAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.DialogMessageTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.SendMessageTypeEnum;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelecom.gs.engine.task.sdk.TaskApis;
import com.chinatelecom.gs.engine.task.sdk.config.WorkflowExecConfig;
import com.chinatelecom.gs.engine.task.sdk.vo.TaskDebugRequest;
import com.chinatelecom.gs.engine.task.sdk.vo.TaskExecutionResult;
import com.chinatelecom.gs.engine.task.sdk.vo.TaskMessageRequest;
import com.chinatelecom.gs.engine.task.sdk.vo.TaskProcessData;
import com.chinatelecom.gs.engine.task.service.TaskExecutionService;
import com.chinatelecom.gs.workflow.core.service.BotWorkflowConfigService;
import com.chinatelecom.gs.workflow.core.workflow.core.model.result.DagResult;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;


@RestController
@Slf4j
@Tag(name = "运行业务流 Controller")
@PermissionTag(code = {MenuConfig.FLOW_MARKET, KsMenuConfig.FLOW_MARKET})
@PermissionTag(code = {MenuConfig.DIALOG_FLOW, MenuConfig.WORKFLOW, KsMenuConfig.WORKFLOW_FLOW})
@PermissionTag(code = MenuConfig.STRATEGY, type = PermissionTypeEnum.MENU)
@RequestMapping({TaskApis.TASK_API + Constants.WEB_PREFIX + TaskApis.RUN_API, TaskApis.TASK_API + Constants.API_PREFIX + TaskApis.RUN_API})
public class TaskExecutionController {

    @Resource
    private TaskExecutionService taskExecutionService;

    @Resource
    private WorkflowExecConfig workflowExecConfig;

    @Resource
    private BotWorkflowConfigService botWorkflowConfigService;

    @Operation(summary = "试运行")
    @PlatformRestApi(name = "试运行", groupName = "运行业务流")
    @DebugLog(operation = "试运行")
    @PostMapping(value = TaskApis.TEST_EXE)
    @AuditLog(businessType = "运行业务流", operType = "试运行", operDesc = "试运行", objId = "#taskDebugRequest.workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<TaskExecutionResult> testExecution(@Validated @RequestBody TaskDebugRequest taskDebugRequest) {
        String appCode = RequestContext.get().getAppCode();
        if (CharSequenceUtil.isBlank(appCode)) {
            throw new BizException("AC020", "appCode不能为空");
        }
        return Result.success(taskExecutionService.invokeTestExecution(taskDebugRequest));
    }

    @Operation(summary = "试运行-同步返回结果")
    @PlatformRestApi(name = "试运行-同步返回结果", groupName = "运行业务流")
    @DebugLog(operation = "试运行-同步返回结果")
    @PostMapping(value = "syncTest")
    @AuditLog(businessType = "运行业务流", operType = "试运行-同步返回结果", operDesc = "试运行-同步返回结果", objId = "#taskDebugRequest.workflowId")
    public Result<DagResult> syncTestExecution(@Validated @RequestBody TaskDebugRequest taskDebugRequest) {
        String appCode = RequestContext.get().getAppCode();
        if (CharSequenceUtil.isBlank(appCode)) {
            throw new BizException("AC020", "appCode不能为空");
        }
        return Result.success(taskExecutionService.invokeRun(taskDebugRequest));
    }


    @Operation(summary = "查询试运行详情")
    @PlatformRestApi(name = "查询试运行详情", groupName = "运行业务流")
    @DebugLog(operation = "查询试运行详情")
    @GetMapping(value = TaskApis.GET_PROCESS)
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "运行业务流", operType = "查询试运行详情", operDesc = "查询试运行详情", objId = "#workflowId")
    public Result<TaskProcessData> getProcess(@RequestParam("workflowId") String workflowId, @RequestParam("messageId") String messageId) {
        String appCode = RequestContext.get().getAppCode();
        if (CharSequenceUtil.isBlank(appCode)) {
            throw new BizException("AC020", "appCode不能为空");
        }
        return Result.success(taskExecutionService.getProcessDetail(workflowId, messageId));
    }

    @Operation(summary = "终止调试")
    @PlatformRestApi(name = "终止调试", groupName = "运行业务流")
    @DebugLog(operation = "终止调试业务流")
    @GetMapping(value = "/endWorkTask")
    @AuditLog(businessType = "运行业务流", operType = "终止调试", operDesc = "终止调试", objId = "#workflowId")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> endWorkTask(@RequestParam("workflowId") String workflowId, @RequestParam("messageId") String messageId) {
        String appCode = RequestContext.get().getAppCode();
        if (CharSequenceUtil.isBlank(appCode)) {
            throw new BizException("AC020", "appCode不能为空");
        }
        return Result.success(taskExecutionService.endWorkflowTask(workflowId, messageId));
    }

    /**
     * 工作流任务执行 SSE接口
     *
     * @return
     */
    @Operation(summary = "业务流任务执行")
    @PlatformRestApi(name = "业务流任务执行", groupName = "运行业务流")
    @PostMapping("/sseDialog")
    @AuditLog(businessType = "运行业务流", operType = "业务流任务执行", operDesc = "业务流任务执行", objId = "#dialogRequest.workflowId")
    public SseEmitter sseDialog(@Valid @RequestBody TaskMessageRequest dialogRequest, HttpServletRequest request) {
        SseEmitter emitter = new SseEmitter(workflowExecConfig.getWorkflowSseTimeout());
        try {
            String appCode = RequestContext.getAppCode();
            if (CharSequenceUtil.isBlank(appCode)) {
                throw new BizException("AC020", "appCode不能为空");
            }
            TaskDebugRequest taskDebugRequest = new TaskDebugRequest();
            taskDebugRequest.setWorkflowId(dialogRequest.getWorkflowId());
            taskDebugRequest.setSessionId(dialogRequest.getSessionId());
            taskDebugRequest.setMessageId(dialogRequest.getMessageId());
            Map<String, Object> inputs = dialogRequest.getInputs() == null ? new LinkedHashMap<>() : dialogRequest.getInputs();
            if (CharSequenceUtil.isNotBlank(dialogRequest.getQuery())) {
                inputs.put("BOT_USER_INPUT", dialogRequest.getQuery());
                inputs.put("FILE_USER", dialogRequest.getFileCodes());
            }

            Map<String, Object> globalVar = dialogRequest.getGlobalVar() == null ? new LinkedHashMap<>() : dialogRequest.getGlobalVar();
            if (CharSequenceUtil.isNotBlank(dialogRequest.getQuery())) {
                globalVar.put("BOT_USER_INPUT", dialogRequest.getQuery());
                globalVar.put("FILE_USER", dialogRequest.getFileCodes());
            }
            if (MapUtils.isNotEmpty(dialogRequest.getInputs())) {
                globalVar.putAll(dialogRequest.getInputs());
            }

            taskDebugRequest.setInputs(inputs);
            taskDebugRequest.setGlobalVar(globalVar);
            taskDebugRequest.setTest(false);
            taskDebugRequest.setAppCode(appCode);
            taskDebugRequest.setSseEmitter(emitter);
            taskExecutionService.invokeTestExecution(taskDebugRequest);
        } catch (Exception e) {
            try {
                BizException bizException = new BizException("null", e.getMessage());
                if (e instanceof com.chinatelelcom.gs.engine.sdk.common.exception.BizException) {
                    bizException = (BizException) e;
                }
                SseMessageResponse errorMessage = buildErrorSSEMessage(dialogRequest.getMessageId(),
                        bizException.getMessage(), bizException.getCode());
                emitter.send(errorMessage, MediaType.APPLICATION_JSON);
                emitter.complete();
            } catch (IOException ex) {
                log.info("task Error while sending SSE data:{}", dialogRequest, ex);
            }
        }
        return emitter;
    }


    /**
     * 工作流任务测试 SSE接口
     *
     * @return
     */
    @Operation(summary = "业务流任务执行(测试)")
    @PlatformRestApi(name = "业务流任务执行(测试)", groupName = "运行业务流")
    @PostMapping("/sseDialog-test")
    @AuditLog(businessType = "运行业务流", operType = "业务流任务执行(测试)", operDesc = "业务流任务执行(测试)", objId = "#dialogRequest.workflowId")
    public SseEmitter sseDialogTest(@Valid @RequestBody TaskDebugRequest dialogRequest) {
        SseEmitter emitter = new SseEmitter(workflowExecConfig.getWorkflowSseTimeout());
        try {
            String appCode = RequestContext.getAppCode();
            if (CharSequenceUtil.isBlank(appCode)) {
                throw new BizException("AC020", "appCode不能为空");
            }
            TaskDebugRequest taskDebugRequest = new TaskDebugRequest();
            taskDebugRequest.setWorkflowId(dialogRequest.getWorkflowId());
            taskDebugRequest.setSessionId(dialogRequest.getSessionId());
            taskDebugRequest.setMessageId(dialogRequest.getMessageId());
            Map<String, Object> inputs = dialogRequest.getInputs() == null ? new LinkedHashMap<>() : dialogRequest.getInputs();
            if (CharSequenceUtil.isNotBlank(dialogRequest.getQuery())) {
                inputs.put("BOT_USER_INPUT", dialogRequest.getQuery());
                inputs.put("FILE_USER", dialogRequest.getFileCodes());
            }

            Map<String, Object> globalVar = dialogRequest.getGlobalVar() == null ? new LinkedHashMap<>() : dialogRequest.getGlobalVar();
            if (CharSequenceUtil.isNotBlank(dialogRequest.getQuery())) {
                globalVar.put("BOT_USER_INPUT", dialogRequest.getQuery());
                globalVar.put("FILE_USER", dialogRequest.getFileCodes());
            }
            if (MapUtils.isNotEmpty(dialogRequest.getInputs())) {
                globalVar.putAll(dialogRequest.getInputs());
            }

            taskDebugRequest.setGlobalVar(globalVar);
            taskDebugRequest.setInputs(inputs);
            taskDebugRequest.setTest(true);
            taskDebugRequest.setAppCode(appCode);
            taskDebugRequest.setSseEmitter(emitter);
            taskDebugRequest.setAgentCode(dialogRequest.getAgentCode());
            taskExecutionService.invokeTestExecution(taskDebugRequest);
        } catch (Exception e) {
            try {
                BizException bizException = new BizException("null", e.getMessage());
                if (e instanceof com.chinatelelcom.gs.engine.sdk.common.exception.BizException) {
                    bizException = (BizException) e;
                }
                SseMessageResponse errorMessage = buildErrorSSEMessage(dialogRequest.getMessageId(),
                        bizException.getMessage(), bizException.getCode());
                emitter.send(errorMessage, MediaType.APPLICATION_JSON);
                emitter.complete();
            } catch (IOException ex) {
                log.info("task Error while sending SSE data:{}", dialogRequest, ex);
            }
        }
        return emitter;
    }

    private SseMessageResponse buildErrorSSEMessage(String messageId, String message, String code) {
        SseMessageResponse sseMessageResponse = new SseMessageResponse();
        sseMessageResponse.setUpMsgId(messageId);
        String downMsgId = IdGenerator.getMessageId();
        sseMessageResponse.setDownMsgId(downMsgId);
        sseMessageResponse.setMessageType(DialogMessageTypeEnum.ALL);
        sseMessageResponse.setEventType(SendMessageTypeEnum.ERROR);
        sseMessageResponse.setCode(code);
        BotAnswer botAnswer = new BotAnswer();
        botAnswer.setAnswerType(AnswerTypeEnum.MARKDOWN);
        botAnswer.setNamespace("com.markdown");
        botAnswer.setMessageId(downMsgId);
        botAnswer.setContent(message);
        sseMessageResponse.setAnswer(botAnswer);
        return sseMessageResponse;
    }
}
