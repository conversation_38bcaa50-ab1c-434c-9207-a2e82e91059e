package com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config;


import lombok.Data;

@Data
public class AgentConfig {

    /**
     * 智能体code
     */
    private String agentCode;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 租户名
     */
    private String tenantName;

    /**
     * 机器人名称
     */
    private String agentName;

    /**
     * 测试版本号
     */
    private Long testVersion;

    /**
     * 线上版本号
     */
    private Long prodVersion;


    private boolean defaultRagAgent;

    /**
     * bot类型
     */
    private Integer botType;

    /**
     * agent分类id
     * AgentTemplateCategoryEnum
     */
    private String templateCategoryId;

    /**
     * 基础信息配置
     */
    private AgentBasicConfig basicConfig;

    /**
     * 模型配置
     */
    private ModelConfig modelConfig;

    /**
     * 多模态大模型配置
     */
    private ModelConfig multiModelConfig;

    /**
     * 对话配置
     */
    private DialogConfig dialogConfig;
    /**
     * 全局策略配置
     */
    private GlobalPolicyConfig globalPolicyConfig;

    /**
     * kms配置
     */
    private KmsConfig kmsConfig;

    /**
     * 安全围栏开关
     */
    private Boolean safeFenceSwitch;

    /**
     * 敏感词开关
     */
    private Boolean sensitiveSwitch;
    /**
     * 安全围栏鉴权配置
     */
    private SafeFenceConfig safeFenceConfig;

    /**
     * 应答模式配置
     */
    private DialogModeConfig dialogModeConfig;
}
