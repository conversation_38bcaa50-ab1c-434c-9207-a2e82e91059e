package com.chinatelecom.gs.engine.robot.dialog.controller.web;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONObject;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.QueryChatDataVO;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelecom.gs.plugin.hub.infra.handler.executor.bi.ChatTaskOpenPoller;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "BI聊天窗口")
@Validated
@RestController
@RequestMapping({Constants.ROBOT_PREFIX + Constants.WEB_PREFIX + "/bi", Constants.ROBOT_PREFIX + Constants.API_PREFIX + "/bi"})
public class BiChatDataController {

    @Value("${nl2sql.openapi.chatDataUri:}")
    private String chatDataEndpoint;

    @Value("${nl2sql.host:}")
    private String biHost;

    @Value("${nl2sql.signKey:}")
    private String signKey;

    @Autowired
    private AgentBasicConfigService  agentBasicConfigService;

    @Operation(summary = "查询bi分页数据")
    @PlatformRestApi(name = "查询bi分页数据", groupName = "聊天窗口")
    @GetMapping("/page")
    @AuditLog(businessType = "BI聊天窗口", operType = "查询bi分页数据", operDesc = "查询bi分页数据", objId="#request.agentCode")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Object> queryChatPage(@Validated QueryChatDataVO request) {
        log.info("queryChatPage request:{}", request);
        return queryOpenApiChatPage(request);
    }

    /**
     * open api
     *
     * @param request QueryChatDataVO
     * @return Result<Object>
     */
    private Result<Object> queryOpenApiChatPage(QueryChatDataVO request) {
        // 查询机器人创建者
        String createId = agentBasicConfigService.queryAgentCreateId(request.getAgentCode());
        String format = CharSequenceUtil.format(biHost + chatDataEndpoint,
                request.getChatId(), request.getPage(), request.getPageSize(),
                createId, RequestContext.getTenantId());
        JSONObject taskResult = ChatTaskOpenPoller.pollChatTask(format,
                request.getActiveBusiness(), signKey);
        return Result.success(Objects.isNull(taskResult) ? taskResult : taskResult.get("data"));
    }
}
