package com.chinatelecom.gs.engine.task.handler;

import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.core.model.entity.dto.Intent;
import com.chinatelecom.gs.engine.core.model.enums.DataSourceTypeEnum;
import com.chinatelecom.gs.engine.core.model.enums.IntentRetrieveType;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.ExternalModelInfo;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.ModelProviderEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.QueryRankRequest;
import com.chinatelecom.gs.engine.core.model.toolkit.core.service.CommonRankModel;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.kms.sdk.api.KmsSearchApi;
import com.chinatelecom.gs.engine.kms.sdk.enums.*;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.BaseItem;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchResp;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.sdk.SdkSearchResponse;
import com.chinatelecom.gs.workflow.core.workflow.core.context.DagContext;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.EnvTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.NodeTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.SearchStrategyEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.handler.AbstractNodeHandler;
import com.chinatelecom.gs.workflow.core.workflow.core.model.biz.param.KnowledgeNodeBizParam;
import com.chinatelecom.gs.workflow.core.workflow.core.model.node.DagNode;
import com.chinatelecom.gs.workflow.core.workflow.core.model.result.NodeResult;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 知识库节点
 *
 * @USER: pengmc1
 * @DATE: 2024/8/27 9:52
 */

@Slf4j
@Component
public class KnowledgeNodeHandler extends AbstractNodeHandler<Object> {

    @Resource
    private KmsSearchApi kmsSearchApi;

    @Resource
    private CommonRankModel commonRankModel;

    @Resource
    private ModelServiceClient remoteServiceClient;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    @Value("${csbotplatform.agent.rank.enableBothRecall:true}")
    private boolean enableBothRecall;

    /**
     * 处理的节点类型
     *
     * @return NodeTypeEnum
     */
    @Override
    public NodeTypeEnum nodeType() {
        return NodeTypeEnum.KNOWLEDGE;
    }

    /**
     * 执行任务
     *
     * @param param   输入参数
     * @param node    节点数据
     * @param context 上下文
     * @return NodeResult<Object>
     */
    @Override
    public NodeResult<Object> doExecute(Map<String, Object> param, DagNode node, DagContext context) throws Exception {
        String query = (String) param.get("Query");
        if (query == null) {
            return NodeResult.fail("查询参数不能为空");
        }
        log.info("知识库节点查询query：{}", query);

        KnowledgeNodeBizParam knowledgeNodeBizParam = node.getBizParam(KnowledgeNodeBizParam.class);
        if (knowledgeNodeBizParam == null) {
            return NodeResult.fail("知识库节点参数不能为空");
        }
        log.info("知识库节点业务参数：{}", knowledgeNodeBizParam);

        List<KnowledgeNodeBizParam.KmsItem> kmsList = knowledgeNodeBizParam.getKmsList();


        List<String> faqKnowledgeIds = kmsList.stream().filter(kmsItem -> kmsItem.getKnowledgeType().equals("FAQ")).map(KnowledgeNodeBizParam.KmsItem::getKnowledgeId).collect(Collectors.toList());
        String appCode = Boolean.TRUE.equals(context.getDagParam().getIsSystemAgent()) || context.getDag().getIsSystem() ? superTenant : RequestContext.getAppCode();
        List<Intent> faqKnowledgeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(faqKnowledgeIds)) {
            SearchResp<? extends BaseItem> faqSearchResult = kmsSearchApi.searchChunksWithHeader(buildSearchParamByType(query, "FAQ", knowledgeNodeBizParam, context), appCode);
            log.info("faq检索结果：{}", JSON.toJSONString(faqSearchResult));
            faqKnowledgeList.addAll(processSearchResults(faqSearchResult, query, "FAQ"));
        }
        List<String> docKnowledgeIds = kmsList.stream().filter(kmsItem -> kmsItem.getKnowledgeType().equals("FILE")).map(KnowledgeNodeBizParam.KmsItem::getKnowledgeId).collect(Collectors.toList());
        List<Intent> docKnowledgeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(docKnowledgeIds)) {
            SearchResp<? extends BaseItem> docSearchResult = kmsSearchApi.searchChunksWithHeader(buildSearchParamByType(query, "FILE", knowledgeNodeBizParam, context), appCode);
            log.info("doc检索结果：{}", JSON.toJSONString(docSearchResult));
            docKnowledgeList.addAll(processSearchResults(docSearchResult, query, "FILE"));
        }

        List<String> externalKmsIds = kmsList.stream().filter(kmsItem -> kmsItem.getKnowledgeType().equals("EXTERNAL")).map(KnowledgeNodeBizParam.KmsItem::getKnowledgeId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(externalKmsIds)) {
            SearchResp<? extends BaseItem> externalSearchResult = kmsSearchApi.searchChunksWithHeader(buildSearchParamByType(query, "EXTERNAL", knowledgeNodeBizParam, context), appCode);
            log.info("外部知识库检索结果：{}", JSON.toJSONString(externalSearchResult));
            docKnowledgeList.addAll(processSearchResults(externalSearchResult, query, "EXTERNAL"));
        }

        // 合并faq和doc检索结果
        List<Intent> combinedList = Stream.concat(
                Optional.of(faqKnowledgeList).orElse(Collections.emptyList()).stream(),
                Optional.of(docKnowledgeList).orElse(Collections.emptyList()).stream()
        ).collect(Collectors.toList());

        List<Object> answer = null;
        if (!CollectionUtils.isEmpty(combinedList)) {
            answer = combinedList.stream().map(intent -> Collections.singletonMap("output", intent.getAnswerContent())).collect(Collectors.toList());
        }

        return NodeResult.success(Collections.singletonMap("outputList", answer), combinedList);
    }

    /**
     * 处理检索结果
     *
     * @param searchResult SearchResp
     * @param query        String
     * @param type         String
     * @return List<Intent>
     */
    private List<Intent> processSearchResults(SearchResp<? extends BaseItem> searchResult, String query, String type) {
        if (searchResult == null || searchResult.getData() == null || CollectionUtils.isEmpty(searchResult.getData().getItems())) {
            return Collections.emptyList();
        }

        return searchResult.getData().getItems().stream()
                .distinct()
                .map(o -> {
                    if (type.equals("FAQ")) {
                        return convertFaqIntent(o);
                    } else if (type.equalsIgnoreCase("FILE")) {
                        return convertDocIntent(o, query);
                    } else if (type.equalsIgnoreCase("EXTERNAL")) {
                        return convertExternalIntent(o, query);
                    }
                    return null;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }


    /**
     * 过滤掉不符合要求的结果，并排序
     *
     * @param intents  List<Intent>
     * @param minScore double
     * @param topN     int
     */
    private List<Intent> filterAndSortIntents(List<Intent> intents, double minScore, int topN, boolean useRerank) {
        if (intents == null || intents.isEmpty()) {
            return Collections.emptyList();
        }
        if (!useRerank) {
            return intents.stream()
                    .peek(item -> item.setScore(getFormattedScore(item.getScore())))
                    .filter(item -> item.getScore() >= minScore)
                    .sorted(Comparator.comparing(Intent::getScore).reversed())
                    .limit(topN)
                    .collect(Collectors.toList());
        }

        return intents.stream()
                .peek(item -> {
                    item.setScore(getFormattedScore(item.getScore()));
                    item.setReRankScore(getFormattedScore(item.getReRankScore()));
                })
                .filter(item -> item.getReRankScore() >= minScore)
                .sorted(Comparator.comparing(Intent::getReRankScore).reversed())
                .limit(topN)
                .collect(Collectors.toList());
    }

    /**
     * 获取标准化的两位小数分数（线程安全）
     */
    public double getFormattedScore(Double score) {
        return BigDecimal.valueOf(score)
                .setScale(2, RoundingMode.HALF_UP)
                .doubleValue();
    }

    /**
     * 调用faq精排模型
     *
     * @param context               DagContext
     * @param knowledgeNodeBizParam KnowledgeNodeBizParam
     * @param faqRerankModelParam   ModelPageListParam
     * @param query                 String
     * @param faqKnowledgeList      List<Intent>
     * @return List<Intent>
     */
    private List<Intent> getFaqRankIntents(DagContext context, KnowledgeNodeBizParam knowledgeNodeBizParam, ModelPageListParam faqRerankModelParam, String query, List<Intent> faqKnowledgeList) {
        // 初始化外部模型信息
        ExternalModelInfo externalModelInfo = buildExternalModelInfo(faqRerankModelParam);
        faqKnowledgeList = faqKnowledgeList.stream()
                .sorted(Comparator.comparing(Intent::getScore).reversed())
                .limit(gsGlobalConfig.getSearch().getRecallMaxSize())
                .collect(Collectors.toList());

        // 构建查询排序请求
        QueryRankRequest queryRankRequest = buildQueryRankRequest(context, query, "faq", knowledgeNodeBizParam, faqKnowledgeList, externalModelInfo);
        log.info("faq排序请求：{}", JSON.toJSONString(queryRankRequest));
        // 调用通用排序模型获取排序结果
        List<QueryRankRequest.CandidateQuery> rankResult = commonRankModel.rank(queryRankRequest);

        // 将排序结果映射到意图列表
        return mapRankResultToIntentList(faqKnowledgeList, rankResult);
    }

    // 提取方法：构建外部模型信息
    private ExternalModelInfo buildExternalModelInfo(ModelPageListParam faqRerankModelParam) {
        ExternalModelInfo externalModelInfo = new ExternalModelInfo();
        externalModelInfo.setModelUrl(faqRerankModelParam.getExternalModelUrl());
        externalModelInfo.setModelName(faqRerankModelParam.getModelName());
        externalModelInfo.setModelSecret(faqRerankModelParam.getModelSecret());
        externalModelInfo.setModelApi(faqRerankModelParam.getApiKey());
        externalModelInfo.setProvider(ModelProviderEnum.from(faqRerankModelParam.getModelProvider()));
        return externalModelInfo;
    }

    /**
     * 提取方法：构建查询排序请求
     *
     * @param context               DagContext
     * @param query                 String
     * @param knowledgeNodeBizParam KnowledgeNodeBizParam
     * @param knowledgeList         List<Intent>
     * @param externalModelInfo     ExternalModelInfo
     * @return QueryRankRequest
     */
    private QueryRankRequest buildQueryRankRequest(DagContext context, String query, String knowledgeType,
                                                   KnowledgeNodeBizParam knowledgeNodeBizParam, List<Intent> knowledgeList, ExternalModelInfo externalModelInfo) {
        QueryRankRequest queryRankRequest = new QueryRankRequest();
        queryRankRequest.setModelInfo(externalModelInfo);
        queryRankRequest.setRequestId(context.getMessageId());
        queryRankRequest.setQuery(query);
        if (knowledgeType.equalsIgnoreCase("doc")) {
            queryRankRequest.setTopN(knowledgeNodeBizParam.getDocTopN());

        } else {
            queryRankRequest.setTopN(knowledgeNodeBizParam.getFaqTopN());
        }
        queryRankRequest.setCandidateQueryList(knowledgeList.stream().distinct()
                .map(this::toCandidateQuery)
                .collect(Collectors.toList()));
        return queryRankRequest;
    }

    /**
     * 提取方法：将 Intent 转换为 CandidateQuery
     *
     * @param intent Intent
     * @return QueryRankRequest.CandidateQuery
     */
    private QueryRankRequest.CandidateQuery toCandidateQuery(Intent intent) {
        QueryRankRequest.CandidateQuery candidateQuery = new QueryRankRequest.CandidateQuery();
        candidateQuery.setId(intent.getIntentId());
        candidateQuery.setScore(intent.getScore());
        candidateQuery.setSimilarContent(intent.getSimilarContent());
        return candidateQuery;
    }

    /**
     * 提取方法：将排序结果映射到意图列表
     *
     * @param faqKnowledgeList List<Intent>
     * @param rankResult       List<QueryRankRequest.CandidateQuery>
     * @return List<Intent>
     */
    private List<Intent> mapRankResultToIntentList(List<Intent> faqKnowledgeList, List<QueryRankRequest.CandidateQuery> rankResult) {
        // 使用 Map 提高查找效率
        Map<String, Intent> intentMap = faqKnowledgeList.parallelStream()
                .collect(Collectors.toMap(Intent::getIntentId,
                        Function.identity(),
                        (existing, replacement) -> existing // 如果有重复的，保留原有的
                ));

        return rankResult.stream()
                .filter(candidateQuery -> intentMap.containsKey(candidateQuery.getId()))
                .map(candidateQuery -> {
                    Intent intent = intentMap.get(candidateQuery.getId());
                    intent.setReRankScore(candidateQuery.getScore());
                    return intent;
                })
                .collect(Collectors.toList());
    }


    /**
     * 调用文档精排模型
     *
     * @param context               DagContext
     * @param knowledgeNodeBizParam KnowledgeNodeBizParam
     * @param docRerankModelParam   ModelPageListParam
     * @param query                 String
     * @param docKnowledgeList      List<Intent>
     * @return List<Intent>
     */
    private List<Intent> getDocRerankIntents(DagContext context, KnowledgeNodeBizParam knowledgeNodeBizParam, ModelPageListParam docRerankModelParam, String query, List<Intent> docKnowledgeList) {
        // 初始化外部模型信息
        ExternalModelInfo externalModelInfo = buildExternalModelInfo(docRerankModelParam);

        // 构建查询排序请求
        QueryRankRequest queryRankRequest = buildQueryRankRequest(context, query, "doc", knowledgeNodeBizParam, docKnowledgeList, externalModelInfo);
        docKnowledgeList = docKnowledgeList.stream()
                .sorted(Comparator.comparing(Intent::getScore).reversed())
                .limit(gsGlobalConfig.getSearch().getRecallMaxSize())
                .collect(Collectors.toList());
        // 调用通用排序模型获取排序结果
        List<QueryRankRequest.CandidateQuery> rankResult = commonRankModel.rank(queryRankRequest);

        // 将排序结果映射到意图列表
        return mapRankResultToIntentList(docKnowledgeList, rankResult);
    }

    /**
     * convert faq knowledge to intent
     *
     * @param knowledgeResponse SearchResponse
     * @return Intent
     */
    private Intent convertFaqIntent(SdkSearchResponse.SdkRetrieveItem<? extends BaseItem> knowledgeResponse) {
        Intent intent = new Intent();
        BaseItem source = knowledgeResponse.getSource();
        intent.setKmsCode(source.getKnowledgeBaseCode());
        intent.setIntentType(com.chinatelecom.gs.engine.core.model.enums.IntentRetrieveType.FAQ);
        intent.setDialogEngineType(com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType.KMS);
        intent.setDataSourceTypeEnum(DataSourceTypeEnum.KNOWLEDGE_BASE);
        intent.setIntentId(source.getId());
        intent.setIntentName(source.getTitle());
        intent.setQueryContent(source.getQuestion());
        intent.setSimilarContent(source.getContent());
        intent.setAnswerContent(source.getAnswer());
        intent.setScore(knowledgeResponse.getScore());
        return intent;
    }


    /**
     * convert doc knowledge to intent
     *
     * @param knowledgeResponse SearchResponse
     * @param query             String
     * @return Intent
     */
    private Intent convertExternalIntent(SdkSearchResponse.SdkRetrieveItem<? extends BaseItem> knowledgeResponse, String query) {
        Intent intent = new Intent();
        BaseItem source = knowledgeResponse.getSource();
        intent.setKmsCode(source.getKnowledgeBaseCode());
        intent.setIntentType(IntentRetrieveType.EXTERNAL_KMS);
        intent.setDialogEngineType(com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType.KMS);
        DataSourceTypeEnum knowledgeBase = DataSourceTypeEnum.KNOWLEDGE_BASE;
        intent.setDataSourceTypeEnum(knowledgeBase);
        intent.setIntentId(source.getKnowledgeCode());
        intent.setIntentName(source.getTitle());
        intent.setQueryContent(query);
        intent.setSimilarContent(source.getContent());
        intent.setAnswerContent(source.getContent());
        intent.setScore(knowledgeResponse.getScore());
        return intent;
    }

    /**
     * convert doc knowledge to intent
     *
     * @param knowledgeResponse SearchResponse
     * @param query             String
     * @return Intent
     */
    private Intent convertDocIntent(SdkSearchResponse.SdkRetrieveItem<? extends BaseItem> knowledgeResponse, String query) {
        Intent intent = new Intent();
        BaseItem source = knowledgeResponse.getSource();
        intent.setKmsCode(source.getKnowledgeBaseCode());
        intent.setIntentType(com.chinatelecom.gs.engine.core.model.enums.IntentRetrieveType.DOC);
        intent.setDialogEngineType(com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType.KMS);
        DataSourceTypeEnum knowledgeBase = DataSourceTypeEnum.KNOWLEDGE_BASE;
        intent.setDataSourceTypeEnum(knowledgeBase);
        intent.setIntentId(source.getId());
        intent.setIntentName(source.getTitle());
        intent.setQueryContent(query);
        intent.setSimilarContent(source.getContent());
        intent.setAnswerContent(source.getContent());
        intent.setScore(knowledgeResponse.getScore());
        return intent;
    }


    /**
     * build 检索参数
     *
     * @param query                 String
     * @param knowledgeNodeBizParam KnowledgeNodeBizParam
     * @return SearchParam
     */
    private SearchParam buildSearchParamByType(String query, String knowledgeType, KnowledgeNodeBizParam knowledgeNodeBizParam, DagContext context) {
        SearchParam searchParam = new SearchParam();
        searchParam.setQuery(query);
        searchParam.setRange(SearchRange.all);
        searchParam.setEnv(context.getEnv().equals(EnvTypeEnum.TEST) ? EnvType.TEST : EnvType.PROD);
        SearchParam.Filter filter = new SearchParam.Filter();

        List<KnowledgeNodeBizParam.KmsItem> kmsList = knowledgeNodeBizParam.getKmsList();
        List<SearchParam.KnowledgeFilter> filters = kmsList.stream().filter(kmsItem -> kmsItem.getKnowledgeType().equals(knowledgeType)).map(kmsItem -> {
            SearchParam.KnowledgeFilter knowledgeFilter = new SearchParam.KnowledgeFilter();
            knowledgeFilter.setKnowledgeBaseCode(kmsItem.getKnowledgeId());
            return knowledgeFilter;
        }).collect(Collectors.toList());
        filter.setSourceSystem(RequestContext.getAppSourceType());
        filter.setKnowledgeFilters(filters);

        switch (knowledgeType) {
            case "FAQ":
                searchParam.setType(IndexType.faq);
                filter.setTopN((long)knowledgeNodeBizParam.getFaqTopN());
                searchParam.setPageSize(knowledgeNodeBizParam.getFaqTopN());
                searchParam.setPageNum(1);
                filter.setThreshold(knowledgeNodeBizParam.getFaqMinScore());
                filter.setIsRank(knowledgeNodeBizParam.getFaqUseRerank());
                filter.setRankModelCode(knowledgeNodeBizParam.getFaqSortModelCode());
                break;
            case "FILE":
                searchParam.setType(IndexType.doc);
                filter.setTopN((long) knowledgeNodeBizParam.getDocTopN());
                searchParam.setPageSize(knowledgeNodeBizParam.getDocTopN());
                searchParam.setPageNum(1);
                filter.setThreshold(knowledgeNodeBizParam.getDocMinScore());
                filter.setIsRank(knowledgeNodeBizParam.getDocUseRerank());
                filter.setRankModelCode(knowledgeNodeBizParam.getDocSortModelCode());
                break;
            case "EXTERNAL":
                searchParam.setType(IndexType.external);
                filter.setTopN((long) knowledgeNodeBizParam.getDocTopN());
                searchParam.setPageSize(knowledgeNodeBizParam.getDocTopN());
                searchParam.setPageNum(1);
                filter.setThreshold(knowledgeNodeBizParam.getDocMinScore());
                filter.setIsRank(knowledgeNodeBizParam.getDocUseRerank());
                filter.setRankModelCode(knowledgeNodeBizParam.getDocSortModelCode());
                break;
        }

        filter.setCheckRole(false);
        searchParam.setFilter(filter);
        String strategy = knowledgeNodeBizParam.getStrategy();
        SearchStrategyEnum searchStrategyEnum = SearchStrategyEnum.getByCode(strategy);
        if (searchStrategyEnum == null) {
            searchParam.setRecallType(RecallType.both);
        } else {
            switch (searchStrategyEnum) {
                case MIX:
                    if (!KnowledgeBaseType.EXTERNAL.name().equals(knowledgeType) && enableBothRecall) {
                        searchParam.setRecallType(RecallType.separate_both);
                    } else {
                        searchParam.setRecallType(RecallType.both);
                    }
                    break;
                case KEYWORD:
                    searchParam.setRecallType(RecallType.keyword);
                    break;
                case SEMANTICS:
                    searchParam.setRecallType(RecallType.knn);
                    break;
                default:
                    throw new BizException("BA009", "召回类型错误");
            }
        }
        log.info("{} 检索入参数{}", knowledgeType, JSON.toJSONString(searchParam));
        return searchParam;
    }
}
