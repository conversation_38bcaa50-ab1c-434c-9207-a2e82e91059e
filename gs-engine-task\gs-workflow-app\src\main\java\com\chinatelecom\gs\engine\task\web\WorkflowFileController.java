package com.chinatelecom.gs.engine.task.web;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.constants.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.core.model.utils.FileUtil;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelecom.gs.engine.task.sdk.TaskApis;
import com.chinatelecom.gs.workflow.core.domain.dto.FileUploadDTO;
import com.chinatelecom.gs.workflow.core.domain.dto.FileUploadVo;
import com.chinatelecom.gs.workflow.core.service.FileService;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;

@Controller
@Slf4j
@Tag(name = "文件上传下载Api", description = "FileLoad")
@RequestMapping({TaskApis.TASK_API + Constants.WEB_PREFIX + "/file", TaskApis.TASK_API + Constants.API_PREFIX + "/file"})
public class WorkflowFileController {

    @Autowired
    private FileService fileService;

    @Value("#{'${storage.file.suffix:.jpg,.png,.gif,.bmp,.jpeg,.webp,.csv,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt,.zip,.rar,.gz,.mp3,.wav,.sql,.mp4}'.split(',')}")
    private List<String> fileSuffixList;

    /**
     * 上传应答端的文件
     *
     * @param multipartFile MultipartFile
     * @param needPreview   是否需要预览,富文本回显使用
     * @return FileUploadVo
     */
    @Operation(summary = "上传文件")
    @PlatformRestApi(name = "上传文件", groupName = "文件上传下载Api")
    @ResponseBody
    @PostMapping(path = "/upload")
    @AuditLog(businessType = "文件上传下载Api", operType = "上传文件", operDesc = "上传文件", objId = "null")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<FileUploadVo> uploadCustomerFile(@RequestParam("file") MultipartFile multipartFile,
                                                   @RequestParam(value = "needPreview", required = false ) Boolean needPreview) throws Exception {
        FileUtil.checkFileType(multipartFile, fileSuffixList);
        FileUploadDTO uploadDTO = fileService.upload(multipartFile, needPreview);
        FileUploadVo fileUploadVo = new FileUploadVo();
        BeanUtils.copyProperties(uploadDTO, fileUploadVo);
        return Result.success(fileUploadVo);
    }

    @Operation(summary = "下载文件")
    @PlatformRestApi(name = "下载文件", groupName = "文件上传下载Api")
    @ResponseBody
    @GetMapping("/download/{fileSerialNo}")
    @AuditLog(businessType = "文件上传下载Api", operType = "下载文件", operDesc = "下载文件", objId = "null")
    @HideFromApiTypes(ApiType.OPENAPI)
    public void downloadCustomerFile(HttpServletResponse response,
                                     @Parameter(description = "文件编码", example = "file802041086074687488007.xlsx") @PathVariable String fileSerialNo
    ) throws IOException {
        String contentHeader = getContentHeader(fileSerialNo);
        InputStream inputStream = fileService.download(fileSerialNo);
        response.reset();
        response.setHeader("Content-disposition", contentHeader);
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");
        try (BufferedInputStream bis = new BufferedInputStream(inputStream)) {
            OutputStream os = response.getOutputStream();
            byte[] buf = new byte[1024];
            int length;
            // 输出文件
            while ((length = bis.read(buf)) > 0) {
                os.write(buf, 0, length);
            }
        } catch (IOException e) {
            throw new BizException("B0008", "文件下载异常");
        } finally {
            inputStream.close();
        }
    }

    private static String getContentHeader(String fileSerialNo) throws UnsupportedEncodingException {
        String fileName = URLEncoder.encode(fileSerialNo, "UTF-8");
        return String.format("attachment;filename*=utf-8''%s", fileName);
    }
}

