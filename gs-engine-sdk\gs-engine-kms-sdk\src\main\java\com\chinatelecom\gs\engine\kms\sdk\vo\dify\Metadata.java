package com.chinatelecom.gs.engine.kms.sdk.vo.dify;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class Metadata {
    private String path;
    private String description;

    /**
     * 动态字段存储，支持任意metadata字段
     */
    private Map<String, Object> additionalProperties = new HashMap<>();

    @JsonAnyGetter
    public Map<String, Object> getAdditionalProperties() {
        return additionalProperties;
    }

    @JsonAnySetter
    public void setAdditionalProperty(String name, Object value) {
        additionalProperties.put(name, value);
    }

    /**
     * 便捷方法：添加metadata字段
     */
    public void addMetadata(String key, Object value) {
        if (value != null) {
            additionalProperties.put(key, value);
        }
    }
}