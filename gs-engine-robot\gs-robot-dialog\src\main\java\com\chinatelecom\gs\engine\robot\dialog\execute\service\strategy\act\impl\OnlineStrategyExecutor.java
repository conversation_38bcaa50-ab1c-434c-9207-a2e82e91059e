package com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.act.impl;

import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.AgentStrategyConfig;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.StrategyNodeContext;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.StrategyNodeRequest;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.act.StrategyExecutor;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.processor.KnowledgeAcceptStrategyProcessor;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.processor.MultiRecallMixStrategyProcessor;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.processor.SummaryConversationStrategyProcessor;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.rewrite.QueryRewriteService;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.ToolMixerAnswer;
import com.chinatelecom.gs.workflow.core.workflow.core.context.DagContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
public class OnlineStrategyExecutor extends StrategyExecutor {

    @Resource
    private SummaryConversationStrategyProcessor summaryConversationStrategy;

    @Resource
    private KnowledgeAcceptStrategyProcessor knowledgeAcceptStrategy;

    @Resource
    private MultiRecallMixStrategyProcessor multiRecallMixStrategy;

    @Resource
    private QueryRewriteService queryRewriteService;

    @Override
    public List<ToolMixerAnswer> handleDirectTool(StrategyNodeContext nodeContext, DagContext context) {
        return null;
    }

    @Override
    public List<ToolMixerAnswer> execute(StrategyNodeContext nodeContext, DagContext context) {
        //检查总结是否命中
        ToolMixerAnswer summaryAnswer = summaryConversationStrategy.execute(nodeContext, context);
        if (Objects.nonNull(summaryAnswer)) {
            return Collections.singletonList(summaryAnswer);
        }

        //执行改写
        doRewriteQuery(nodeContext);

        StrategyNodeRequest strategyNodeRequest = nodeContext.getStrategyNodeRequest();
        strategyNodeRequest.setRecallContents(this.deRecallByEngines(nodeContext, context, Arrays.asList(DialogEngineType.KMS,DialogEngineType.PLUGIN, DialogEngineType.DATABASE, DialogEngineType.FILE)));
        nodeContext.setStrategyNodeRequest(strategyNodeRequest);

        ToolMixerAnswer strategyAnswer = knowledgeAcceptStrategy.execute(nodeContext, context);
        if (Objects.nonNull(strategyAnswer)) {
            return  Collections.singletonList(strategyAnswer);
        }

        ToolMixerAnswer toolMixerAnswer = multiRecallMixStrategy.execute(nodeContext, context);
        if (Objects.nonNull(toolMixerAnswer)) {
            return  Collections.singletonList(toolMixerAnswer);
        }

        return Collections.emptyList();
    }

    private void doRewriteQuery(StrategyNodeContext strategyNodeContext) {
        AgentStrategyConfig agentStrategyConfig = strategyNodeContext.getAgentStrategyConfig();
        String rewriteQuery = this.queryRewriteService.getRewriteResponseRpc(agentStrategyConfig.getAgentConfig(), agentStrategyConfig.getHeader(), agentStrategyConfig.getMessageCallback(), strategyNodeContext.getStrategyNodeRequest().getQuery());
        strategyNodeContext.getStrategyNodeRequest().setQuery(rewriteQuery);
    }

}
