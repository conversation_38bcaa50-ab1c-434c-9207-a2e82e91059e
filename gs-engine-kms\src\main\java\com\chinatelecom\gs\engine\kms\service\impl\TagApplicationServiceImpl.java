package com.chinatelecom.gs.engine.kms.service.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.gs.engine.common.config.mybatis.IgnoreAppCode;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.kms.base.model.CodeParam;
import com.chinatelecom.gs.engine.kms.base.service.impl.BaseAppServiceByCodeImpl;
import com.chinatelecom.gs.engine.kms.convert.vo.TagVoConverter;
import com.chinatelecom.gs.engine.kms.dto.TagDTO;
import com.chinatelecom.gs.engine.kms.dto.TagRelationDTO;
import com.chinatelecom.gs.engine.kms.infra.po.TagPO;
import com.chinatelecom.gs.engine.kms.repository.TagAppRepository;
import com.chinatelecom.gs.engine.kms.repository.TagRelationRepository;
import com.chinatelecom.gs.engine.kms.sdk.enums.TagScenarioEnum;
import com.chinatelecom.gs.engine.kms.sdk.enums.TargetType;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.BatchTagParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.TargetItem;
import com.chinatelecom.gs.engine.kms.sdk.vo.tag.TagCreateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.tag.TagQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.tag.TagUpdateParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.tag.TagVO;
import com.chinatelecom.gs.engine.kms.service.KnowledgeAppService;
import com.chinatelecom.gs.engine.kms.service.TagApplicationService;
import com.chinatelecom.gs.engine.kms.util.SortUtils;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Validated
@Slf4j
public class TagApplicationServiceImpl extends BaseAppServiceByCodeImpl<TagAppRepository,
        TagQueryParam, TagVoConverter, TagPO, TagDTO, TagVO, TagCreateParam, TagUpdateParam>
        implements TagApplicationService {

    @Resource
    private TagRelationRepository tagRelationRepository;

    @Resource
    private KnowledgeAppService knowledgeAppService;

    @Resource
    protected GsGlobalConfig gsGlobalConfig;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    private static void setTagInfo(TagDTO node, Tree<String> tree) {
        tree.setId(node.getCode());
        tree.setName(node.getName());
        tree.setParentId(node.getParentCode());
        // 额外的值
        tree.put("description", node.getDescription());
        tree.put("scenario", node.getScenario());
        tree.put("createTime", node.getCreateTime());
        tree.put("updateTime", node.getUpdateTime());
        tree.put("updateId", node.getUpdateId());
        tree.put("createId", node.getCreateId());
        tree.put("createName", node.getCreateName());
        tree.put("updateName", node.getUpdateName());
    }

    /**
     * 校验标签名称唯一性
     *
     * @param name        标签名称
     * @param parentCode  父标签编码
     * @param scenario    标签场景
     * @param currentCode 当前标签编码（更新时使用，创建时为null）
     */
    private void validateTagNameUnique(String name, String parentCode, TagScenarioEnum scenario, String currentCode) {
        if (StringUtils.isNotBlank(parentCode)) {
            // 子标签：按同层级检查标签名称是否重复
            TagDTO existSameName = repository.selectByNameAndParentCode(name, parentCode);
            if (existSameName != null && (!existSameName.getCode().equals(currentCode))) {
                BizAssert.throwBizException("AA062", "同层级下标签名称已存在");
            }
        } else {
            // 根标签：同一场景下不能重名
            TagDTO existSameName = repository.selectByNameAndScenario(name, scenario);
            if (existSameName != null && (!existSameName.getCode().equals(currentCode))) {
                BizAssert.throwBizException("AA063", "当前场景下根标签名称重复");
            }
        }
    }

    protected TagVoConverter converter() {
        return TagVoConverter.INSTANCE;
    }

    @Override
    protected Wrapper<TagPO> pageQueryWrapper(TagQueryParam query) {
        LambdaQueryWrapper<TagPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.like(StringUtils.isNotBlank(query.getName()), TagPO::getName, query.getName())
                .eq(TagPO::getParentCode, query.getParentCode())
                .in(CollectionUtils.isNotEmpty(query.getScenarios()), TagPO::getScenario, query.getScenarios())
        ;
        SortUtils.buildOrderBy(lambdaQueryWrapper, query.getOrder());
        return lambdaQueryWrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TagVO create(TagCreateParam createParam) {
        TagDTO dto = converter().convertCreate(createParam);

        // 校验标签名称重复逻辑
        validateTagNameUnique(createParam.getName(), createParam.getParentCode(), createParam.getScenario(), null);

        int tagMaxLevel = gsGlobalConfig.getKmsBizConfig().getTagMaxLevel();
        // 检查父标签的层级
        if (StringUtils.isNotBlank(createParam.getParentCode())) {
            String parentPath = repository.getTagPath(createParam.getParentCode());
            int parentLevel = calculateTagLevel(parentPath);

            if (parentLevel >= tagMaxLevel) {
                BizAssert.throwBizException("AA084", "标签层级超过限制，最多支持" + tagMaxLevel + "层");
            }
        }

        dto.setCode(IdGenerator.getId(StringUtils.EMPTY));
        // 生成path
        String parentCode = dto.getParentCode();
        String path = repository.getTagPath(parentCode);
        dto.setPath(path + dto.getCode() + TAG_PATH);

//        boolean isReferenced = this.isReferenced(parentCode);
//        // 新建根节点不需要刷新
//        if (StringUtils.isBlank(createParam.getParentCode())) {
//            isReferenced = false;
//        }
        repository.save(dto);
        dto = repository.selectByCode(dto.getCode());

        // 如果没有子标签 要强刷数据
//        if (isReferenced) {
//            //BizAssert.assertTrue(createParam.getForceRefresh(), "AA087", "标签新建需要设置为强制刷新");
//            log.info("标签[{}]的父标签[{}]从叶子标签变为父标签，自动刷新数据", dto.getCode(), parentCode);
//            refreshWhenLeafToParent(dto);
//        }

        return converter().convertVO(dto);
    }

    private int calculateTagLevel(String path) {
        if (StringUtils.isEmpty(path) || TAG_PATH.equals(path)) {
            return 0;
        }

        long count = path.chars().filter(ch -> ch == TAG_PATH.charAt(0)).count();

        return count > 0 ? (int) count - 1 : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TagVO updateTag(String code, TagUpdateParam updateParam) {
        // 获取当前标签信息
        TagDTO currentTag = repository.selectByCode(code);
        if (Objects.isNull(currentTag)) {
            BizAssert.throwBizException("AA083", "标签不存在");
        }

        // 校验标签名称重复逻辑
        TagScenarioEnum scenario = updateParam.getScenario() != null ? updateParam.getScenario() : currentTag.getScenario();
        validateTagNameUnique(updateParam.getName(), currentTag.getParentCode(), scenario, code);

        // 检查场景是否发生变更
        boolean scenarioChanged = updateParam.getScenario() != null &&
                !updateParam.getScenario().equals(currentTag.getScenario());

        // 更新当前标签
        super.update(code, updateParam);
        TagDTO updatedTag = repository.selectByCode(code);

        // 如果场景发生变更，更新所有子标签的场景
        if (scenarioChanged) {
            updateChildrenScenario(code, updateParam.getScenario());
        }

        return converter().convertVO(updatedTag);
    }

    @Transactional()
    @Override
    public boolean delete(CodeParam codes) {
        Set<String> tagCodes = repository.deleteTagAndChildren(codes.getCodes());
        unbind(tagCodes);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unbind(Set<String> codeParam) {
        log.info("开始解除标签关联，参数：{}", codeParam);
        try {
            Set<String> allTagCodes = new HashSet<>(codeParam);
            Set<String> subTagCodes = repository.fetchAllSubTag(codeParam);
            if (CollectionUtils.isNotEmpty(subTagCodes)) {
                log.info("找到子标签数量：{}，子标签：{}", subTagCodes.size(), subTagCodes);
                allTagCodes.addAll(subTagCodes);
            }

            log.info("解除标签关联，包含父标签和子标签，总数量：{}", allTagCodes.size());

//            // 解除提示词关联
//            List<String> promptCodes = tagRelationRepository.findByTagCodes(TargetType.PROMPT, allTagCodes);
//            if (CollectionUtils.isNotEmpty(promptCodes)) {
//                tagRelationRepository.removeByTarget(TargetType.PROMPT, promptCodes);
//            }
//            // 解除工作流关联
//            List<String> workflowIds = tagRelationRepository.findByTagCodes(TargetType.WORKFLOW, allTagCodes);
//            if (CollectionUtils.isNotEmpty(workflowIds)) {
//                tagRelationRepository.removeByTarget(TargetType.WORKFLOW, workflowIds);
//            }

            // 获取受影响的knowledge
            List<String> knowledgeCodes = tagRelationRepository.findByTagCodes(TargetType.Knowledge, allTagCodes);

            // 解除标签关联
            int item = tagRelationRepository.deleteByTagCodes(allTagCodes);
            log.info("解除标签关联成功，影响记录数：{}", item);

            // 更新受影响知识点的发布状态
            if (CollectionUtils.isNotEmpty(knowledgeCodes)) {
                knowledgeAppService.handleTagUpdate(knowledgeCodes);
            }
            return item > 0;
        } catch (Exception e) {
            log.error("解除标签关联失败，参数：{}，异常：{}", codeParam, e.getMessage(), e);
            throw new BizException("AA049", new String[]{"解除标签关联失败"}, "解除标签关联失败");
        }
    }

    @Override
    public List<TagDTO> selectNameAndCodeByCodes(List<String> tags) {
        return repository.selectNameAndCodeByCodes(tags);
    }

    @Override
    @IgnoreAppCode
    public List<Tree<String>> listTagTree(TagQueryParam param) {
        String parentCode = param.getParentCode();
        String appCode = param.getPubliced() ? superTenant : RequestContext.getAppCode();
        List<TagDTO> tags = repository.selectListByPath(parentCode, appCode);

        if (CollectionUtils.isNotEmpty(param.getScenarios())) {
            tags = tags.stream()
                    .filter(tag -> {
                        // 特殊处理一下市场标签
                        if (tag.getScenario() != null && param.getScenarios().contains(tag.getScenario())) {
                            return true;
                        } else if (tag.getScenario() != null && param.getScenarios().contains(TagScenarioEnum.WORKFLOW_MALL)) {
                            // 保留市场标签
                            return tag.getScenario() == TagScenarioEnum.FLOW;
                        } else if (tag.getScenario() != null && param.getScenarios().contains(TagScenarioEnum.PLUGIN_MALL)) {
                            // 保留市场标签
                            return tag.getScenario() == TagScenarioEnum.PLUGIN;
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(tags)) {
            return new ArrayList<>();
        }
        // 筛选出已关联数据的标签
        if (Boolean.TRUE.equals(param.getRelated())) {
            // 过滤掉未关联数据的标签
            List<String> tagCodeList = tags.stream().map(TagDTO::getCode).collect(Collectors.toList());
            // 获取当前场景的类型
            List<String> relatedTagCodeList = Lists.newArrayList();
            List<TagScenarioEnum> scenarios = param.getScenarios();
            for (TagScenarioEnum scenario : scenarios) {
                if (scenario == TagScenarioEnum.GLOBAL) {
                    continue;
                }
                if (scenario.equals(TagScenarioEnum.WORKFLOW_MALL) && param.getPubliced() && RequestContext.getAppSourceType().equals(AppSourceType.KS)) {
                    List<String> tagCodes = tagRelationRepository.selectByTagCodeAndTypeDialogFlow(TargetType.getByType(scenario), tagCodeList, appCode);
                    relatedTagCodeList.addAll(tagCodes);
                    continue;
                }
                List<String> tagCodes = tagRelationRepository.selectByTagCodeAndType(TargetType.getByType(scenario), tagCodeList, appCode);
                relatedTagCodeList.addAll(tagCodes);
            }
            if (CollectionUtils.isEmpty(relatedTagCodeList)) {
                return new ArrayList<>();
            }
            List<TagDTO> subTagList = tags.stream().filter(tag -> {
                for (String code : relatedTagCodeList) {
                    if (tag.getPath().contains(code)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            // 递归找到所有的父标签
            List<TagDTO> allTagList = new ArrayList<>();
            getAllTag(tags, subTagList, allTagList);
            tags = allTagList;
        }

        // 构建树形结构
        TreeNodeConfig config = new TreeNodeConfig();
        config.setIdKey("code");
        config.setParentIdKey("parentCode");
        config.setDeep(100);

        List<Tree<String>> list = TreeUtil.build(tags, parentCode, config, (node, tree) -> {
            tree.setId(node.getCode());
            tree.setName(node.getName());
            tree.setParentId(node.getParentCode());
            // 额外的值
            tree.put("description", node.getDescription());
            tree.put("scenario", node.getScenario());
        });

        return list;
    }

    /**
     * 递归获取所有父标签
     *
     * @param allTags 所有标签列表
     * @param subTags 子标签列表
     * @param result  结果集合，用于存储找到的所有父标签
     */
    private void getAllTag(List<TagDTO> allTags, List<TagDTO> subTags, List<TagDTO> result) {
        // 用于存储当前层找到的父标签
        Set<TagDTO> currentLevelParents = new HashSet<>();

        for (TagDTO tag : subTags) {
            // 如果当前标签已经在结果集中，跳过处理
            if (result.contains(tag)) {
                continue;
            }

            // 将当前标签添加到结果集
            result.add(tag);

            // 查找当前标签的直接父标签
            String parentCode = tag.getParentCode();
            if (parentCode != null) {
                allTags.stream()
                        .filter(t -> t.getCode().equals(parentCode))
                        .findFirst()
                        .ifPresent(currentLevelParents::add);
            }
        }

        // 如果找到了父标签，继续递归查找
        if (!currentLevelParents.isEmpty()) {
            getAllTag(allTags, new ArrayList<>(currentLevelParents), result);
        }
    }

//    private void updateChildrenPath(String parentCode, String newPath) {
//        List<TagDTO> children = repository.selectListByPath(parentCode, newPath);
//        for (TagDTO child : children) {
//            String oldPath = child.getPath();
//            String newPathChild = newPath + parentCode + TAG_PATH + child.getCode() + TAG_PATH;
//            child.setPath(newPathChild);
//            repository.updateByCode(child);
//            // 递归更新子节点
//            updateChildrenPath(child.getCode(), newPathChild);
//        }
//    }


    private void updateChildrenScenario(String parentCode, TagScenarioEnum scenario) {
        log.info("更新标签[{}]的所有子标签场景为[{}]", parentCode, scenario);

        // 获取所有子标签
        Set<String> children = repository.fetchAllSubTag(Collections.singletonList(parentCode));
        if (CollectionUtils.isEmpty(children)) {
            return;
        }
        repository.updateScenario(children, scenario);

    }

    @Override
    public Tree<String> listTagTreeFull(TagQueryParam param) {
        String parentCode = param.getParentCode();
        BizAssert.notEmpty(parentCode, "AA014", "父级目录不能为空");

        String name = param.getName();

        List<TagDTO> allTags = repository.fetchAllParentTagDTO(parentCode);

        if (CollectionUtils.isEmpty(allTags)) {
            return new Tree<>();
        }

        if (CollectionUtils.isNotEmpty(param.getScenarios())) {
            allTags = allTags.stream()
                    .filter(tag -> tag.getScenario() == null || param.getScenarios().contains(tag.getScenario()))
                    .collect(Collectors.toList());
        }

        TreeNodeConfig config = new TreeNodeConfig();
        config.setIdKey("code");
        config.setParentIdKey("parentCode");
        config.setNameKey("name");
        config.setDeep(100);

        // 构建完整树
        Tree<String> fullTree = TreeUtil.buildSingle(allTags, parentCode, config, TagApplicationServiceImpl::setTagInfo);

        String rootCode = fullTree.getId();
        TagDTO rootTag = allTags.stream()
                .filter(tag -> rootCode.equals(tag.getCode()))
                .findFirst()
                .orElse(null);

        if (rootTag != null) {
            setTagInfo(rootTag, fullTree);
        }

        if (StringUtils.isEmpty(name)) {
            return fullTree;
        }

        // 如果有名称搜索条件，获取符合条件的标签
        List<TagDTO> matchedTags = repository.selectListByPathAndNameAndScenario(parentCode, name, param.getScenarios(), RequestContext.getAppCode());

        if (CollectionUtils.isEmpty(matchedTags)) {
            return new Tree<>();
        }

        Set<String> secondLevelParentCodes = findSecondLevelCodes(matchedTags, parentCode);

        if (secondLevelParentCodes.isEmpty()) {
            return fullTree;
        }

        filterTreeBySecondLevelCodes(fullTree, secondLevelParentCodes);

        return fullTree;
    }


    private void filterTreeBySecondLevelCodes(Tree<String> tree, Set<String> secondLevelCodes) {
        if (tree == null || tree.getChildren() == null || tree.getChildren().isEmpty()) {
            return;
        }
        tree.getChildren().removeIf(child -> !secondLevelCodes.contains(child.getId()));
    }

    private Set<String> findSecondLevelCodes(List<TagDTO> matchedTags, String parentCode) {
        return matchedTags.stream()
                .map(tag -> {
                    String[] pathParts = tag.getPath().split(TAG_PATH);
                    List<String> validParts = Arrays.stream(pathParts)
                            .filter(StringUtils::isNotEmpty)
                            .collect(Collectors.toList());

                    int parentIndex = validParts.indexOf(parentCode);
                    if (parentIndex != -1 && parentIndex + 1 < validParts.size()) {
                        return validParts.get(parentIndex + 1);
                    }

                    if (parentCode.equals(tag.getParentCode())) {
                        return tag.getCode();
                    }

                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean refreshWhenLeafToParent(TagDTO dto) {

        String tagCode = dto.getCode();
        String parentCode = dto.getParentCode();
        // 检查是否是叶子标签
        boolean hasSubTag = repository.haveSubTag(tagCode);
        BizAssert.assertFalse(hasSubTag, "AA086", "刷新标签需要为叶子标签");

        log.info("叶子标签[{}]变为父标签，子标签：{}", parentCode, tagCode);

        // 查询父标签的关联关系，排除Knowledge类型的数据
        List<TagRelationDTO> tagRelations = tagRelationRepository.selectByTagCodeExcludeTargetType(parentCode, Collections.singletonList(TargetType.Knowledge));
        if (CollectionUtils.isEmpty(tagRelations)) {
            log.info("父标签:{}没有关联数据或只有Knowledge类型的关联数据，继续", parentCode);
        }

        tagRelations.forEach(tagRelation -> {
            tagRelation.setTagCode(tagCode);
            tagRelation.setId(null);
            tagRelation.setCode(IdGenerator.getId(StringUtils.EMPTY));
        });
        tagRelationRepository.saveBatch(tagRelations);

        log.info("父标签:{}其他标签更新涉及数量: {}", parentCode, tagRelations.size());

        // 知识点
        List<String> currentTagKnowledgeCodes = tagRelationRepository.findByTagCodes(TargetType.Knowledge, Collections.singleton(parentCode));
        if (CollectionUtils.isEmpty(currentTagKnowledgeCodes)) {
            log.info("父标签:{}没有关联知识点，跳过", parentCode);
            return true;
        }
        BatchTagParam param = new BatchTagParam();
        param.setTagCodes(Collections.singletonList(tagCode));
        List<TargetItem> targetItems = new ArrayList<>();
        param.setTargetItems(targetItems);
        for (String knowledgeCode : currentTagKnowledgeCodes) {
            TargetItem targetItem = new TargetItem();
            targetItem.setType(TargetType.Knowledge);
            targetItem.setCode(knowledgeCode);
            targetItems.add(targetItem);
        }
        log.info("父标签:{}知识标签更新涉及数量: {}", parentCode, currentTagKnowledgeCodes.size());
        knowledgeAppService.batchTagging(param);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean manualRefreshTag(String code) {
        if (StringUtils.isEmpty(code)) {
            return false;
        }

        log.info("手动刷新标签数据，标签编码：{}", code);

        // 检查标签是否存在
        TagDTO tagDTO = repository.selectByCode(code);
        if (tagDTO == null) {
            log.warn("标签不存在：{}", code);
            return false;
        }


        String parentCode = tagDTO.getParentCode();

        log.info("手动刷新标签数据,标签的父标签：{}", parentCode);

        refreshWhenLeafToParent(tagDTO);

        return true;
    }

    @Override
    public Boolean isReferenced(String tagCode) {
        List<TagRelationDTO> relations = tagRelationRepository.selectByTagCode(tagCode);
        if (CollectionUtils.isEmpty(relations)) {
            return false;
        }

        return !repository.haveSubTag(tagCode);
    }
}
