package com.chinatelecom.gs.engine.kms.sdk.vo.dify;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * Dify元数据筛选条件
 * 支持下划线和驼峰命名的兼容性
 */
@Data
public class MetadataCondition {

    /**
     * 逻辑操作符，取值为 and 或 or，默认 and
     * 支持输入：logical_operator, logicalOperator
     * 输出：logical_operator
     */
    @JsonProperty("logical_operator")
    @JsonAlias({"logicalOperator", "logical_operator"})
    private String logicalOperator = "and";

    /**
     * 条件列表
     */
    @JsonProperty("conditions")
    @JsonAlias({"conditions"})
    private List<MetadataConditionItem> conditions;
}
