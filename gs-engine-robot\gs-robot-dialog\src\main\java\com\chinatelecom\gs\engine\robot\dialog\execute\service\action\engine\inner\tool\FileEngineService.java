package com.chinatelecom.gs.engine.robot.dialog.execute.service.action.engine.inner.tool;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.corekit.common.core.AnswerService;
import com.chinatelecom.gs.engine.kms.sdk.api.FileSplitSearchApi;
import com.chinatelecom.gs.engine.kms.sdk.api.SessionFileSearchApi;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelecom.gs.engine.kms.sdk.enums.RecallType;
import com.chinatelecom.gs.engine.kms.sdk.vo.file.SessionFileQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.file.SessionFileVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.fileSearch.FileSplitSearchParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.FileItem;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchResp;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.sdk.SdkSearchResponse;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.rank.IntentRankProcessor;
import com.chinatelecom.gs.engine.robot.manage.info.domain.dto.StrategyNodeSetting;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.IntentRetrieveType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.engine.request.NluRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.engine.response.NluResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.common.Client;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.common.Header;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.common.Track;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntent;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntentAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.ChatTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.Answer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.AnswerResponseType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.ToolAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.request.AnswerBuildRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.model.RankModelConfig;
import com.chinatelecom.gs.workflow.core.workflow.core.context.DagContext;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.EnvTypeEnum;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 文件应答
 *
 * @USER: pengmc1
 * @DATE: 2025/2/26 16:03
 */

@RefreshScope
@Service
@Slf4j
public class FileEngineService implements ToolEngine {


    /*************************************es检索相关配置*************************************/
    /**
     * es检索数量
     */
    @Value("${gs.agent.fileBot.esSearch.num:15}")
    private Integer esSearchNum;
    /**
     * knn最小分数
     */
    @Value("${gs.agent.fileBot.esSearch.knnMinScore:0.25}")
    private Double knnMinScore;
    /**
     * 融合表达式
     */
    @Value("${gs.agent.fileBot.esSearch.firstRankExpression:atan_normalized_bm25(0.01)*0.5+knn_score()*0.5}")
    private String firstRankExpression;
    /**
     * 两路召回开关
     */
    @Value("${gs.agent.fileBot.esSearch.twoRecallSwitch:true}")
    private Boolean twoRecallSwitch;
    /**
     * 向量阈值
     */
    @Value("${gs.agent.fileBot.esSearch.knnThreshold:0.2}")
    private Double knnThreshold;
    /**
     * 关键词阈值
     */
    @Value("${gs.agent.fileBot.esSearch.keywordThreshold:0.2}")
    private Double keywordThreshold;


    /*************************************精排相关配置*************************************/
    /**
     * 精排阈值
     */
    @Value("${gs.agent.fileBot.rank.docAccept:0.2}")
    private Double docAccept;
    /**
     * 精排后采纳的个数
     */
    @Value("${gs.agent.fileBot.rank.topK:8}")
    private Integer topK;
    /**
     * 精排开关
     */
    @Value("${gs.agent.fileBot.rank.enableRank:false}")
    private Boolean enableRank;
    /**
     * 是否使用大模型精排
     */
    @Value("${gs.agent.fileBot.rank.enableLLMRank:true}")
    private Boolean enableLLMRank;

    /**
     * 单个文件最大大小直出
     */
    @Value("${gs.agent.fileBot.config.maxContentLength:10000}")
    private Integer maxContentLength;

    @Resource
    private FileSplitSearchApi fileSplitSearchApi;
    @Resource
    private AnswerService answerService;

    @Resource
    private IntentRankProcessor rankProcessor;

    @Resource
    private SessionFileSearchApi sessionFileSearchApi;


    @Override
    public List<ToolIntentAnswer> getRecallContents(String query, DagContext context, StrategyNodeSetting strategyNodeSetting) {
        NluRequest nluRequest = buildNluRequest(query, context, strategyNodeSetting);
        NluResponse nluResponse = this.getNluResponse(nluRequest);
        if (Objects.isNull(nluResponse) || CollectionUtils.isEmpty(nluResponse.getIntents())) {
            return Collections.emptyList();
        }

        return this.getNlgResponse(nluResponse.getIntents());
    }

    @Override
    public List<ToolIntentAnswer> getSummaryContents(String query, DagContext context, StrategyNodeSetting strategyNodeSetting) {
        NluRequest nluRequest = buildNluRequest(query, context, strategyNodeSetting);
        List<ToolIntent> toolIntents = getSummaryResponse(nluRequest);
        if (CollectionUtils.isEmpty(toolIntents)) {
            return Collections.emptyList();
        }
        return this.getNlgResponse(toolIntents);
    }


    private NluResponse getNluResponse(NluRequest nluRequest) {
        String sessionId = nluRequest.getHeader().getTrack().getSessionId();
        List<String> fileCodes = nluRequest.getHeader().getClient().getFileCodes();
        NluResponse nluResponse = new NluResponse(DialogEngineType.FILE, nluRequest.getAgentCode(), true);
        //step1 没有挂载文件，直接返回
        if (CollectionUtils.isEmpty(fileCodes)) {
            log.info("【文件应答】未挂在文件，直接返回");
            return null;
        }
        //step2 获取文件元数据
        List<SessionFileVO> sessionFileVOS = getFiles(sessionId, fileCodes);
        if (CollectionUtils.isEmpty(sessionFileVOS)) {
            log.info("【文件应答】未查询到文件元数据，直接返回");
            return null;
        }

        //step3 单文件，并且内容长度小于5000，直接全部内容
        if (sessionFileVOS.size() == 1 && sessionFileVOS.get(0).getFileContentLength() <= this.maxContentLength) {
            ToolIntent singleFileIntent = convertIntent(sessionFileVOS.get(0), nluRequest.getAgentCode());
            nluResponse.setIntents(Collections.singletonList(singleFileIntent));
            return nluResponse;
        }
        //step4 多文件
        List<ToolIntent> finalIntents = Lists.newArrayList();
        //step4.1 进行ES检索 + 精排
        List<String> esFileCodes = sessionFileVOS.stream().filter(o -> !KnowledgeType.EXCEL.equals(KnowledgeType.fromFileName(o.getFileName()))).map(SessionFileVO::getFileCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(esFileCodes)) {
            if (Boolean.TRUE.equals(this.twoRecallSwitch)) {
                //向量和关键词分开召回
                List<ToolIntent> esKnnIntents = esSearch(sessionId, esFileCodes, nluRequest, RecallType.knn);
                log.info("【文件应答】Knn向量召回结果：{}", JSON.toJSONString(esKnnIntents));
                List<ToolIntent> esKeywordIntents = esSearch(sessionId, esFileCodes, nluRequest, RecallType.keyword);
                log.info("【文件应答】关键词召回结果：{}", JSON.toJSONString(esKeywordIntents));
                List<ToolIntent> esIntents = mixKnnKeywordIntents(esKnnIntents, esKeywordIntents);
                if (CollectionUtils.isNotEmpty(esIntents)) {
                    finalIntents.addAll(esIntents);
                }
            } else {
                //向量 + 关键词融合召回
                //进行ES检索
                List<ToolIntent> esIntents = esSearch(sessionId, esFileCodes, nluRequest, RecallType.both);
                //对检索结果进行精排
                if (Boolean.TRUE.equals(this.enableRank)) {
                    RankModelConfig rankModelConfig = new RankModelConfig();
                    rankModelConfig.setTopK(this.topK);
                    List<ToolIntent> docRankIntents = rankProcessor.rankWithModel(esIntents, nluRequest.getProcessContent(), nluRequest.getHeader().getTrack().getMessageId(), rankModelConfig);
                    List<ToolIntent> docFilterIntents = docRankIntents.stream().filter(o -> o.getScore() >= this.docAccept).collect(Collectors.toList());
                    //按分数进行排序，从高到底
                    if (CollectionUtils.isNotEmpty(docFilterIntents)) {
                        docFilterIntents = docFilterIntents.stream().sorted(Comparator.comparing(ToolIntent::getScore).reversed()).collect(Collectors.toList());
                        finalIntents.addAll(docFilterIntents);
                    }
                } else if (CollectionUtils.isNotEmpty(esIntents)) {
                    esIntents = esIntents.stream().sorted(Comparator.comparing(ToolIntent::getScore).reversed()).limit(this.topK).collect(Collectors.toList());
                    finalIntents.addAll(esIntents);
                }
            }
        }
        ///step4.2 判断是否有excel文件，如果有直接添加到意图中
        List<SessionFileVO> excelSessionFiles = sessionFileVOS.stream().filter(o -> KnowledgeType.EXCEL.equals(KnowledgeType.fromFileName(o.getFileName()))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(excelSessionFiles)) {
            List<ToolIntent> excelIntents = excelSessionFiles.stream().map(o -> convertIntent(o, nluRequest.getAgentCode())).collect(Collectors.toList());
            finalIntents.addAll(excelIntents);
        }
        nluResponse.setIntents(limitContentLength(finalIntents, this.maxContentLength));
        log.info("【文件应答】文件应答nluResponse结果：{}", JSON.toJSONString(nluResponse));
        return nluResponse;
    }

    /**
     * 融合两种类型的意图结果
     *
     * @param knnIntents
     * @param keywordIntents
     * @return
     */
    protected List<ToolIntent> mixKnnKeywordIntents(List<ToolIntent> knnIntents, List<ToolIntent> keywordIntents) {
        List<ToolIntent> mixIntents = new ArrayList<>();
        int knnSize = 0;
        int keywordSize = 0;
        if (CollectionUtils.isNotEmpty(knnIntents)) {
            knnIntents = knnIntents.stream().filter(o -> o.getScore() >= this.knnThreshold).sorted(Comparator.comparing(ToolIntent::getScore).reversed()).limit(this.topK).collect(Collectors.toList());
            knnSize = knnIntents.size();
        }
        if (CollectionUtils.isNotEmpty(keywordIntents)) {
            keywordIntents = keywordIntents.stream().filter(o -> o.getScore() >= this.keywordThreshold).sorted(Comparator.comparing(ToolIntent::getScore).reversed()).limit(this.topK).collect(Collectors.toList());
            keywordSize = keywordIntents.size();
        }
        if (knnSize == 0 && keywordSize == 0) {
            return mixIntents;
        } else if (knnSize > 0 && keywordSize == 0) {
            return knnIntents;
        } else if (knnSize == 0 && keywordSize > 0) {
            return keywordIntents;
        } else if (knnSize >= this.topK) {
            return knnIntents.subList(0, this.topK);
        } else {
            mixIntents.addAll(knnIntents);
            Map<String, ToolIntent> knnIntentMap = knnIntents.stream().collect(Collectors.toMap(ToolIntent::getToolIntentId, Function.identity()));
            for (ToolIntent keywordIntent : keywordIntents) {
                if (mixIntents.size() >= this.topK) {
                    break;
                }
                if (!knnIntentMap.containsKey(keywordIntent.getToolIntentId())) {
                    mixIntents.add(keywordIntent);
                }
            }
        }
        return mixIntents;
    }

    /**
     * 获取文件元数据
     *
     * @param sessionId 会话ID
     * @param fileCodes 文件编码列表
     * @return
     */
    private List<SessionFileVO> getFiles(String sessionId, List<String> fileCodes) {
        SessionFileQueryParam sessionFileQueryParam = new SessionFileQueryParam();
        sessionFileQueryParam.setSessionId(sessionId);
        sessionFileQueryParam.setFileCodes(fileCodes);
        sessionFileQueryParam.setViewContent(true);
        try {
            log.info("【文件应答】获取文件元数据，请求参数：{}", JSON.toJSONString(sessionFileQueryParam));
            Result<List<SessionFileVO>> fileResult = sessionFileSearchApi.files(sessionFileQueryParam);
            log.info("【文件应答】获取文件元数据，返回结果：{}", JSON.toJSONString(fileResult));
            if (Objects.nonNull(fileResult)) {
                return fileResult.getData();
            }
        } catch (Exception e) {
            log.error("【文件应答】获取文件元数据发生异常，请求为：{}", JSON.toJSONString(sessionFileQueryParam), e);
        }
        return null;
    }

    /**
     * 进行ES意图识别
     *
     * @param sessionId
     * @param fileCodes
     * @param nluRequest
     * @return
     */
    private List<ToolIntent> esSearch(String sessionId, List<String> fileCodes, NluRequest nluRequest, RecallType recallType) {
        FileSplitSearchParam fileSplitSearchParam = new FileSplitSearchParam();
        fileSplitSearchParam.setSessionId(sessionId);
        fileSplitSearchParam.setFileCodes(fileCodes);
        fileSplitSearchParam.setQuery(nluRequest.getProcessContent());
        fileSplitSearchParam.setSize(this.esSearchNum);
        fileSplitSearchParam.setKnnMinScore(this.knnMinScore);
        fileSplitSearchParam.setFirstRankExpression(this.firstRankExpression);
        fileSplitSearchParam.setRecallType(Objects.nonNull(recallType) ? recallType : RecallType.both);
        SearchResp<FileItem> docSearchResult = null;
        try {
            docSearchResult = fileSplitSearchApi.search(fileSplitSearchParam);
        } catch (Exception e) {
            log.warn("【文件应答】文件分片检索调用失败,参数为：{}", JsonUtils.toJsonString(fileSplitSearchParam), e);
        }
        if (Objects.isNull(docSearchResult) || Objects.isNull(docSearchResult.getData()) || CollectionUtils.isEmpty(docSearchResult.getData().getItems())) {
            log.info("【文件应答】未检索到内容，直接返回");
            return null;
        }
        List<ToolIntent> docTempIntents = new ArrayList<>();
        for (SdkSearchResponse.SdkRetrieveItem<FileItem> searchFileResponse : docSearchResult.getData().getItems()) {
            ToolIntent intent = convertIntent(searchFileResponse, nluRequest.getAgentCode());
            docTempIntents.add(intent);
        }
        return docTempIntents;
    }

    /**
     * 检索内容转换为意图
     *
     * @param response
     * @param agentCode
     * @return
     */
    private ToolIntent convertIntent(SdkSearchResponse.SdkRetrieveItem<FileItem> response, String agentCode) {
        ToolIntent intent = new ToolIntent();
        FileItem source = response.getSource();
        intent.setParentCode(source.getFileCode());
        intent.setParentName(source.getFileName());
        intent.setRetrieveType(IntentRetrieveType.DOC);
        intent.setToolIntentId(source.getId());
        intent.setToolIntentName(source.getTitle());
        intent.setSimilarContent(source.getContent());
        intent.setScore(response.getScore());
        intent.setDialogEngineType(DialogEngineType.FILE);
        intent.setResponseType(AnswerResponseType.LLM);
        if (source.getSource() != null) {
            HashMap<String, Object> extraData = new HashMap<>();
            extraData.put("source", source.getSource());
            intent.setExtraData(extraData);
        }

        AnswerBuildRequest answerBuildRequest = new AnswerBuildRequest();
        answerBuildRequest.setContent(source.getContent());
        if (source.getContent() == null) {
            answerBuildRequest.setContent("文档内容返回空");
        }
        answerBuildRequest.setAnswerTypeEnum(AnswerTypeEnum.PLAIN_TEXT);
        Answer answer = this.answerService.buildAnswer(answerBuildRequest);

        Map<String, Object> answerExtra = new HashMap<>();
        answerExtra.put("candidateExtra", source.getMetaData());
        answer.setExtraData(answerExtra);
        intent.setContent(answer);
        return intent;
    }

    /**
     * 文件内容转换为意图
     *
     * @param sessionFileVO
     * @param agentCode
     * @return
     */
    private ToolIntent convertIntent(SessionFileVO sessionFileVO, String agentCode) {
        ToolIntent intent = new ToolIntent();
        intent.setParentCode(sessionFileVO.getFileCode());
        intent.setParentName(sessionFileVO.getFileName());
        intent.setRetrieveType(IntentRetrieveType.DOC);
        intent.setToolIntentId(sessionFileVO.getFileCode());
        intent.setToolIntentName(sessionFileVO.getFileName());
        intent.setSimilarContent(sessionFileVO.getFileContent());
        intent.setScore(1.0);
        intent.setDialogEngineType(DialogEngineType.FILE);
        intent.setResponseType(AnswerResponseType.LLM);
        AnswerBuildRequest answerBuildRequest = new AnswerBuildRequest();
        answerBuildRequest.setContent(sessionFileVO.getFileContent());
        answerBuildRequest.setAnswerTypeEnum(AnswerTypeEnum.PLAIN_TEXT);
        Answer answer = this.answerService.buildAnswer(answerBuildRequest);
        intent.setContent(answer);
        return intent;
    }

    private List<ToolIntentAnswer> getNlgResponse(List<ToolIntent> toolIntents) {

        List<ToolIntentAnswer> intentAnswers = toolIntents.stream().map(dmIntent -> {
            Object answerObj = dmIntent.getContent();
            if (Objects.isNull(answerObj)) {
                return null;
            }

            Answer answer = (Answer) answerObj;
            ToolAnswer intentAnswer = BeanUtil.toBean(answer, ToolAnswer.class);
            ToolIntentAnswer toolIntentAnswer = BeanUtil.toBean(dmIntent, ToolIntentAnswer.class);
            toolIntentAnswer.setToolAnswer(intentAnswer);
            return toolIntentAnswer;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(intentAnswers)) {
            return Collections.emptyList();
        }

        return intentAnswers;
    }

    public List<ToolIntent> getSummaryResponse(NluRequest nluRequest) {
        String sessionId = nluRequest.getHeader().getTrack().getSessionId();
        List<String> fileCodes = nluRequest.getHeader().getClient().getFileCodes();
        //step1 没有挂载文件，直接返回
        if (CollectionUtils.isEmpty(fileCodes)) {
            log.info("【文件应答】未挂在文件，直接返回");
            return null;
        }
        //step2 获取文件元数据
        List<SessionFileVO> sessionFileVOS = getFiles(sessionId, fileCodes);
        if (CollectionUtils.isEmpty(sessionFileVOS)) {
            log.info("【文件应答】未查询到文件元数据，直接返回");
            return Collections.emptyList();
        }
        List<ToolIntent> summaryIntents = Lists.newArrayList();
        //按比例进行截取
        long totalLength = sessionFileVOS.stream().map(SessionFileVO::getFileContentLength).reduce(0, Integer::sum).longValue();
        for (SessionFileVO sessionFileVO : sessionFileVOS) {
            if (totalLength > this.maxContentLength) {
                //截取内容
                long subLength = (long) sessionFileVO.getFileContentLength() * this.maxContentLength / totalLength;
                if (subLength > 0) {
                    sessionFileVO.setFileContent(sessionFileVO.getFileContent().substring(0, (int) subLength));
                    summaryIntents.add(convertIntent(sessionFileVO, nluRequest.getAgentCode()));
                }
            } else {
                summaryIntents.add(convertIntent(sessionFileVO, nluRequest.getAgentCode()));
            }
        }

        return summaryIntents;
    }

    /**
     * 限制意图相似问的总长度
     *
     * @param origIntents 原始意图列表
     * @param maxLength   最大长度
     * @return
     */
    public List<ToolIntent> limitContentLength(List<ToolIntent> origIntents, Integer maxLength) {
        if (CollectionUtils.isEmpty(origIntents)) {
            return origIntents;
        }
        List<ToolIntent> finalIntents = Lists.newArrayList();
        Integer totalLength = 0;
        for (ToolIntent intent : origIntents) {
            if (StringUtils.isNotEmpty(intent.getSimilarContent())) {
                Integer intentLength = intent.getSimilarContent().length();
                totalLength += intentLength;
                if (totalLength < maxLength) {
                    finalIntents.add(intent);
                } else {
                    //截取内容
                    intent.setSimilarContent(intent.getSimilarContent().substring(0, intentLength - (totalLength - maxLength)));
                    finalIntents.add(intent);
                    log.info("【文件应答】意图内容过长进行了截取，原始意图个数：{}，截取后意图个数：{}", origIntents.size(), finalIntents.size());
                    break;
                }
            }
        }
        return finalIntents;
    }


    @Override
    public String getName() {
        return DialogEngineType.FILE.getCode();
    }


    private NluRequest buildNluRequest(String query, DagContext context, StrategyNodeSetting strategyNodeSetting) {
        NluRequest nluRequest = new NluRequest();
        nluRequest.setAgentCode(context.getAgentCode());
        nluRequest.setProcessContent(query);
        nluRequest.setChatType(ChatTypeEnum.CHAT.getCode());
        Header header = new Header();
        Track track = new Track();
        track.setMessageId(context.getMessageId());
        track.setSessionId(context.getSessionId());
        Client client = new Client();
        client.setTest(EnvTypeEnum.TEST.equals(context.getEnv()));
        Object fileCodeObj = MapUtils.getObject(context.getInputParamMap(), "FILE_USER");
        if (Objects.nonNull(fileCodeObj)) {
            List<String> fileCodes = (List<String>) fileCodeObj;
            client.setFileCodes(fileCodes);
        }
        header.setTrack(track);
        header.setClient(client);
        nluRequest.setHeader(header);
        return nluRequest;
    }
}
