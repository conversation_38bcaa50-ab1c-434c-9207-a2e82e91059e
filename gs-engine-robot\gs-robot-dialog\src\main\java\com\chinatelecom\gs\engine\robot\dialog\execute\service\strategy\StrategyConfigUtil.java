package com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy;

import cn.hutool.core.bean.BeanUtil;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.AgentStrategyConfig;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.StrategyNodeContext;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.StrategyNodeRequest;
import com.chinatelecom.gs.engine.robot.manage.info.domain.dto.StrategyNodeSetting;
import com.chinatelecom.gs.engine.robot.sdk.enums.InteractionTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.AgentConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.KmsConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.ModelConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.engine.request.DialogEngineRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.common.Header;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.Instruction;
import com.chinatelecom.gs.workflow.core.workflow.core.context.DagContext;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.EnvTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.model.dag.DagParam;

import java.util.HashMap;
import java.util.Map;

public class StrategyConfigUtil {

    public static StrategyNodeContext buildContext(DialogEngineRequest dialogEngineRequest, AgentConfig agentConfig, String query){
        StrategyNodeContext strategyNodeContext = new StrategyNodeContext();

        AgentStrategyConfig agentStrategyConfig = BeanUtil.toBean(dialogEngineRequest, AgentStrategyConfig.class);
        agentStrategyConfig.setAgentConfig(agentConfig);
        agentStrategyConfig.setActionCallback(dialogEngineRequest.getActionCallBack());
        agentStrategyConfig.setMessageCallback(dialogEngineRequest.getMessageCallback());
        agentStrategyConfig.setChatType("chat");
        agentStrategyConfig.setOriginContent(query);
        agentStrategyConfig.setBusinessCode(agentConfig.getAgentCode());

        strategyNodeContext.setAgentStrategyConfig(agentStrategyConfig);

        StrategyNodeRequest strategyNodeRequest = new StrategyNodeRequest();
        strategyNodeRequest.setAgentCode(dialogEngineRequest.getAgentCode());
        strategyNodeRequest.setUserQuery(query);
        strategyNodeRequest.setQuery(query);
        strategyNodeContext.setStrategyNodeRequest(strategyNodeRequest);


        StrategyNodeSetting strategyNodeSetting = new StrategyNodeSetting();

        StrategyNodeSetting.SkillSetting skillSetting = new StrategyNodeSetting.SkillSetting();
        skillSetting.setEnableBotSkills(true);
        strategyNodeSetting.setSkillParam(skillSetting);

        StrategyNodeSetting.KmsSetting  kmsSetting = new StrategyNodeSetting.KmsSetting();
        KmsConfig kmsConfig = agentConfig.getKmsConfig();
        kmsSetting.setSearchStrategy(kmsConfig.getSearchStrategy().getCode());
        kmsSetting.setDoc(kmsConfig.getDoc());
        kmsSetting.setFaq(kmsConfig.getFaq());
        strategyNodeSetting.setKmsParam(kmsSetting);

        strategyNodeSetting.setPrompt(query);
        strategyNodeSetting.setSystemPrompt(agentConfig.getBasicConfig().getAgentReply());

        ModelConfig modelConfig = agentConfig.getModelConfig();
        strategyNodeSetting.setModelCode(modelConfig.getModelCode());
        strategyNodeSetting.setModelName(modelConfig.getModelName());
        strategyNodeSetting.setMaxTokens(modelConfig.getLlmConfig().getMaxTokens());
        strategyNodeSetting.setTemperature(modelConfig.getLlmConfig().getTemperature());
        strategyNodeSetting.setTopP(0.001f);

        strategyNodeContext.setStrategyNodeSetting(strategyNodeSetting);

        return strategyNodeContext;
    }


    public static DagContext buildDagContext(DialogEngineRequest dialogEngineRequest, Instruction instruction, String query){
        Header header = dialogEngineRequest.getHeader();
        String messageId = header.getTrack().getMessageId();
        String sessionId = header.getTrack().getSessionId();

        Map<String, Object> inputs = new HashMap<>();
        inputs.put("BOT_USER_INPUT", dialogEngineRequest.getProcessContent());
        inputs.put("FILE_USER", dialogEngineRequest.getHeader().getClient().getFileCodes());
        inputs.put("INPUT_EXTRA", dialogEngineRequest.getExtraData());

        Map<String, Object> globalVar = new HashMap<>();
        globalVar.put("BOT_USER_INPUT", dialogEngineRequest.getProcessContent());
        globalVar.put("FILE_USER", dialogEngineRequest.getHeader().getClient().getFileCodes());
        globalVar.put("INPUT_EXTRA", dialogEngineRequest.getExtraData());

        DagParam param = DagParam.builder()
                .sessionId(sessionId)
                .messageId(messageId)
                .interactionType(InteractionTypeEnum.ASYNC)
                .messageCallback(dialogEngineRequest.getMessageCallback())
                .actionCallBack(dialogEngineRequest.getActionCallBack())
                .userId(RequestContext.get().getUserId())
                .appCode(RequestContext.getAppCode())
                .agentCode(dialogEngineRequest.getAgentCode())
                .isSystemAgent(dialogEngineRequest.getIsSystemAgent())
                .env(dialogEngineRequest.getHeader().getClient().getTest() ? EnvTypeEnum.TEST : EnvTypeEnum.PRODUCT)
                .globalVarMap(globalVar)
                .inputParamMap(inputs)
                .agentCode(dialogEngineRequest.getAgentCode())
                .downMessageId(header.getTrack().getDownMessageId())
                .chatType(dialogEngineRequest.getChatType())
                .safeFenceSwitch(dialogEngineRequest.getHeader().getClient().getSafeFenceSwitch())
                .chatLLMSwitch(dialogEngineRequest.getHeader().getClient().getChatLLMSwitch())
                .instruction(instruction)
                .build();

        DagContext dagContext = new DagContext();
        dagContext.setDagParam(param);
        return dagContext;
    }
}
