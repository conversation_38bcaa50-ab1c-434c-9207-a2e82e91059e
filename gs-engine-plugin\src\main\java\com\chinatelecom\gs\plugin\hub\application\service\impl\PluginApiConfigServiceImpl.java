package com.chinatelecom.gs.plugin.hub.application.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.chinatelecom.cloud.platform.client.rpc.PlatformUser;
import com.chinatelecom.cloud.platform.client.util.SsoUtil;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.enums.BizTypeEnum;
import com.chinatelecom.gs.engine.common.enums.InternalPluginNameEnum;
import com.chinatelecom.gs.engine.common.enums.LogStatusEnum;
import com.chinatelecom.gs.engine.common.enums.LogTypeEnum;
import com.chinatelecom.gs.engine.common.log.track.InvokeProcessMessage;
import com.chinatelecom.gs.engine.common.log.track.LogMessage;
import com.chinatelecom.gs.engine.common.log.track.LogMqProducer;
import com.chinatelecom.gs.engine.common.utils.DataResourceAccess;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.enums.PluginStatusEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.spi.agent.client.AgentInfoRpcApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.model.PluginDefaultParamsRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.request.*;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.plugin.response.*;
import com.chinatelecom.gs.plugin.hub.application.bizcheck.BizCheck;
import com.chinatelecom.gs.plugin.hub.application.convertor.PluginApiConvertor;
import com.chinatelecom.gs.plugin.hub.application.service.PluginApiConfigService;
import com.chinatelecom.gs.plugin.hub.application.service.PluginMallInfoConfigService;
import com.chinatelecom.gs.plugin.hub.application.service.PluginMcpService;
import com.chinatelecom.gs.plugin.hub.application.service.PluginMetaConfigService;
import com.chinatelecom.gs.plugin.hub.application.tunnel.PluginTunnelFacade;
import com.chinatelecom.gs.plugin.hub.infra.dto.PluginApiDTO;
import com.chinatelecom.gs.plugin.hub.infra.dto.PluginApiParamDTO;
import com.chinatelecom.gs.plugin.hub.infra.dto.PluginMetaDTO;
import com.chinatelecom.gs.plugin.hub.infra.enums.PluginServiceTypeEnum;
import com.chinatelecom.gs.plugin.hub.infra.enums.ReqRspEnum;
import com.chinatelecom.gs.plugin.hub.infra.handler.InternalPluginExecutor;
import com.chinatelecom.gs.plugin.hub.infra.handler.InternalPluginHolder;
import com.chinatelecom.gs.plugin.hub.infra.handler.InternalPluginRequest;
import com.chinatelecom.gs.plugin.hub.infra.po.PluginApiPO;
import com.chinatelecom.gs.plugin.hub.infra.repository.PluginApiParamRepository;
import com.chinatelecom.gs.plugin.hub.infra.repository.PluginApiRepository;
import com.chinatelecom.gs.plugin.hub.infra.repository.PluginMetaRepository;
import com.chinatelecom.gs.plugin.hub.infra.repository.PluginVersionInfoRepository;
import com.chinatelecom.gs.plugin.hub.infra.service.PluginMallInfoService;
import com.chinatelecom.gs.plugin.hub.infra.service.impl.PluginApiServiceImpl;
import com.chinatelecom.gs.plugin.hub.model.*;
import com.chinatelecom.gs.plugin.hub.model.PluginApiRequest;
import com.chinatelecom.gs.workflow.core.workflow.core.utils.LogUtils;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class PluginApiConfigServiceImpl implements PluginApiConfigService {

    private static final String PLUGIN_TYPE = "pluginType";

    @Value("${app.plugin.redirectUrl:}")
    private String redirectUrl;

    @Value("${plugin.whitelist.ips:}")
    private String whitelistIps;

    @Value("${plugin.whitelist.switch:false}")
    private Boolean whitelistSwitch;

    @Autowired
    private PluginVersionInfoRepository pluginVersionInfoRepository;

    @Autowired
    private PluginApiRepository pluginApiRepository;

    @Autowired
    private PluginMetaRepository pluginMetaRepository;

    @Autowired
    private PluginApiParamRepository pluginApiParamRepository;

    private final PluginApiConvertor convertor = PluginApiConvertor.INSTANCE;
    @Autowired
    private PluginApiServiceImpl pluginApiServiceImpl;

    @Autowired
    private DataResourceAccess dataResourceAccess;

    @Autowired
    private PluginMetaConfigService pluginMetaConfigService;

    @Autowired
    private InternalPluginHolder internalPluginHolder;

    @Autowired
    private AgentInfoRpcApi agentInfoRpcApi;

    @Resource
    private LogMqProducer logMqProducer;

    @Autowired
    private PluginMallInfoConfigService pluginMallInfoConfigService;

    @Autowired
    private PluginMallInfoService pluginMallInfoService;

    @Resource
    private PluginMcpService pluginMcpService;

    @Override
    public PluginApiInfo getPluginApi(PluginMetaDTO pluginMetaDTO, String apiId) {
        PluginApiDTO pluginApiDTO = pluginApiRepository.getByIdAndVersion(pluginMetaDTO.getPluginId(), apiId, pluginMetaDTO.getVersion());
        if (pluginApiDTO == null) {
            throw new BizException("AB002", "工具不存在");
        }
        PluginApiInfo pluginApiInfo = new PluginApiInfo();
        pluginApiInfo.setPluginId(pluginApiDTO.getPluginId());
        pluginApiInfo.setPluginIcon(pluginMetaDTO.getPluginIcon());
        pluginApiInfo.setApiId(pluginApiDTO.getApiId());
        pluginApiInfo.setApiName(pluginApiDTO.getApiName());
        pluginApiInfo.setApiDesc(pluginApiDTO.getApiDesc());
        pluginApiInfo.setPath(pluginApiDTO.getPath());
        pluginApiInfo.setMethod(pluginApiDTO.getMethod());
        pluginApiInfo.setServiceStatus(pluginApiDTO.getServiceStatus());
        pluginApiInfo.setDebugStatus(pluginApiDTO.getDebugStatus());
        pluginApiInfo.setPluginName(pluginMetaDTO.getPluginName());
        pluginApiInfo.setEnabled(pluginApiDTO.getEnabled());
        pluginApiInfo.setCreateTime(pluginApiDTO.getCreateTime());

        List<PluginApiParam> requestParamList = getApiParam(pluginApiDTO, ReqRspEnum.REQUEST.getCode(), pluginMetaDTO.getVersion());
        List<PluginApiParam> responseParamList = getApiParam(pluginApiDTO, ReqRspEnum.RESPONSE.getCode(), pluginMetaDTO.getVersion());
        pluginApiInfo.setRequestParams(requestParamList);
        pluginApiInfo.setResponseParams(responseParamList);
        return pluginApiInfo;
    }

    @Override
    public PluginApiInfo getPluginApiDetail(String pluginId, String apiId, Boolean online) {
        if (StringUtils.isEmpty(pluginId)) {
            // 通过apiId查询查询出一个插件
            PluginApiDTO pluginApiDTO = pluginApiRepository.getByApiId(apiId);
            if (pluginApiDTO == null) {
                throw new BizException("AB002", "工具不存在");
            }
            pluginId = pluginApiDTO.getPluginId();
        }
        Long version;
        if (Boolean.TRUE.equals(online)) {
            version = pluginVersionInfoRepository.getPublishVersion(pluginId);
        } else {
            version = pluginVersionInfoRepository.getEditVersion(pluginId);
        }
        // 检查插件是否存在
        PluginMetaDTO pluginMetaDTO = pluginMetaRepository.queryByIdAndVersion(pluginId, version);
        if (pluginMetaDTO == null) {
            throw new BizException("AB001", "插件不存在");
        }

        PluginApiDTO pluginApiDTO = pluginApiRepository.getByIdAndVersion(pluginId, apiId, version);
        if (pluginApiDTO == null) {
            throw new BizException("AB002", "工具不存在");
        }
        // 查询工具参数, 根据类型查询请求参数和响应参数
        List<PluginApiParam> requestParamList = getApiParam(pluginApiDTO, ReqRspEnum.REQUEST.getCode(), version);
        List<PluginApiParam> responseParamList = getApiParam(pluginApiDTO, ReqRspEnum.RESPONSE.getCode(), version);
        PluginApiInfo pluginApiInfo = new PluginApiInfo();
        pluginApiInfo.setPluginId(pluginApiDTO.getPluginId());
        pluginApiInfo.setPluginIcon(pluginMetaDTO.getPluginIcon());
        pluginApiInfo.setApiId(pluginApiDTO.getApiId());
        pluginApiInfo.setApiName(pluginApiDTO.getApiName());
        pluginApiInfo.setApiDesc(pluginApiDTO.getApiDesc());
        pluginApiInfo.setPath(pluginApiDTO.getPath());
        pluginApiInfo.setMethod(pluginApiDTO.getMethod());
        pluginApiInfo.setServiceStatus(pluginApiDTO.getServiceStatus());
        pluginApiInfo.setDebugStatus(pluginApiDTO.getDebugStatus());
        pluginApiInfo.setPluginName(pluginMetaDTO.getPluginName());
        pluginApiInfo.setEnabled(pluginApiDTO.getEnabled());
        pluginApiInfo.setCreateTime(pluginApiDTO.getCreateTime());
        pluginApiInfo.setRequestParams(requestParamList);
        pluginApiInfo.setResponseParams(responseParamList);
        return pluginApiInfo;
    }

    @Override
    public PluginInfoResponse queryPluginApiInfo(String apiId) {
        PluginApiDTO apiDTO = pluginApiRepository.getByApiId(apiId);
        if (Objects.isNull(apiDTO)) {
            return null;
        }
        String pluginId = apiDTO.getPluginId();
        PluginInfoResponse pluginInfoResponse = new PluginInfoResponse();
        // 根据pluginId和apiId查询对应的agent配置信息, 区分环境
        Long version = pluginVersionInfoRepository.getPublishVersion(pluginId);

        PluginMetaDTO pluginMetaDTO = pluginMetaRepository.queryByIdAndVersion(pluginId, version);
        if (pluginMetaDTO == null) {
            return null;
        }
        BeanUtils.copyProperties(pluginMetaDTO, pluginInfoResponse);
        // 查询api和参数
        PluginApiDTO pluginApiDTO = pluginApiRepository.queryApiByApiId(pluginId, apiId, version);
        if (pluginApiDTO != null) {
            PluginApiInfo pluginApiDetail = getPluginApiDetail(pluginId, apiId, true);
            pluginInfoResponse.setPluginApiInfo(pluginApiDetail);
        }
        return pluginInfoResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPluginApi(PluginApiRequest request) {
        RequestInfo userInfo = RequestContext.get();
        dataResourceAccess.getResourceData(request.getPluginId(), userInfo.getTenantId(), PLUGIN_TYPE, userInfo.getUserId());
        // 获取插件编辑版本
        Long editVersion = pluginVersionInfoRepository.getEditVersion(request.getPluginId());
        // 检查插件是否存在
        PluginMetaDTO pluginMetaDTO = pluginMetaRepository.queryByIdAndVersion(request.getPluginId(), editVersion);
        if (pluginMetaDTO == null) {
            throw new BizException("AB001", "插件不存在");
        }
        if (PluginServiceTypeEnum.MCP.getType().equals(pluginMetaDTO.getServiceType())) {
            throw new BizException("AB031", "MCP插件不能手动创建工具");
        }

        BizCheck.checkPluginApiRequest(request);
        checkName(request, editVersion);
        String apiId = IdGenerator.getId(null);

        PluginApiDTO pluginApiDTO = convertor.convert(request);
        pluginApiDTO.setApiId(apiId);
        pluginApiDTO.setPluginId(pluginMetaDTO.getPluginId());
        pluginApiDTO.setVersion(editVersion);
        pluginApiDTO.setServiceStatus(0);
        pluginApiDTO.setEnabled(true);
        pluginApiDTO.setDebugStatus(0);
        pluginApiDTO.setYn(0L);
        pluginApiDTO.setCreateId(userInfo.getUserId());
        pluginApiDTO.setCreateName(userInfo.getUserName());
        pluginApiDTO.setCreateTime(LocalDateTime.now());
        pluginApiDTO.setUpdateId(userInfo.getUserId());
        pluginApiDTO.setUpdateName(userInfo.getUserName());
        pluginApiDTO.setUpdateTime(LocalDateTime.now());
        pluginApiDTO.setAppCode(userInfo.getAppCode());
        pluginApiRepository.save(pluginApiDTO);

        // 检查插件状态, 如果是在已经发布的插件查新建工具, 则需要将插件状态变为更新待发布
        Integer status = Objects.equals(pluginMetaDTO.getStatus(), PluginStatusEnum.PUBLISHED.getCode()) ? PluginStatusEnum.MODIFY.getCode() : pluginMetaDTO.getStatus();
        pluginMetaDTO.setStatus(status);
        pluginMetaDTO.setUpdateId(userInfo.getUserId());
        pluginMetaDTO.setUpdateName(userInfo.getUserName());
        pluginMetaDTO.setUpdateTime(LocalDateTime.now());
        pluginMetaRepository.updateById(pluginMetaDTO);
        return apiId;
    }

    @Override
    public List<PluginApiInfo> queryPluginApiListForExecute(String pluginId, Long version) {
        List<PluginApiDTO> list = pluginApiRepository.queryByPluginIdAndVersion(pluginId, version);
        List<PluginApiInfo> pluginApiInfos = Lists.newArrayList();
        if (!CollectionUtils.isNotEmpty(list)) {
            return pluginApiInfos;
        }
        list.forEach(pluginApiDTO -> {
            PluginApiInfo pluginApiInfo = new PluginApiInfo();
            BeanUtils.copyProperties(pluginApiDTO, pluginApiInfo);
            // 查询请求api参数
            List<PluginApiParam> requestParam = getApiParam(pluginApiDTO, ReqRspEnum.REQUEST.getCode(), version);
            pluginApiInfo.setRequestParams(requestParam);
            // 查询响应api参数
            List<PluginApiParam> responseParam = getApiParam(pluginApiDTO, ReqRspEnum.RESPONSE.getCode(), version);
            pluginApiInfo.setResponseParams(responseParam);
            pluginApiInfos.add(pluginApiInfo);
        });
        return pluginApiInfos;
    }

    @Override
    public Page<PluginApiInfo> queryPluginApiList(QueryPluginApiParam queryParam) {
        // 获取插件编辑版本
        Long version;
        if (Boolean.TRUE.equals(queryParam.getOnline())) {
            version = pluginVersionInfoRepository.getPublishVersion(queryParam.getPluginId());
        } else {
            version = pluginVersionInfoRepository.getEditVersion(queryParam.getPluginId());
        }

        // 检查插件是否存在
        PluginMetaDTO pluginMetaDTO = pluginMetaRepository.queryByIdAndVersion(queryParam.getPluginId(), version);
        if (pluginMetaDTO == null) {
            throw new BizException("AB001", "插件不存在");
        }
        queryParam.setVersion(version);

        Page<PluginApiDTO> pluginApiDTOPage = pluginApiRepository.pageQuery(queryParam);

        List<PluginApiInfo> pluginApiInfos = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(pluginApiDTOPage.getRecords())) {
            pluginApiDTOPage.getRecords().forEach(pluginApiDTO -> {
                PluginApiInfo pluginApiInfo = new PluginApiInfo();
                BeanUtils.copyProperties(pluginApiDTO, pluginApiInfo);
                // 查询请求api参数
                List<PluginApiParam> requestParam = getApiParam(pluginApiDTO, ReqRspEnum.REQUEST.getCode(), version);
                pluginApiInfo.setRequestParams(requestParam);
                // 查询响应api参数
                List<PluginApiParam> responseParam = getApiParam(pluginApiDTO, ReqRspEnum.RESPONSE.getCode(), version);
                pluginApiInfo.setResponseParams(responseParam);
                Result<Integer> result = agentInfoRpcApi.queryBotRefNumByApiId(pluginApiDTO.getApiId());
                if (result != null && result.getData() != null) {
                    pluginApiInfo.setBotRefCount(result.getData());
                }
                pluginApiInfos.add(pluginApiInfo);
            });
        }
        PageImpl<PluginApiInfo> page = new PageImpl<>();
        page.setCurrent(pluginApiDTOPage.getCurrent());
        page.setTotal(pluginApiDTOPage.getTotal());
        page.setSize(pluginApiDTOPage.getSize());
        page.setPages(pluginApiDTOPage.getPages());
        page.setRecords(pluginApiInfos);
        return page;
    }

    @Override
    public ExecutePluginApiResponse executePluginApi(ExecutePluginApiRequest debugPluginApiRequest, boolean test) {
        AgentBindApiParamsResponse response = null;

        if (StringUtils.isNotEmpty(debugPluginApiRequest.getAgentCode())) {
            PluginDefaultParamsRequest request = new PluginDefaultParamsRequest();
            request.setAgentCode(debugPluginApiRequest.getAgentCode());
            request.setApiId(debugPluginApiRequest.getApiId());
            request.setTest(debugPluginApiRequest.isAgentTest());
            Result<AgentBindApiParamsResponse> agentBindApiParamsResponseResult = agentInfoRpcApi.queryBotDefaultParams(request);
            if (Boolean.TRUE.equals(agentBindApiParamsResponseResult.isSuccess()) && agentBindApiParamsResponseResult.getData() != null) {
                response = agentBindApiParamsResponseResult.getData();
            }
        }

        PluginInfoResponse toolInfo = queryPluginApiInfoByEnv(debugPluginApiRequest.getApiId(), test);
        if (Objects.isNull(toolInfo)) {
            throw new BizException("AB002", "工具不存在");
        }
        if (toolInfo.getCreateApiType().equals(2)) {
            // 内部插件
            Map<String, Object> inputs = debugPluginApiRequest.getInputs();
            List<PluginApiParam> requestParams = response == null ? toolInfo.getPluginApiInfo().getRequestParams() : response.getRequestParams();
            if (CollUtil.isNotEmpty(requestParams)) {
                for (PluginApiParam requestParam : requestParams) {
                    Object value = inputs.get(requestParam.getName());
                    if (value == null && (Objects.nonNull(requestParam.getUserValue()) || Objects.nonNull(requestParam.getDefaultValue()))) {
                        String userValue = requestParam.getUserValue();
                        inputs.put(requestParam.getName(), Objects.nonNull(userValue) ? userValue : requestParam.getDefaultValue());
                    }
                }
            }
            ExecutePluginApiResponse executePluginApiResponse = new ExecutePluginApiResponse();
            executePluginApiResponse.setRequestParam(JSON.toJSONString(debugPluginApiRequest.getInputs()));
            LocalDateTime executeInternalApiStartTime = LocalDateTime.now();
            LogStatusEnum executeInternalApiStatusEnum = LogStatusEnum.SUCCESS;
            try {
                Object result = executeInternalApi(toolInfo, inputs, debugPluginApiRequest);
                JSONObject content = new JSONObject();
                content.put("data", result);
                executePluginApiResponse.setResponseParam(content.toString());
                executePluginApiResponse.setSuccess(true);
            } catch (Exception e) {
                executeInternalApiStatusEnum = LogStatusEnum.FAILED;
                executePluginApiResponse.setSuccess(false);
                executePluginApiResponse.setErrorMsg("执行官方插件失败：" + e.getMessage());
            } finally {
                LogMessage logMessage = LogUtils.buildCommonLog(LogTypeEnum.PLUGIN_INVOKE,
                        debugPluginApiRequest.getInputs(), executePluginApiResponse, executeInternalApiStatusEnum,
                        executeInternalApiStartTime, LocalDateTime.now(), "");
                logMessage.setName("插件调用");
                logMqProducer.asyncSendLogMessage(logMessage);
                //内部插件发送统计埋点
                InvokeProcessMessage invokeProcessMessage = LogUtils.buildInvokeProcessMessage(BizTypeEnum.PLUGIN,
                        executeInternalApiStartTime, LocalDateTime.now());
                invokeProcessMessage.setBizName(toolInfo.getPluginApiInfo().getApiName() + "(" + debugPluginApiRequest.getApiId() + ")");
                invokeProcessMessage.setIsCommon(true);
                logMqProducer.asyncSendInvokeProcessMessage(invokeProcessMessage);
            }
            return executePluginApiResponse;
        }
        // 根据工具id查询插件id
        PluginApiDTO apiDTO = pluginApiRepository.getByApiId(debugPluginApiRequest.getApiId());
        if (apiDTO == null) {
            throw new BizException("AB002", "工具不存在");
        }
        String pluginId = apiDTO.getPluginId();
        // 获取插件编辑版本
        Long version;
        if (test) {
            version = pluginVersionInfoRepository.getEditVersion(pluginId);
        } else {
            version = pluginVersionInfoRepository.getPublishVersion(pluginId);
        }
        // 检查插件是否存在
        PluginMetaDTO pluginMetaDTO = pluginMetaRepository.queryByIdAndVersion(pluginId, version);
        if (pluginMetaDTO == null) {
            throw new BizException("AB001", "插件不存在");
        }
        // 先查询工具是否存在
        PluginApiDTO pluginApiDTO = pluginApiRepository.getByIdAndVersion(pluginId, debugPluginApiRequest.getApiId(), version);
        if (pluginApiDTO == null) {
            throw new BizException("AB002", "工具不存在");
        }
        ExecutePluginApiResponse debugPluginApiResponse = new ExecutePluginApiResponse();
        // 工具未启用
        if (Boolean.FALSE.equals(pluginApiDTO.getEnabled())) {
            debugPluginApiResponse.setSuccess(false);
            debugPluginApiResponse.setErrorMsg("工具未启用");
            debugPluginApiResponse.setResponseParam(JSON.toJSONString(debugPluginApiRequest.getInputs()));
            return debugPluginApiResponse;
        }
        PluginInfo pluginInfo = new PluginInfo();
        BeanUtils.copyProperties(pluginMetaDTO, pluginInfo);
        BeanUtils.copyProperties(pluginApiDTO, pluginInfo);
        if (StringUtils.isNotEmpty(pluginMetaDTO.getCommonHeaders())) {
            pluginInfo.setCommonHeaders(JSON.parseArray(pluginMetaDTO.getCommonHeaders(), CommonHeader.class));
        }
        if (StringUtils.isNotEmpty(pluginMetaDTO.getOauthInfo())) {
            pluginInfo.setOauthInfo(JSON.parseObject(pluginMetaDTO.getOauthInfo(), PluginOAuthInfo.class));
        }

        // 查询请求参数
        if (response == null) {
            List<PluginApiParam> requestParam = getApiParam(pluginApiDTO, ReqRspEnum.REQUEST.getCode(), version);
            pluginInfo.setRequestParams(requestParam);
            // 查询响应api参数
            List<PluginApiParam> responseParam = getApiParam(pluginApiDTO, ReqRspEnum.RESPONSE.getCode(), version);
            pluginInfo.setResponseParams(responseParam);
        } else {
            pluginInfo.setRequestParams(response.getRequestParams());
            pluginInfo.setResponseParams(response.getResponseParams());
        }

        pluginInfo.setUrl(pluginMetaDTO.getUrl() + pluginApiDTO.getPath());
        pluginInfo.setRedirectUrl(redirectUrl);

        ExecutePluginApiResponse executePluginApiResponse = new ExecutePluginApiResponse();
        // 校验白名单
        if (Boolean.TRUE.equals(whitelistSwitch)) {
            Set<String> ipWhitelist = Arrays.stream(whitelistIps.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toCollection(HashSet::new));
            if (CollectionUtils.isEmpty(ipWhitelist)) {
                executePluginApiResponse.setSuccess(false);
                executePluginApiResponse.setErrorMsg("ip不在白名单中");
                return executePluginApiResponse;
            }
            if (PluginServiceTypeEnum.MCP.getType().equals(pluginMetaDTO.getServiceType())) {
                if (!pluginMcpService.checkWhitelist(ipWhitelist, pluginMetaDTO)) {
                    executePluginApiResponse.setSuccess(false);
                    executePluginApiResponse.setErrorMsg("ip不在白名单中");
                    return executePluginApiResponse;
                }
            } else {
                if (!isAllowed(pluginInfo.getUrl(), ipWhitelist)) {
                    executePluginApiResponse.setSuccess(false);
                    executePluginApiResponse.setErrorMsg("ip不在白名单中");
                    return executePluginApiResponse;
                }
            }
        }
        LocalDateTime startTime = LocalDateTime.now();
        LogStatusEnum statusEnum = LogStatusEnum.SUCCESS;
        try {
            convertJson(debugPluginApiRequest, pluginInfo);
            // 判断是否为MCP插件，并进行处理
            if (PluginServiceTypeEnum.MCP.getType().equals(pluginMetaDTO.getServiceType())) {
                executePluginApiResponse = pluginMcpService.callTool(pluginMetaDTO, pluginInfo, debugPluginApiRequest.getInputs(), version);
            } else {
                executePluginApiResponse = PluginTunnelFacade.doInvoke(debugPluginApiRequest.getInputs(), pluginInfo);
            }
        } catch (Exception e) {
            statusEnum = LogStatusEnum.FAILED;
            log.error("调用插件发生异常！", e);
        } finally {
            //发送链路统计埋点
            LogMessage logMessage = LogUtils.buildCommonLog(LogTypeEnum.PLUGIN_INVOKE, debugPluginApiRequest.getInputs(), executePluginApiResponse, statusEnum, startTime, LocalDateTime.now(), "");
            logMessage.setName("插件调用");
            logMqProducer.asyncSendLogMessage(logMessage);
            //发送统计埋点
            InvokeProcessMessage invokeProcessMessage = LogUtils.buildInvokeProcessMessage(BizTypeEnum.PLUGIN, startTime, LocalDateTime.now());
            invokeProcessMessage.setBizName(pluginApiDTO.getApiName() + "(" + pluginApiDTO.getApiId() + ")");
            invokeProcessMessage.setIsCommon(toolInfo.getIsCommon());
            logMqProducer.asyncSendInvokeProcessMessage(invokeProcessMessage);
        }

        // 调试通过需要将调试状态修改为成功
        if (test) {
            if (executePluginApiResponse.isSuccess()) {
                pluginApiDTO.setDebugStatus(1);
                pluginApiRepository.updateById(pluginApiDTO);
            } else {
                pluginApiDTO.setDebugStatus(0);
                pluginApiRepository.updateById(pluginApiDTO);
            }

        }
        return executePluginApiResponse;
    }

    private void convertJson(ExecutePluginApiRequest debugPluginApiRequest, PluginInfo pluginInfo) {
        log.info("convertJson:{}", JSON.toJSONString(debugPluginApiRequest));
        Map<String, Object> inputs = debugPluginApiRequest.getInputs();
        List<PluginApiParam> requestParams = pluginInfo.getRequestParams();
        Map<String, PluginApiParam> paramDefines = new HashMap<>();
        requestParams.forEach(param->{
            paramDefines.put(param.getName(), param);
        });

        // 根据工具参数定义，进行数据类型转换:
        inputs.keySet().forEach(key -> {
            Object value = inputs.get(key);
            PluginApiParam paramDef = paramDefines.get(key);
            if (Objects.nonNull(paramDef)) {
                int expectTypeCode = paramDef.getParamType();
                if (value instanceof String) {
                    try {
                        //1-string,2-integer,3-number,4-boolean,5-object,6-array
                        switch (expectTypeCode) {
                            case 1:
                                break;
                            case 2:
                                inputs.put(key, Integer.valueOf(value.toString()));
                                break;
                            case 3:
                                inputs.put(key, Double.valueOf(value.toString()));
                                break;
                            case 4:
                                inputs.put(key, Boolean.valueOf(value.toString()));
                                break;
                            case 6:
                            case 7:
                            case 8:
                            case 9:
                            case 10:
                                inputs.put(key, JSONObject.parseObject(value.toString(), List.class));
                                break;
                            default:
                                inputs.put(key, JSONObject.parseObject(value.toString()));
                        }
                    } catch (Exception e) {
                        log.info("value:{}", JSON.toJSONString(value));
                        log.error("json parse error", e);
                    }
                }
            } else {
                log.info("Unexpected param:{}", key);
            }
        });
    }

    private String extractHost(String fullUrl) {
        try {
            URI uri = new URI(fullUrl);
            return uri.getHost();
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("Invalid URL: " + fullUrl, e);
        }
    }

    private boolean isAllowed(String fullUrl, Set<String> whitelist) {
        String host = extractHost(fullUrl);
        if (host == null || host.isEmpty()) {
            return false;
        }

        for (String allowed : whitelist) {
            if (Objects.equals(host, allowed)) {
                return true;
            }
        }
        return false;
    }

    private PluginInfoResponse queryPluginApiInfoByEnv(String apiId, boolean test) {
        PluginApiDTO apiDTO = pluginApiRepository.getByApiId(apiId);
        if (Objects.isNull(apiDTO)) {
            return null;
        }
        String pluginId = apiDTO.getPluginId();
        PluginInfoResponse pluginInfoResponse = new PluginInfoResponse();
        // 根据pluginId和apiId查询对应的agent配置信息, 区分环境
        Long version;
        if (test) {
            version = pluginVersionInfoRepository.getEditVersion(pluginId);
        } else {
            version = pluginVersionInfoRepository.getPublishVersion(pluginId);
        }

        PluginMetaDTO pluginMetaDTO = pluginMetaRepository.queryByIdAndVersion(pluginId, version);
        if (pluginMetaDTO == null) {
            return null;
        }
        BeanUtils.copyProperties(pluginMetaDTO, pluginInfoResponse);
        // 查询api和参数
        PluginApiDTO pluginApiDTO = pluginApiRepository.queryApiByApiId(pluginId, apiId, version);
        if (pluginApiDTO != null) {
            PluginApiInfo pluginApiDetail = getPluginApiDetail(pluginId, apiId, !test);
            if (pluginApiDetail != null) {
                pluginApiDetail.setUrl(pluginMetaDTO.getUrl());
            }
            pluginInfoResponse.setPluginApiInfo(pluginApiDetail);
        }
        pluginInfoResponse.setIsCommon(pluginMallInfoService.queryCommonPlugin(pluginId));
        return pluginInfoResponse;
    }

    @Override
    public Object executeInternalApi(ExecuteInternalPluginApiRequest request) {
        PluginInfoResponse toolInfo = queryPluginApiInfo(request.getApiId());
        if (Objects.isNull(toolInfo)) {
            throw new BizException("AB002", "工具不存在");
        }
        Map<String, Object> inputs = request.getInputs();
        ExecutePluginApiRequest executePluginApiRequest = new ExecutePluginApiRequest();
        BeanUtils.copyProperties(request, executePluginApiRequest);
        return executeInternalApi(toolInfo, inputs, executePluginApiRequest);
    }


    private Object executeInternalApi(PluginInfoResponse toolInfo, Map<String, Object> inputs, ExecutePluginApiRequest pluginApiRequest) {
        try {
            if (toolInfo.getCreateApiType().equals(2)) {
                // 官方插件内部调用
                PluginApiInfo pluginApiInfo = toolInfo.getPluginApiInfo();
                InternalPluginNameEnum apiName = InternalPluginNameEnum.getEnumByCode(pluginApiInfo.getApiName());
                InternalPluginExecutor pluginExecutor = internalPluginHolder.getPluginExecutor(apiName);

                InternalPluginRequest pluginRequest = new InternalPluginRequest();
                pluginRequest.setPluginType(apiName);
                pluginRequest.setRequestParam(inputs);
                pluginRequest.setPluginApiInfo(pluginApiInfo);
                pluginRequest.setSessionId(pluginApiRequest.getSessionId());
                pluginRequest.setMessageCallback(pluginApiRequest.getMessageCallback());
                pluginRequest.setMessageId(pluginApiRequest.getMessageId());
                pluginRequest.setDownMessageId(pluginApiRequest.getDownMessageId());
                return pluginExecutor.execute(pluginRequest);
            }
        } catch (Exception e) {
            log.error("插件调用异常", e);
            throw new BizException("AB012", "插件调用异常:" + e.getMessage());
        }
        return null;
    }

    @Override
    public List<PluginApiInfo> queryOnlineApiByPluginId(String pluginId) {
        Long publishVersion = pluginVersionInfoRepository.getPublishVersion(pluginId);
        // 检查插件是否存在
        PluginMetaDTO pluginMetaDTO = pluginMetaRepository.queryByIdAndVersion(pluginId, publishVersion);
        if (pluginMetaDTO == null) {
            throw new BizException("AB001", "插件不存在");
        }
        LambdaQueryWrapper<PluginApiPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PluginApiPO::getPluginId, pluginId);
        //上线状态为1
        queryWrapper.eq(PluginApiPO::getServiceStatus, 1);
        queryWrapper.eq(PluginApiPO::getVersion, publishVersion);
        List<PluginApiPO> list = pluginApiServiceImpl.list(queryWrapper);
        if (list == null || list.isEmpty()) {
            return Lists.newArrayList();
        }
        List<PluginApiDTO> pluginApiDTOS = list.stream().map(PluginApiConvertor.INSTANCE::convert).collect(Collectors.toList());

        List<PluginApiInfo> pluginApiInfos = Lists.newArrayList();
        pluginApiDTOS.forEach(pluginApiDTO -> {
            PluginApiInfo pluginApiInfo = new PluginApiInfo();
            BeanUtils.copyProperties(pluginApiDTO, pluginApiInfo);
            pluginApiInfo.setPluginName(pluginMetaDTO.getPluginName());
            // 查询请求api参数
            List<PluginApiParam> requestParam = getApiParam(pluginApiDTO, ReqRspEnum.REQUEST.getCode(), publishVersion);
            pluginApiInfo.setRequestParams(requestParam);
            // 查询响应api参数
            if (PluginServiceTypeEnum.MCP.getType().equals(pluginMetaDTO.getServiceType())) {
                //如果是MCP插件，返回默认Object result对象
                List<PluginApiParam> responseParam = new ArrayList<>();
                PluginApiParam result = new PluginApiParam();
                result.setName("result");
                result.setParamDesc("result");
                result.setParamType(5); //object
                result.setEnabled(true);

                responseParam.add(result);
                pluginApiInfo.setResponseParams(responseParam);
                pluginApiInfos.add(pluginApiInfo);
            } else {
                List<PluginApiParam> responseParam = getApiParam(pluginApiDTO, ReqRspEnum.RESPONSE.getCode(), publishVersion);
                pluginApiInfo.setResponseParams(responseParam);
                pluginApiInfos.add(pluginApiInfo);
            }
        });
        return pluginApiInfos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPluginApiByRpc(PluginApiRpcRequest request) {
        pluginMcpService.checkMcpApi(request.getPluginId());
        RequestInfo requestInfo = RequestContext.get();
        dataResourceAccess.getResourceData(request.getPluginId(), requestInfo.getTenantId(), PLUGIN_TYPE, requestInfo.getUserId());
        RequestInfo userInfo = RequestContext.get();
        // 获取插件编辑版本
        Long editVersion = pluginVersionInfoRepository.getEditVersion(request.getPluginId());
        // 检查插件是否存在
        PluginMetaDTO pluginMetaDTO = pluginMetaRepository.queryByIdAndVersion(request.getPluginId(), editVersion);
        if (pluginMetaDTO == null) {
            throw new BizException("AB001", "插件不存在");
        }

        checkName(request, userInfo, editVersion);
        String apiId = IdGenerator.getId(null);

        PluginApiDTO pluginApiDTO = convertor.convert(request);
        pluginApiDTO.setApiId(apiId);
        pluginApiDTO.setPluginId(pluginMetaDTO.getPluginId());
        pluginApiDTO.setVersion(editVersion);
        pluginApiDTO.setServiceStatus(0);
        pluginApiDTO.setEnabled(true);
        // 默认为调试通过
        pluginApiDTO.setDebugStatus(1);
        pluginApiDTO.setYn(0L);
        pluginApiDTO.setCreateId(userInfo.getUserId());
        pluginApiDTO.setCreateName(userInfo.getUserName());
        pluginApiDTO.setCreateTime(LocalDateTime.now());
        pluginApiDTO.setUpdateId(userInfo.getUserId());
        pluginApiDTO.setUpdateName(userInfo.getUserName());
        pluginApiDTO.setUpdateTime(LocalDateTime.now());
        pluginApiDTO.setAppCode(userInfo.getAppCode());
        pluginApiRepository.save(pluginApiDTO);

        // 保存请求参数
        saveParam(request.getRequestParams(), apiId, request.getPluginId(), editVersion, 1);

        // 保存响应参数
        saveParam(request.getResponseParams(), apiId, request.getPluginId(), editVersion, 2);

        // 发布插件
        pluginMetaConfigService.publishPlugin(request.getPluginId());

        return apiId;
    }

    @Override
    public Map<String, Object> recallPluginApi(List<ExecutePluginApiRequest> apiRequests, boolean test) {
        Map<String, Object> recallResult = new HashMap<>();
        for (ExecutePluginApiRequest apiRequest : apiRequests) {
            Map<String, Object> inputs = apiRequest.getInputs();
            List<PluginApiParam> requestParams = apiRequest.getRequestParams();
            if (CollUtil.isNotEmpty(requestParams) && CollUtil.isNotEmpty(inputs)) {
                for (PluginApiParam requestParam : requestParams) {
                    Object value = inputs.get(requestParam.getName());
                    if (value == null && (Objects.nonNull(requestParam.getUserValue()) || Objects.nonNull(requestParam.getDefaultValue()))) {
                        String userValue = requestParam.getUserValue();
                        inputs.put(requestParam.getName(), Objects.nonNull(userValue) ? userValue : requestParam.getDefaultValue());
                    }
                }
            }
            ExecutePluginApiResponse executePluginApiResponse = new ExecutePluginApiResponse();
            executePluginApiResponse.setRequestParam(JSON.toJSONString(apiRequest.getInputs()));
            try {
                Object result = recallInternalApi(apiRequest.getApiName(), inputs, apiRequest);
                if (Objects.nonNull(result)) {
                    recallResult.put(apiRequest.getApiId(), result);
                }
            } catch (Exception e) {
                log.error("召回api {} 异常", apiRequest.getApiId(), e);
            }
        }
        return recallResult;
    }


    private Object recallInternalApi(String apiName, Map<String, Object> inputs, ExecutePluginApiRequest pluginApiRequest) {
        try {
            // 官方插件内部调用
            InternalPluginNameEnum apiNameEnmu = InternalPluginNameEnum.getEnumByCode(apiName);
            InternalPluginExecutor pluginExecutor = internalPluginHolder.getPluginExecutor(apiNameEnmu);
            InternalPluginRequest pluginRequest = new InternalPluginRequest();
            pluginRequest.setPluginType(apiNameEnmu);
            pluginRequest.setRequestParam(inputs);
//            pluginRequest.setPluginApiInfo(pluginApiInfo);
            pluginRequest.setSessionId(pluginApiRequest.getSessionId());
            pluginRequest.setMessageCallback(pluginApiRequest.getMessageCallback());
            pluginRequest.setMessageId(pluginApiRequest.getMessageId());
            pluginRequest.setDownMessageId(pluginApiRequest.getDownMessageId());
            return pluginExecutor.recall(pluginRequest);
        } catch (Exception e) {
            log.error("插件召回调用异常", e);
            return null;
        }
    }


    private void saveParam(List<PluginApiParam> params, String apiId, String pluginId, Long editVersion, int reqRspType) {
        List<PluginApiParamDTO> requestList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(params)) {
            params.forEach(res -> {
                PluginApiParamDTO pluginApiParamPO = new PluginApiParamDTO();
                BeanUtils.copyProperties(res, pluginApiParamPO);
                pluginApiParamPO.setParamId(IdGenerator.getId(null));
                pluginApiParamPO.setApiId(apiId);
                pluginApiParamPO.setPluginId(pluginId);
                pluginApiParamPO.setReqRspType(reqRspType);
                if (reqRspType == 2) {
                    pluginApiParamPO.setLocation(4);
                }
                pluginApiParamPO.setVersion(editVersion);
                // 新增子参数
                List<PluginApiParamDTO> subParams = recursionSubParam(res.getSubParameters(), apiId, pluginId, reqRspType, pluginApiParamPO.getParamId(), editVersion, pluginApiParamPO.getLocation());
                requestList.add(pluginApiParamPO);
                requestList.addAll(subParams);
            });
        }
        if (CollectionUtils.isNotEmpty(requestList)) {
            pluginApiParamRepository.saveBatch(requestList);
        }
    }

    /**
     * 获取api参数
     *
     * @param pluginApiDTO 工具实体
     * @param paramType    参数类型
     * @param version  版本
     * @return 参数集合
     */
    private List<PluginApiParam> getApiParam(PluginApiDTO pluginApiDTO, int paramType, Long version) {
        List<PluginApiParamDTO> pluginApiRequestList = pluginApiParamRepository.listByApiId(pluginApiDTO.getPluginId(), pluginApiDTO.getApiId(), paramType, version);
        // 递归获取根参数
        List<PluginApiParamDTO> rootParams = pluginApiRequestList.stream().filter(param -> Objects.isNull(param.getFatherParamId())).collect(Collectors.toList());
        List<PluginApiParam> rootParamList = Lists.newArrayList();
        rootParams.forEach(param -> {
            PluginApiParam rootParam = new PluginApiParam();
            BeanUtils.copyProperties(param, rootParam);
            // 递归获取子参数
            List<PluginApiParam> subParams = recursionSubParam(pluginApiRequestList, param.getParamId());
            rootParam.setSubParameters(subParams);
            rootParamList.add(rootParam);
        });
        return rootParamList;
    }

    private List<PluginApiParam> recursionSubParam(List<PluginApiParamDTO> paramPOList, String parentParamId) {
        List<PluginApiParamDTO> subParamList = paramPOList.stream().filter(param -> Objects.equals(param.getFatherParamId(), parentParamId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subParamList)) {
            return Lists.newArrayList();
        }
        List<PluginApiParam> list = Lists.newArrayList();
        subParamList.parallelStream().forEach(param -> {
            PluginApiParam subParam = new PluginApiParam();
            BeanUtils.copyProperties(param, subParam);
            String paramId = param.getParamId();
            List<PluginApiParam> subParams = recursionSubParam(paramPOList, paramId);
            subParam.setSubParameters(subParams);
            list.add(subParam);
        });
        return list;
    }

    private void updatePluginStatus(PluginMetaDTO pluginMetaDTO, RequestInfo userInfo) {
        // 检查插件状态, 如果是在已经发布的插件查新建工具, 则需要将插件状态变为更新待发布
        Integer status = Objects.equals(pluginMetaDTO.getStatus(), PluginStatusEnum.PUBLISHED.getCode()) ? PluginStatusEnum.MODIFY.getCode() : pluginMetaDTO.getStatus();
        pluginMetaDTO.setStatus(status);
        pluginMetaDTO.setUpdateId(userInfo.getUserId());
        pluginMetaDTO.setUpdateName(userInfo.getUserName());
        pluginMetaDTO.setUpdateTime(LocalDateTime.now());
        pluginMetaRepository.updateById(pluginMetaDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePluginApi(UpdatePluginApiRequest request) {
        PlatformUser user = SsoUtil.get();
        dataResourceAccess.getResourceData(request.getPluginId(), user.getCorpCode(), PLUGIN_TYPE, user.getUserId());
        RequestInfo userInfo = RequestContext.get();
        // 获取插件编辑版本
        Long editVersion = pluginVersionInfoRepository.getEditVersion(request.getPluginId());
        // 检查插件是否存在
        PluginMetaDTO pluginMetaDTO = pluginMetaRepository.queryByIdAndVersion(request.getPluginId(), editVersion);
        if (pluginMetaDTO == null) {
            throw new BizException("AB001", "插件不存在");
        }

        // 检查工具是否存在
        PluginApiDTO pluginApiDTO = pluginApiRepository.getByIdAndVersion(request.getPluginId(), request.getApiId(), editVersion);
        if (pluginApiDTO == null) {
            throw new BizException("AB002", "工具不存在");
        }

        if (request.getUpdateApiType() == null) {
            throw new BizException("AB004", "更新插件类型不能为空");
        }
        if (PluginServiceTypeEnum.MCP.getType().equals(pluginMetaDTO.getServiceType())) {
            pluginMcpService.apiOnOrOff(request, pluginApiDTO);
            updatePluginStatus(pluginMetaDTO, userInfo);
            return Boolean.TRUE;
        }
        // 更新后清除调试状态
        pluginApiDTO.setDebugStatus(0);
        switch (request.getUpdateApiType()) {
            case 1:
                updatePluginApiBaseInfo(request, pluginApiDTO, userInfo, editVersion);
                break;
            case 2:
                updateApiRequestParam(request, editVersion);
                break;
            case 3:
                updateApiResponseParam(request, editVersion);
                break;
            case 4:
                updatePluginApiEnabledStatus(request, pluginApiDTO, userInfo);
                break;
            default:
                throw new BizException("AB005", "更新插件类型不正确");
        }
        // 检查插件状态, 如果是在已经发布的插件查新建工具, 则需要将插件状态变为更新待发布
        updatePluginStatus(pluginMetaDTO, userInfo);
        return true;
    }

    private void updatePluginApiEnabledStatus(UpdatePluginApiRequest request, PluginApiDTO pluginApiDTO, RequestInfo userInfo) {
        if (Objects.isNull(request.getEnabled())) {
            throw new BizException("AB006", "是否启用不能为空");
        }
        pluginApiDTO.setEnabled(request.getEnabled());
        pluginApiDTO.setUpdateId(userInfo.getUserId());
        pluginApiDTO.setUpdateName(userInfo.getUserName());
        pluginApiDTO.setUpdateTime(LocalDateTime.now());
        pluginApiRepository.updateById(pluginApiDTO);
    }

    /**
     * 删除原来的数据, 基于请求体重新插入
     *
     * @param request     更新插件api请求体
     * @param editVersion 版本
     */
    private void updateApiResponseParam(UpdatePluginApiRequest request, Long editVersion) {
        String pluginId = request.getPluginId();
        String apiId = request.getApiId();
        // 先删除原来的响应体,后插入新的响应体, 参数类型请求体为1, 响应体类型为2.
        pluginApiParamRepository.deleteOldData(apiId, pluginId, 2, editVersion);
        saveParam(request.getResponseParams(), apiId, pluginId, editVersion, 2);
    }

    /**
     * 递归的获取子参数的数据
     *
     * @param subParameters 子参数集合
     * @param apiId         工具id
     * @param pluginId      插件id
     * @param paramType     参数类型
     * @param parentParamId 父参数id
     * @param editVersion   版本
     * @param location      参数所在位置
     * @return
     */
    private List<PluginApiParamDTO> recursionSubParam(List<PluginApiParam> subParameters, String apiId, String pluginId, int paramType, String parentParamId, Long editVersion, int location) {
        if (CollectionUtils.isEmpty(subParameters)) {
            return Lists.newArrayList();
        }
        List<PluginApiParamDTO> subParamsList = Lists.newArrayList();
        subParameters.forEach(subParam -> {
            PluginApiParamDTO pluginApiParamDTO = new PluginApiParamDTO();
            BeanUtils.copyProperties(subParam, pluginApiParamDTO);
            pluginApiParamDTO.setParamId(IdGenerator.getId(null));
            pluginApiParamDTO.setPluginId(pluginId);
            pluginApiParamDTO.setReqRspType(paramType);
            pluginApiParamDTO.setApiId(apiId);
            pluginApiParamDTO.setFatherParamId(parentParamId);
            pluginApiParamDTO.setLocation(location);
            pluginApiParamDTO.setVersion(editVersion);
            // 递归子参数
            List<PluginApiParamDTO> subParams = recursionSubParam(subParam.getSubParameters(), apiId, pluginId, paramType, pluginApiParamDTO.getParamId(), editVersion, pluginApiParamDTO.getLocation());
            subParamsList.add(pluginApiParamDTO);
            subParamsList.addAll(subParams);
        });
        return subParamsList;
    }

    private void updateApiRequestParam(UpdatePluginApiRequest request, Long editVersion) {
        String pluginId = request.getPluginId();
        String apiId = request.getApiId();
        // 先删除原来的响应体,后插入新的响应体, 参数类型请求体为1, 响应体类型为2.
        pluginApiParamRepository.deleteOldData(apiId, pluginId, 1, editVersion);
        saveParam(request.getRequestParams(), apiId, pluginId, editVersion, 1);
    }

    private void updatePluginApiBaseInfo(UpdatePluginApiRequest request, PluginApiDTO pluginApiDTO, RequestInfo userInfo, Long editVersion) {
        BizCheck.checkPluginApiRequest(request);
        checkName(request, editVersion);
        pluginApiDTO.setApiName(request.getApiName());
        pluginApiDTO.setApiDesc(request.getApiDesc());
        pluginApiDTO.setPath(request.getPath());
        pluginApiDTO.setMethod(request.getMethod());
        pluginApiDTO.setUpdateId(userInfo.getUserId());
        pluginApiDTO.setUpdateName(userInfo.getUserName());
        pluginApiDTO.setUpdateTime(LocalDateTime.now());
        pluginApiRepository.updateById(pluginApiDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePluginApi(String pluginId, String apiId) {
        pluginMcpService.checkMcpApi(pluginId);
        PlatformUser user = SsoUtil.get();
        dataResourceAccess.getResourceData(pluginId, user.getCorpCode(), PLUGIN_TYPE, user.getUserId());
        // 查询是否存在引用
        int count = 0;
        Result<Integer> botRefNum = agentInfoRpcApi.queryBotRefNumByApiId(apiId);
        if (botRefNum != null && botRefNum.getData() != null) {
            count = botRefNum.getData();
        }
        if (count > 0) {
            throw new BizException("AB038", "该工具被引用，无法删除");
        }
        // 只能删除编辑态的工具
        Long editVersion = pluginVersionInfoRepository.getEditVersion(pluginId);
        // 删除api参数
        pluginApiParamRepository.removeByApiId(apiId, editVersion);

        boolean result = pluginApiRepository.removeById(apiId);
        if (!result) {
            throw new BizException("AB007", "删除工具失败");
        }
        return true;
    }

    @Override
    public boolean batchDeletePluginApi(List<String> apiIds, String pluginId) {
        pluginMcpService.checkMcpApi(pluginId);
        PlatformUser user = SsoUtil.get();
        dataResourceAccess.getResourceData(pluginId, user.getCorpCode(), PLUGIN_TYPE, user.getUserId());
        boolean result = pluginApiRepository.removeByIds(apiIds);
        if (!result) {
            throw new BizException("AB007", "删除工具失败");
        }
        return true;
    }

    private void checkName(PluginApiRequest request, Long editVersion) {
        if (StringUtils.isEmpty(request.getApiName())) {
            throw new BizException("AB008", "工具名称不能为空");
        }
        if (request.getApiName().length() > 30) {
            throw new BizException("AB009", "工具名称最长不能超过30个字符");
        }
        PluginApiDTO pluginApiDTOS = pluginApiRepository.queryApiByName(request.getApiName(), request.getPluginId(), editVersion);
        // update的时候, 如果工具名称不变,不需要校验,如果工具名称改变了, 需要校验工具名称是否存在
        if (request instanceof UpdatePluginApiRequest) {
            UpdatePluginApiRequest updateRequest = (UpdatePluginApiRequest) request;
            if (pluginApiDTOS != null && !StringUtils.equals(updateRequest.getApiId(), pluginApiDTOS.getApiId())) {
                throw new BizException("AB010", "工具名称已经存在, 请更换其他名称");
            }
        } else {
            if (pluginApiDTOS != null) {
                throw new BizException("AB010", "工具名称已经存在, 请更换其他名称");
            }
        }
    }

    private void checkName(PluginApiRpcRequest request, RequestInfo userInfo, Long editVersion) {
        if (StringUtils.isEmpty(request.getApiName())) {
            throw new BizException("AB008", "工具名称不能为空");
        }
        if (request.getApiName().length() > 30) {
            throw new BizException("AB009", "工具名称最长不能超过30个字符");
        }
        PluginApiDTO pluginApiDTOS = pluginApiRepository.queryApiByName(request.getApiName(), request.getPluginId(), editVersion);
        if (pluginApiDTOS != null) {
            throw new BizException("AB010", "工具名称已经存在, 请更换其他名称");
        }
    }
}
