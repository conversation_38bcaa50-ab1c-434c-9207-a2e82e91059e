package com.chinatelecom.gs.engine.kms.sdk.vo.dify;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * Dify搜索请求
 * 支持下划线和驼峰命名的兼容性
 */
@Data
public class DifySearchRequest {

    /**
     * 查询内容
     */
    @NotNull
    @JsonProperty("query")
    @JsonAlias({"query"})
    private String query;

    /**
     * 检索设置
     * 支持输入：retrieval_setting, retrievalSetting
     * 输出：retrieval_setting
     */
    @JsonProperty("retrieval_setting")
    @JsonAlias({"retrievalSetting", "retrieval_setting"})
    private RetrievalSetting retrievalSetting = new RetrievalSetting();

    /**
     * 知识库ID
     * 支持输入：knowledge_id, knowledgeId
     * 输出：knowledge_id
     */
    @NotNull
    @JsonProperty("knowledge_id")
    @JsonAlias({"knowledgeId", "knowledge_id"})
    private String knowledgeId;

    /**
     * 元数据筛选条件，可选
     * 支持输入：metadata_condition, metadataCondition
     * 输出：metadata_condition
     */
    @JsonProperty("metadata_condition")
    @JsonAlias({"metadataCondition", "metadata_condition"})
    private MetadataCondition metadataCondition;
}