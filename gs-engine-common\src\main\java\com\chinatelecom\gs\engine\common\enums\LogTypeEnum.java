package com.chinatelecom.gs.engine.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * @USER: pengmc1
 * @DATE: 2025/5/7 15:35
 */

@Getter
public enum LogTypeEnum {
    WORKFLOW_INVOKE("workflow-invoke", "工作流调用"),
    NODE_INVOKE("node-invoke", "节点调用"),
    AGENT_CONFIG("agent-config", "Agent配置"),
    QUERY_REWRITE("query-rewrite", "Query改写"),
    SUMMARY_INTENT("summary-intent", "总结意图识别"),
    TOOL_CHOOSE("tool-choose", "工具选择"),
    ES_SEARCH("es-search", "ES检索"),
    RERANK("rerank", "精排"),
    LLM_INVOKE("llm-invoke", "大模型调用"),
    ENTITY_REG("entity-reg", "实体识别"),
    PLUGIN_INVOKE("plugin-invoke", "插件调用"),
    SUPER_SAFE_CHECK("super-safe-check", "超级安全围栏"),
    SAFE_CHECK("safe-check", "安全围栏"),
    QUESTION_RECOMMEND("question-recommend", "问题推荐"),
    CHAT_ANSWER("chat-answer", "聊天答案"),
    INTENT_REG("intent-reg", "意图识别"),

    TOOL_ENGINE("tool-engine", "工具引擎"),
    KENFOU_REG("kenfou-reg", "肯否识别"),
    COMMON_ENTITY_REG("common-entity-reg", "通用实体识别"),
    VAD_TIMEOUT("vad-timeout", "静音超时"),
    KNOWLEDGE_ANSWER("knowledge-answer", "知识库直出"),
    KS_CHUNK_RECALL("ks-chunk-recall", "知识库分片召回"),
    ;

    @JsonValue
    @EnumValue
    private String code;
    private String desc;

    LogTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LogTypeEnum getByCode(String code) {
        if (code == null || code.isEmpty()) {
            return null;
        }
        for (LogTypeEnum entity : LogTypeEnum.values()) {
            if (entity.getCode().equals(code )) {
                return entity;
            }
        }
        return null;
    }
}
