package com.chinatelecom.gs.plugin.hub.infra.handler.executor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.enums.InternalPluginNameEnum;
import com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.kms.dto.EmbeddingInfo;
import com.chinatelecom.gs.engine.kms.flow.parse.component.context.KnowledgeParseConfigContext;
import com.chinatelecom.gs.engine.kms.rpc.split.DocSplitRpc;
import com.chinatelecom.gs.engine.kms.rpc.split.dto.SplitRequest;
import com.chinatelecom.gs.engine.kms.rpc.split.dto.SplitResponse;
import com.chinatelecom.gs.engine.kms.rpc.split.dto.SplitResult;
import com.chinatelecom.gs.engine.kms.sdk.api.SessionFileSearchApi;
import com.chinatelecom.gs.engine.kms.sdk.enums.KnowledgeType;
import com.chinatelecom.gs.engine.kms.sdk.enums.SplitIndexType;
import com.chinatelecom.gs.engine.kms.sdk.vo.file.SessionFileQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.file.SessionFileVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeParseConfig;
import com.chinatelecom.gs.engine.kms.service.ModelAppService;
import com.chinatelecom.gs.plugin.hub.infra.handler.InternalPluginExecutor;
import com.chinatelecom.gs.plugin.hub.infra.handler.InternalPluginRequest;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 音频文件解析
 */
@Slf4j
@Component
public class AudioAnalysisPluginExecutor extends InternalPluginExecutor {
    @Resource
    private SessionFileSearchApi sessionFileSearchApi;

    @Resource
    private DocSplitRpc docSplitRpc;

    @Resource
    private ModelAppService modelAppService;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Override
    public InternalPluginNameEnum pluginType() {
        return InternalPluginNameEnum.AUDIO_ANALYSIS;
    }

    @Override
    protected Object executePlugin(InternalPluginRequest pluginRequest) {
        Map<String, Object> requestParam = pluginRequest.getRequestParam();
        Object fileCodeObj = requestParam.get("fileCodes");
        List<String> fileCodes = JSON.parseArray(JSON.toJSONString(fileCodeObj), String.class);
        if (CollUtil.isEmpty(fileCodes)) {
            return CharSequenceUtil.EMPTY;
        }
        List<AnalysisDTO> analysisList = new ArrayList<>();
        List<SessionFileVO> files = getFiles(pluginRequest.getSessionId(), fileCodes);
        RequestInfo user = RequestContext.get();
        log.info("开始解析文件，文件数量：{},user:{}", files.size(), user);
        List<CompletableFuture<AnalysisDTO>> futures = files.stream()
                .map(file -> CompletableFuture.supplyAsync(() -> {
                    try {
                        RequestContext.set(user);
                        log.info("开始解析文件：{}", file.getFileName());
                        StringBuilder content = parseFile(file);
                        log.info("文件：{} 解析完成", file.getFileName());
                        return AnalysisDTO.builder()
                                .content(Objects.isNull(content) ? "" : content.toString())
                                .fileCode(file.getFileCode())
                                .fileName(file.getFileName())
                                .build();
                    } finally {
                        RequestContext.remove();
                    }
                }))
                .collect(Collectors.toList());

        try {
            analysisList = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futures.stream()
                            .map(CompletableFuture::join)
                            .collect(Collectors.toList()))
                    .get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("音频解析异常{},", e.getMessage(), e);
        }
        return analysisList;
    }

    /**
     * 解析文件
     *
     * @param file SessionFileVO
     * @return StringBuilder
     */
    private StringBuilder parseFile(SessionFileVO file) {
        SplitRequest request = new SplitRequest();
        request.setName(file.getFileName());
        KnowledgeType knowledgeType = KnowledgeType.fromFileName(file.getFileName());
        request.setKnowledgeType(knowledgeType);
        request.setIndexType(SplitIndexType.TEXT);
        request.setOriginalFileKey(file.getFileOssUrl());
        request.setKnowledgeParseConfig(getDefaultContext());
        log.info("音频文件解析，请求参数：{}", JSON.toJSONString(request));
        SplitResponse splitResponse = docSplitRpc.split(request);
        log.info("音频文件解析，返回结果：{}", splitResponse);
        if (Objects.isNull(splitResponse) || CollUtil.isEmpty(splitResponse.getSplitResults())) {
            return null;
        }
        List<SplitResult> splitResults = splitResponse.getSplitResults();
        StringBuilder content = new StringBuilder();
        for (int i = 0; i < splitResults.size(); i++) {
            content.append(splitResults.get(i).getContent());
        }
        return content;
    }

    /**
     * 获取默认解析参数
     *
     * @return KnowledgeParseConfigContext
     */
    private KnowledgeParseConfigContext getDefaultContext() {
        KnowledgeParseConfigContext context = new KnowledgeParseConfigContext();
        log.info("默认解析配置：{}", gsGlobalConfig.getKmsBaseConfig().getDefaultConfig());
        KnowledgeParseConfig parseConfig = com.alibaba.fastjson2.JSON.parseObject(gsGlobalConfig.getKmsBaseConfig().getDefaultConfig(), KnowledgeParseConfig.class);
        if (parseConfig == null) {
            parseConfig = new KnowledgeParseConfig();
            parseConfig.setModelUrlList(new KnowledgeParseConfig.ModelUrlList());
            parseConfig.setChunkConfig(new KnowledgeParseConfig.ChunkConfig());
            parseConfig.setParserConfig(new KnowledgeParseConfig.ParserConfig());
        }
        log.info("解析配置1：{}", JSON.toJSONString(parseConfig));
        //设置cpu解析为false
        parseConfig.getParserConfig().setCpuFlag(Boolean.FALSE);
        parseConfig.getChunkConfig().setSemanticChunkFlag(Boolean.FALSE);
        context.setKnowledgeBaseConfig(parseConfig);
        context.setReExecute(false);
        log.info("解析配置2：{}", JSON.toJSONString(parseConfig));
        Map<ModelTypeEnum, ModelPageListParam> modelMap = modelAppService.getDefaultModel();
        log.info("默认模型：{}", JSON.toJSONString(modelMap));
        for (Map.Entry<ModelTypeEnum, ModelPageListParam> entry : modelMap.entrySet()) {
            ModelPageListParam modelPageListParam = entry.getValue();
            if (modelPageListParam != null) {
                setModelUrl(entry.getKey(), parseConfig.getModelUrlList(), modelPageListParam);
            }
        }
        log.info("解析配置3：{}", JSON.toJSONString(parseConfig));
        EmbeddingInfo embeddingInfo = modelAppService.queryDefaultEmbedding(null);
        log.info("模型信息：{}", JSON.toJSONString(embeddingInfo));
        if (Objects.nonNull(embeddingInfo)) {
            log.info("设置模型URL：{}", embeddingInfo.getModelCode());
            ModelPageListParam modelPageListParam = modelAppService.queryByModelCode(embeddingInfo.getModelCode());
            setModelUrl(ModelTypeEnum.EMBEDDING, parseConfig.getModelUrlList(), modelPageListParam);
        }
        log.info("解析配置：{}", JSON.toJSONString(parseConfig));
        return context;
    }

    private void setModelUrl(ModelTypeEnum modelTypeEnum, KnowledgeParseConfig.ModelUrlList modelUrlList, ModelPageListParam modelPageListParam) {
        switch (modelTypeEnum) {
            case OFFLINE_LLM:
                modelUrlList.setLlmUrl(modelPageListParam.getExternalModelUrl());
                break;
            case ASR:
                modelUrlList.setAsrUrl(modelPageListParam.getExternalModelUrl());
                break;
            case OCR:
                modelUrlList.setOcrUrl(modelPageListParam.getExternalModelUrl());
                break;
            case LAYOUT:
                modelUrlList.setLayoutUrl(modelPageListParam.getExternalModelUrl());
                break;
            case VLLM:
                modelUrlList.setVllmModelUrl(modelPageListParam.getExternalModelUrl());
                break;
            case RERANK:
            case ENTITY:
            case SETR:
            case CLASSIFIER:
            case EMBEDDING:
            default:
        }
    }

    /**
     * 获取文件元数据
     *
     * @param sessionId 会话ID
     * @param fileCodes 文件编码列表
     * @return List<SessionFileVO>
     */
    private List<SessionFileVO> getFiles(String sessionId, List<String> fileCodes) {
        SessionFileQueryParam sessionFileQueryParam = new SessionFileQueryParam();
        sessionFileQueryParam.setSessionId(sessionId);
        sessionFileQueryParam.setFileCodes(fileCodes);
        sessionFileQueryParam.setViewContent(true);
        try {
            log.info("【文件应答】获取文件元数据，请求参数：{}", JSON.toJSONString(sessionFileQueryParam));
            Result<List<SessionFileVO>> fileResult = sessionFileSearchApi.files(sessionFileQueryParam);
            log.info("【文件应答】获取文件元数据，返回结果：{}", JSON.toJSONString(fileResult));
            if (Objects.nonNull(fileResult)) {
                return fileResult.getData();
            }
        } catch (Exception e) {
            log.error("【文件应答】获取文件元数据发生异常，请求为：{}", JSON.toJSONString(sessionFileQueryParam), e);
        }
        return Collections.emptyList();
    }
}
