package com.chinatelecom.gs.plugin.mcp.sdk.spec;


public class McpTransportSessionNotFoundException extends RuntimeException{

    public McpTransportSessionNotFoundException(String sessionId, Exception cause) {
        super("Session " + sessionId + " not found on the server", cause);
    }

    /**
     * Construct an instance with the session identifier but without a {@link Exception
     * cause}.
     * @param sessionId transport session identifier
     */
    public McpTransportSessionNotFoundException(String sessionId) {
        super("Session " + sessionId + " not found on the server");
    }
}
