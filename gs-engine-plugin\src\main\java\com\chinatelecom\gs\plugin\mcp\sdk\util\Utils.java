/*
 * Copyright 2024-2024 the original author or authors.
 */

package com.chinatelecom.gs.plugin.mcp.sdk.util;

import reactor.util.annotation.Nullable;

import java.net.URI;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * Miscellaneous utility methods.
 *
 * <AUTHOR>
 */

public final class Utils {

	/**
	 * Check whether the given {@code String} contains actual <em>text</em>.
	 * <p>
	 * More specifically, this method returns {@code true} if the {@code String} is not
	 * {@code null}, its length is greater than 0, and it contains at least one
	 * non-whitespace character.
	 * @param str the {@code String} to check (may be {@code null})
	 * @return {@code true} if the {@code String} is not {@code null}, its length is
	 * greater than 0, and it does not contain whitespace only
	 * @see Character#isWhitespace
	 */
	public static boolean hasText(@Nullable String str) {
		return (str != null && !str.isEmpty() && !str.trim().isEmpty());
	}

	/**
	 * Return {@code true} if the supplied Collection is {@code null} or empty. Otherwise,
	 * return {@code false}.
	 * @param collection the Collection to check
	 * @return whether the given Collection is empty
	 */
	public static boolean isEmpty(@Nullable Collection<?> collection) {
		return (collection == null || collection.isEmpty());
	}

	/**
	 * Return {@code true} if the supplied Map is {@code null} or empty. Otherwise, return
	 * {@code false}.
	 * @param map the Map to check
	 * @return whether the given Map is empty
	 */
	public static boolean isEmpty(@Nullable Map<?, ?> map) {
		return (map == null || map.isEmpty());
	}

	public static <T> CompletableFuture<T> toCompletableFuture(Future<T> future) {
		return CompletableFuture.supplyAsync(() -> {
			try {
				return future.get();  // blocking
			} catch (InterruptedException | ExecutionException e) {
				throw new RuntimeException(e);
			}
		});
	}

	public static CompletableFuture<Void> toCompletableFutureDiscard(Future<?> future) {
		return CompletableFuture.supplyAsync(() -> {
			try {
				future.get();  // blocking
				return null;
			} catch (InterruptedException | ExecutionException e) {
				throw new RuntimeException(e);
			}
		});
	}

	private static boolean isUnderBaseUri(URI baseUri, URI endpointUri) {
		if (!baseUri.getScheme().equals(endpointUri.getScheme())
				|| !baseUri.getAuthority().equals(endpointUri.getAuthority())) {
			return false;
		}

		URI normalizedBase = baseUri.normalize();
		URI normalizedEndpoint = endpointUri.normalize();

		String basePath = normalizedBase.getPath();
		String endpointPath = normalizedEndpoint.getPath();

		if (basePath.endsWith("/")) {
			basePath = basePath.substring(0, basePath.length() - 1);
		}
		return endpointPath.startsWith(basePath);
	}

	public static URI resolveUri(URI baseUrl, String endpointUrl) {
		URI endpointUri = URI.create(endpointUrl);
		if (endpointUri.isAbsolute() && !isUnderBaseUri(baseUrl, endpointUri)) {
			throw new IllegalArgumentException("Absolute endpoint URL does not match the base URL.");
		}
		else {
			return baseUrl.resolve(endpointUri);
		}
	}
}
