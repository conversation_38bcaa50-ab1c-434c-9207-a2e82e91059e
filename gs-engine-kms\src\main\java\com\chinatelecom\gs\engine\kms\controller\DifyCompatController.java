package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.rpc.UserInfo;
import com.chinatelecom.gs.engine.common.constants.Apis;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.InterceptorUtils;
import com.chinatelecom.gs.engine.common.utils.UserInfoUtils;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.enums.EnvType;
import com.chinatelecom.gs.engine.kms.sdk.vo.dify.*;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.BaseItem;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.SearchResp;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.sdk.SdkSearchResponse;
import com.chinatelecom.gs.engine.kms.search.SearchService;
import com.chinatelecom.gs.engine.kms.utils.MetadataConditionEvaluator;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(KmsApis.OPENAPI + Apis.OPEN + "/dify")
public class DifyCompatController {

    private static final String HEADER_STRING = "Bearer ";

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    @Resource
    private SearchService searchService;

    @PostMapping("/retrieval")
    public ResponseEntity<?> search(@RequestBody DifySearchRequest request, HttpServletRequest httpRequest) {
        try {
            // 认证与上下文设置
            setupRequestContext(httpRequest);

            // 2. 参数适配
            SearchParam searchParam = new SearchParam();
            searchParam.setEnv(EnvType.TEST);
            searchParam.setQuery(request.getQuery());
            SearchParam.Filter filter = new SearchParam.Filter();
            long topK = request.getRetrievalSetting().getTopK();
            double scoreThreshold = request.getRetrievalSetting().getScoreThreshold();
            filter.setTopN(topK != 0 ? topK : 10L);
            filter.setThreshold(scoreThreshold);
            List<SearchParam.KnowledgeFilter> knowledgeFilters = new ArrayList<>();
            SearchParam.KnowledgeFilter knowledgeFilter = new SearchParam.KnowledgeFilter();
            knowledgeFilter.setKnowledgeBaseCode(request.getKnowledgeId());
            knowledgeFilters.add(knowledgeFilter);
            filter.setKnowledgeFilters(knowledgeFilters);
            searchParam.setFilter(filter);

            // 3. 分片搜索调用
            SearchResp<? extends BaseItem> resp = searchService.searchChunkSync(searchParam);

            // 4. 结果适配和metadata条件筛选
            DifySearchResponse difyResp = new DifySearchResponse();
            if (resp != null && resp.getData() != null && resp.getData().getItems() != null) {
                for (SdkSearchResponse.SdkRetrieveItem<? extends BaseItem> item : resp.getData().getItems()) {
                    BaseItem base = item.getSource();

                    // 应用metadata条件筛选
                    if (request.getMetadataCondition() != null) {
                        if (!MetadataConditionEvaluator.evaluate(base, request.getMetadataCondition())) {
                            continue; // 跳过不符合条件的结果
                        }
                    }

                    DifySearchData data = new DifySearchData();
                    data.setContent(base.getContent());
                    data.setTitle(base.getTitle());
                    data.setScore(item.getScore());

                    // 构建增强的metadata
                    Metadata meta = buildEnhancedMetadata(base);
                    data.setMetadata(meta);

                    difyResp.getRecords().add(data);
                }
            }
            return ResponseEntity.ok(difyResp);

        } catch (BizException e) {
            log.error("Dify兼容接口业务异常: {}", e.getMessage(), e);
            return handleDifyError(e);
        } catch (Exception e) {
            log.error("Dify兼容接口系统异常: {}", e.getMessage(), e);
            DifyErrorResponse errorResp = new DifyErrorResponse(500, "内部服务器错误");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResp);
        }
    }

    /**
     * 构建增强的metadata，包含更多字段信息
     */
    private Metadata buildEnhancedMetadata(BaseItem base) {
        Metadata meta = new Metadata();
        meta.setPath(base.getFileKey());
        meta.setDescription(base.getDescription());

        // 添加扩展metadata字段
        if (base.getKnowledgeType() != null) {
            meta.addMetadata("category", base.getKnowledgeType().name());
        }
        if (base.getTags() != null && !base.getTags().isEmpty()) {
            meta.addMetadata("tags", base.getTags());
        }
        if (base.getCreateTime() != null) {
            meta.addMetadata("create_time", base.getCreateTime());
        }
        if (base.getUpdateTime() != null) {
            meta.addMetadata("update_time", base.getUpdateTime());
        }
        if (base.getPublishTime() != null) {
            meta.addMetadata("publish_time", base.getPublishTime());
        }
        if (base.getKnowledgeBaseName() != null) {
            meta.addMetadata("knowledge_base", base.getKnowledgeBaseName());
        }
        if (base.getKnowledgeCode() != null) {
            meta.addMetadata("knowledge_code", base.getKnowledgeCode());
        }
        if (base.getKnowledgeBaseCode() != null) {
            meta.addMetadata("knowledge_base_code", base.getKnowledgeBaseCode());
        }
        if (base.getCreateName() != null) {
            meta.addMetadata("create_name", base.getCreateName());
        }
        return meta;
    }

    /**
     * 处理Dify错误响应
     */
    private ResponseEntity<DifyErrorResponse> handleDifyError(BizException e) {
        String code = e.getCode();
        DifyErrorResponse errorResp;
        HttpStatus httpStatus;

        // 根据业务异常码映射到Dify错误码
        switch (code) {
            case "A0025": // 认证相关错误
                errorResp = new DifyErrorResponse(1002, "无效的API密钥");
                httpStatus = HttpStatus.FORBIDDEN;
                break;
            default:
                if (e.getMessage().contains("知识库不存在")) {
                    errorResp = new DifyErrorResponse(2001, "知识库不存在");
                    httpStatus = HttpStatus.BAD_REQUEST;
                } else {
                    errorResp = new DifyErrorResponse(500, "内部服务器错误");
                    httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
                }
                break;
        }

        return ResponseEntity.status(httpStatus).body(errorResp);
    }

    /**
     * 设置请求上下文信息
     */
    private void setupRequestContext(HttpServletRequest request) throws BizException {

        String authHeader = request.getHeader("Authorization");

        if (StringUtils.isEmpty(authHeader) || !authHeader.startsWith(HEADER_STRING)) {
            // 尝试从api-key头获取
            String apiKeyHeader = request.getHeader("api-key");
            if (StringUtils.isEmpty(apiKeyHeader)) {
                log.error("缺少API密钥");
                throw new BizException("A0025", "缺少API密钥");
            }
            authHeader = HEADER_STRING + apiKeyHeader;
        }

        String apiKey = authHeader.substring(HEADER_STRING.length()).trim();

        // 首先尝试secretDTO查询方式

        String userId;
        String tenantId;

        // Base64解码方式：tenantId@userId => Base64编码
        try {
            // Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(apiKey);
            String decodedString = new String(decodedBytes, StandardCharsets.UTF_8);

            // 按@分割获取tenantId和userId
            String[] parts = decodedString.split("@");
            if (parts.length != 2) {
                log.error("Base64解码后的格式不正确，应为 tenantId@userId 格式");
                throw new BizException("A0025", "无效的API密钥");
            }

            tenantId = parts[0];
            userId = parts[1];

            log.info("使用Base64解码方式获取用户信息: tenantId={}, userId={}", tenantId, userId);

        } catch (IllegalArgumentException e) {
            log.error("Base64解码失败: {}", e.getMessage());
            throw new BizException("A0025", "无效的API密钥");
        }

        UserInfo userInfo = UserInfoUtils.getUserInfo(tenantId, userId);
        if (userInfo == null) {
            log.error("用户信息不存在: tenantId={}, userId={}", tenantId, userId);
            throw new BizException("A0025", "用户信息不存在");
        }

        // 设置请求上下文
        RequestInfo requestInfo = new RequestInfo();
        RequestContext.set(requestInfo);

        requestInfo.setUserId(userId);
        requestInfo.setTenantId(tenantId);
        requestInfo.setUserName(userInfo.getName());
        requestInfo.setIsSuperTenant(superTenant.equals(tenantId));
        requestInfo.setIsAdmin(UserInfoUtils.isAdmin(tenantId, userId));

        requestInfo.setTeam(UserInfoUtils.getUserTeam());
//        requestInfo.setAppSourceType(AppSourceType.KS);
        InterceptorUtils.setAppCode(request, requestInfo);



    }

}