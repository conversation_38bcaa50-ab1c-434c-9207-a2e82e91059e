package com.chinatelecom.gs.engine.robot.dialog.execute.service.action.engine.inner.system.config;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.chinatelecom.gs.engine.common.cache.memory.SessionHistoryCacheService;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.core.corekit.common.core.AnswerService;
import com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum;
import com.chinatelecom.gs.engine.core.model.service.AgentIntentManagerAppService;
import com.chinatelecom.gs.engine.core.model.service.IntentRecognitionService;
import com.chinatelecom.gs.engine.core.sdk.rpc.ModelServiceClient;
import com.chinatelecom.gs.engine.core.sdk.vo.AgentIntentResponse;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentionRecognitionParam;
import com.chinatelecom.gs.engine.core.sdk.vo.IntentionRecognitionResponse;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.ToolCall;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelecom.gs.engine.core.sdk.vo.msg.MessageCallback;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.act.BotActTypeEnum;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.AgentStrategyConfig;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.StrategyNodeContext;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.StrategyNodeRequest;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.llm.LLMInvokeUtil;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.StrategyConfigUtil;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.act.StrategyExecutor;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.act.StrategyExecutorRegistry;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.strategy.processor.KnowledgeAcceptStrategyProcessor;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.toolselect.CandidateSelectService;
import com.chinatelecom.gs.engine.robot.dialog.memory.usr.agent.AgentInteractionStoreService;
import com.chinatelecom.gs.engine.robot.sdk.center.Interaction;
import com.chinatelecom.gs.engine.robot.sdk.center.SessionInteraction;
import com.chinatelecom.gs.engine.robot.sdk.dto.SourceInfo;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.ChatCodeConfigEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.ContentTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.DialogState;
import com.chinatelecom.gs.engine.robot.sdk.rpc.AgentSpeechConfigRpcApi;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.AgentConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.ModelConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.engine.request.DialogEngineRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.engine.request.NlgRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.common.Header;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.common.Track;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.config.AgentInterruptConfigResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.config.AgentSpeechConfigResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.DmToolIntent;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.ToolIntentAnswer;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.ToolMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.*;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.request.AnswerBuildRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.DialogMessageTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.SendMessageTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.DagEngine;
import com.chinatelecom.gs.workflow.core.workflow.core.context.DagContext;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.DagStatusEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.model.dag.DagHolder;
import com.chinatelecom.gs.workflow.core.workflow.core.model.dag.DagParam;
import com.chinatelecom.gs.workflow.core.workflow.core.model.result.DagResult;
import com.chinatelecom.gs.workflow.core.workflow.core.utils.DagParamUtils;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.utils.SpringContextUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class InterruptConfigEngineService implements IConfigEngineService {

    @Resource
    private AnswerService answerService;

    @Resource
    private IntentRecognitionService intentRecognitionService;

    @Resource
    private ModelServiceClient remoteServiceClient;

    @Resource
    private AgentIntentManagerAppService agentIntentManagerAppService;

    @Resource
    private AgentSpeechConfigRpcApi agentSpeechConfigRpcApi;

    @Resource
    private StrategyExecutorRegistry strategyExecutorRegistry;

    @Resource
    private CandidateSelectService candidateSelectService;

    @Resource
    private SessionHistoryCacheService sessionHistoryCacheService;

    @Resource
    private AgentInteractionStoreService agentInteractionStoreService;

    @Resource
    private KnowledgeAcceptStrategyProcessor knowledgeAcceptStrategy;

    @Resource
    private DagEngine dagEngine;

    @Override
    public ToolIntentAnswer genIntentAnswer(NlgRequest nlgRequest, AgentConfig agentConfig, DmToolIntent dmIntent) {
        Answer answer = this.getAnswer(nlgRequest.getHeader(), agentConfig, dmIntent);
        if (Objects.isNull(answer)) {
            return null;
        }
        ToolIntentAnswer intentAnswer = BeanUtil.toBean(dmIntent, ToolIntentAnswer.class);
        answer.setInstructions(Lists.newArrayList(new Instruction(ChatCodeConfigEnum.SYSTEM_INTERRUPT.getCode(), new HashMap<>())));
        return intentAnswer;
    }

    @Override
    public AgentAnswer genIntentAnswer(DialogEngineRequest dialogEngineRequest, AgentConfig agentConfig) {
        AgentAnswer agentAnswer = getAgentAnswer(dialogEngineRequest, agentConfig);
        if (Objects.isNull(agentAnswer)) {
            return buildNoAnswer(dialogEngineRequest);
        }
        return agentAnswer;
    }

    /**
     * 返回未识别到打断意图的答案
     *
     * @param dialogEngineRequest DialogEngineRequest
     * @return AgentAnswer
     */
    private AgentAnswer buildNoAnswer(DialogEngineRequest dialogEngineRequest) {
        AgentAnswer noAnswer = new AgentAnswer();
        DmToolIntent dmToolIntent = new DmToolIntent();
        dmToolIntent.setToolIntentId(ChatCodeConfigEnum.SYSTEM_INTERRUPT_END.getCode());
        dmToolIntent.setToolIntentName(ChatCodeConfigEnum.SYSTEM_INTERRUPT_END.getDesc());
        dmToolIntent.setScore(1.0);
        dmToolIntent.setResponseType(AnswerResponseType.DIRECT);
        noAnswer.setDmToolIntent(dmToolIntent);
        noAnswer.setAnswerSourceType(AnswerSourceType.SYSTEM);
        noAnswer.setMessageId(dialogEngineRequest.getHeader().getTrack().getDownMessageId());
        noAnswer.setAnswerType(AnswerTypeEnum.INSTRUCT);
        noAnswer.setInstructions(Lists.newArrayList(new Instruction(ChatCodeConfigEnum.SYSTEM_INTERRUPT_END.getCode(), new HashMap<>())));
        return noAnswer;
    }

    private AgentAnswer getAgentAnswer(DialogEngineRequest dialogEngineRequest, AgentConfig agentConfig) {
        Result<AgentSpeechConfigResponse> speechConfig = agentSpeechConfigRpcApi.querySpeechConfig(dialogEngineRequest.getAgentCode());
        AgentInterruptConfigResponse agentInterruptConfig = speechConfig.getData().getAgentInterruptConfig();
        if (!speechConfig.isSuccess() || Objects.isNull(agentInterruptConfig) || "0".equals(agentInterruptConfig.getEnable())) {
            log.warn("当前机器人未开启打断配置！");
            return null;
        }
        Map<String, Object> extraData = dialogEngineRequest.getExtraData();
        if (CollectionUtil.isEmpty(extraData)) {
            log.warn("extraData 用户打断话术为空");
            return null;
        }
        Object instructionValues = extraData.get("instructionValues");
        if (Objects.isNull(instructionValues)) {
            log.warn("instructionValues 用户打断话术为空");
            return null;
        }
        String userQuery = JSON.parseObject(JSON.toJSONString(instructionValues)).getString("userQuery");
        if (StringUtils.isBlank(userQuery)) {
            log.warn("userQuery 用户打断话术为空");
        }
        AgentIntentResponse agentIntentResponse = agentIntentManagerAppService.queryInterruptIntent();
        if (Objects.isNull(agentIntentResponse)) {
            log.warn("未查询到打断意图！");
            return null;
        }
        ModelPageListParam defaultModel = remoteServiceClient.getDefaultModel(ModelTypeEnum.OFFLINE_LLM.getCode());
        IntentionRecognitionParam recognitionParam = new IntentionRecognitionParam();
        recognitionParam.setQuery(userQuery);
        recognitionParam.setIntentList(Lists.newArrayList(agentIntentResponse.getCode()));
        recognitionParam.setModelCode(defaultModel.getModelCode());
        Track track = dialogEngineRequest.getHeader().getTrack();
        recognitionParam.setSessionId(track.getSessionId());
        IntentionRecognitionResponse intentionRecognitionResponse = intentRecognitionService.intentRecognition(recognitionParam);
        log.info("【打断】意图识别结果：{}", JSON.toJSONString(intentionRecognitionResponse));
        if (Objects.isNull(intentionRecognitionResponse)) {
            log.warn("未识别到打断意图");
            return convertAgentAnswer(dialogEngineRequest, agentConfig, userQuery, agentInterruptConfig, track);
        }
        MessageCallback<ToolMessageResponse> messageCallback = dialogEngineRequest.getMessageCallback();
        sendInterrupt(track, agentInterruptConfig, agentIntentResponse, messageCallback);
        try {
            // 发送消息耗时长，避免finish消息先发送
            Thread.sleep(300);
        } catch (InterruptedException e) {
            log.error("【打断】发送消息等待异常！", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("【打断】发送消息等待异常！", e);
        }
        log.info("【打断】发送消息成功！");
        return getAgentAnswer(agentIntentResponse, speechConfig, track);
    }

    private AgentAnswer convertAgentAnswer(DialogEngineRequest dialogEngineRequest, AgentConfig agentConfig, String userQuery, AgentInterruptConfigResponse agentInterruptConfig, Track track) {
        ToolIntentAnswer toolIntentAnswer = checkOtherIntents(dialogEngineRequest, agentConfig, userQuery, agentInterruptConfig);
        if (Objects.isNull(toolIntentAnswer)) {
            return null;
        }
        ToolAnswer toolAnswer = toolIntentAnswer.getToolAnswer();
        AnswerBuildRequest answerBuildRequest = new AnswerBuildRequest();
        answerBuildRequest.setAnswerTypeEnum(toolAnswer.getAnswerType());
        answerBuildRequest.setContent(toolAnswer.getContent());
        answerBuildRequest.setInstructions(toolAnswer.getInstructions());
        answerBuildRequest.setEnableSmartInterruption(toolAnswer.getEnableSmartInterruption());
        // 补充reasoning信息封装
        if (Objects.nonNull(toolAnswer.getReasoning())) {
            answerBuildRequest.setReasoningContent(toolAnswer.getReasoning().getReasoningContent());
        }
        Answer answer = this.answerService.buildAnswer(answerBuildRequest);
        answer.setMessageId(toolAnswer.getMessageId());
        answer.setExtraData(toolAnswer.getExtraData());
        answer.setActions(toolAnswer.getActions());
        answer.setSourceInfos(Collections.singletonList(buildSource(toolIntentAnswer)));
        AgentAnswer agentAnswer = BeanUtil.toBean(answer, AgentAnswer.class);
        DmToolIntent answerIntent = new DmToolIntent();
        answerIntent.setToolIntentId(toolIntentAnswer.getToolIntentId());
        answerIntent.setDialogEngineType(toolIntentAnswer.getDialogEngineType());
        agentAnswer.setAnswerSourceType(AnswerSourceType.LLM);
        agentAnswer.setDmToolIntent(answerIntent);
        agentAnswer.setMessageId(track.getDownMessageId());
//
//        Speech speech = new Speech();
//        speech.setType(SpeechTypeEnum.PLAIN_TEXT.getCode());
//        speech.setTts(toolIntentAnswer.getContent());
//        agentAnswer.setSpeech(speech);
//        agentAnswer.setAnswerType(toolIntentAnswer.getToolAnswer().getAnswerType());
//        agentAnswer.setAnswerSourceType(AnswerSourceType.LLM);
//        agentAnswer.setContent(toolIntentAnswer.getContent());
        return agentAnswer;
    }


    private SourceInfo buildSource(ToolIntentAnswer toolIntentAnswer){
        SourceInfo sourceInfo = new SourceInfo();
        sourceInfo.setSourceCode(toolIntentAnswer.getToolIntentId());
        sourceInfo.setSourceName(toolIntentAnswer.getToolIntentName());
        sourceInfo.setSourceType(toolIntentAnswer.getDialogEngineType().getCode());
        SourceInfo.Candidate candidate = new SourceInfo.Candidate();
        candidate.setName(toolIntentAnswer.getToolIntentName());
        candidate.setContent("");
        sourceInfo.setCandidates(Collections.singletonList(candidate));
        return sourceInfo;
    }


    /**
     * 发送打断指令和话术
     *
     * @param track                Track
     * @param agentInterruptConfig AgentInterruptConfigResponse
     * @param agentIntentResponse  AgentIntentResponse
     * @param messageCallback      MessageCallback
     */
    private void sendInterrupt(Track track, AgentInterruptConfigResponse agentInterruptConfig, AgentIntentResponse agentIntentResponse, MessageCallback<ToolMessageResponse> messageCallback) {
        ToolIntentAnswer toolIntentAnswer = new ToolIntentAnswer();
        ToolAnswer toolAnswer = new ToolAnswer();
        toolAnswer.setMessageId(track.getDownMessageId());
        toolAnswer.setResponseType(AnswerResponseType.DIRECT);
        toolAnswer.setAnswerType(AnswerTypeEnum.PLAIN_TEXT);
        toolAnswer.setContent(agentInterruptConfig.getCommonInterruptPhrases());
        toolAnswer.setContentType(ContentTypeEnum.PART.getCode());
        toolAnswer.setInstructions(Lists.newArrayList(new Instruction(ChatCodeConfigEnum.SYSTEM_INTERRUPT.getCode(), new HashMap<>())));
        toolIntentAnswer.setToolAnswer(toolAnswer);
        toolIntentAnswer.setDialogEngineType(DialogEngineType.SYSTEM);
        toolIntentAnswer.setToolIntentId(agentIntentResponse.getCode());
        toolIntentAnswer.setToolIntentName(agentIntentResponse.getName());
        toolIntentAnswer.setScore(1.0);
        toolIntentAnswer.setSimilarContent(agentInterruptConfig.getCommonInterruptPhrases());
        toolIntentAnswer.setResponseType(AnswerResponseType.DIRECT);
        toolIntentAnswer.setContent(agentInterruptConfig.getCommonInterruptPhrases());
        // 发送通用打断话术和打断指令
        ToolMessageResponse messageResponse = ToolMessageResponse.builder()
                .eventType(SendMessageTypeEnum.ADD)
                .toolAnswer(toolIntentAnswer)
                .messageType(DialogMessageTypeEnum.PART)
                .sessionId(track.getSessionId())
                .downMsgId(track.getDownMessageId())
                .upMsgId(track.getMessageId())
                .userId(RequestContext.getUserId()).build();
        messageCallback.invoke(messageResponse);
    }

    private AgentAnswer getAgentAnswer(AgentIntentResponse agentIntentResponse, Result<AgentSpeechConfigResponse> speechConfig, Track track) {
        AgentAnswer agentAnswer = new AgentAnswer();
        DmToolIntent dmToolIntent = new DmToolIntent();
        dmToolIntent.setToolIntentId(agentIntentResponse.getCode());
        dmToolIntent.setToolIntentName(agentIntentResponse.getName());
        dmToolIntent.setScore(1.0);
        dmToolIntent.setResponseType(AnswerResponseType.DIRECT);
        dmToolIntent.setContent(speechConfig.getData().getAgentInterruptConfig().getCommonInterruptPhrases());

        agentAnswer.setContent(speechConfig.getData().getAgentInterruptConfig().getCommonInterruptPhrases());
        agentAnswer.setDmToolIntent(dmToolIntent);
        agentAnswer.setAnswerSourceType(AnswerSourceType.SYSTEM);
        agentAnswer.setMessageId(track.getDownMessageId());
        agentAnswer.setAnswerType(AnswerTypeEnum.PLAIN_TEXT);
        return agentAnswer;
    }

    @Override
    public String configCode() {
        return ChatCodeConfigEnum.SYSTEM_INTERRUPT.getCode();
    }

    private Answer getAnswer(Header header, AgentConfig config, DmToolIntent dmIntent) {
        String content = config.getDialogConfig().getNoAnswerScript();
        if (StringUtils.isEmpty(content)) {
            return null;
        }

        AnswerBuildRequest answerBuildRequest = new AnswerBuildRequest();
        answerBuildRequest.setHeader(header);
        answerBuildRequest.setAnswerTypeEnum(AnswerTypeEnum.PLAIN_TEXT);
        answerBuildRequest.setContent(content);
        answerBuildRequest.setInstructions(Lists.newArrayList(new Instruction(ChatCodeConfigEnum.SYSTEM_INTERRUPT.getCode(), new HashMap<>())));

        List<String> guides = new ArrayList<>(config.getDialogConfig().getAgentGuide());
        Collections.shuffle(guides);

        answerBuildRequest.setOptions(guides.stream().limit(3).collect(Collectors.toList()));
        return this.answerService.buildAnswer(answerBuildRequest);
    }

    /**
     * 召回其他意图
     *
     * @param dialogEngineRequest  DialogEngineRequest
     * @param agentConfig          AgentConfig
     * @param query                String
     * @param agentInterruptConfig AgentInterruptConfigResponse
     * @return ToolIntentAnswer
     */
    public ToolIntentAnswer checkOtherIntents(DialogEngineRequest dialogEngineRequest, AgentConfig agentConfig, String query, AgentInterruptConfigResponse agentInterruptConfig) {
        StrategyNodeContext strategyNodeContext = StrategyConfigUtil.buildContext(dialogEngineRequest, agentConfig, query);
        StrategyNodeRequest nodeRequest = strategyNodeContext.getStrategyNodeRequest();
        updateLastState(nodeRequest, agentConfig.getAgentCode(), dialogEngineRequest.getHeader().getTrack().getSessionId());
        strategyNodeContext.setStrategyNodeRequest(nodeRequest);

        StrategyExecutor strategyExecutor = strategyExecutorRegistry.getStrategy(agentConfig.getBotType(), BotActTypeEnum.FLOW.getCode());
        if (Objects.isNull(strategyExecutor)) {
            log.error("没有找到合适的执行策略 bot {}", agentConfig.getAgentCode());
            return null;
        }

        Instruction instruction = new Instruction();
        instruction.setCode(ChatCodeConfigEnum.SYSTEM_INTERRUPT.getCode());
        instruction.setValues(Maps.of("userQuery", query));

        DagContext dagContext = StrategyConfigUtil.buildDagContext(dialogEngineRequest, instruction, query);
        DagHolder.set(dagContext, null);
        List<ToolIntentAnswer> recallIntents = strategyExecutor.deRecallByEngines(strategyNodeContext, dagContext, Arrays.asList(DialogEngineType.KMS, DialogEngineType.WORKFLOW));
        log.info("【打断】召回意图：{}", JSON.toJSONString(recallIntents));
        recallIntents = selectIntents(strategyNodeContext, dagContext, recallIntents);
        log.info("【打断】工具选择后意图：{}", JSON.toJSONString(recallIntents));
        nodeRequest.setSelectedContents(true);
        strategyNodeContext.setStrategyNodeRequest(nodeRequest);

        if (!CollectionUtil.isEmpty(recallIntents)) {

            //仍然在上一个流程上
            if (recallIntents.size() == 1 &&
                    StringUtils.isNotEmpty(nodeRequest.getLastToolCode()) &&
                    recallIntents.get(0).getToolIntentId().equalsIgnoreCase(nodeRequest.getLastToolCode()) &&
                    DialogEngineType.WORKFLOW.equals(recallIntents.get(0).getDialogEngineType())) {
                ToolIntentAnswer toolAnswer = executeWorkflow(nodeRequest.getLastToolCode(), dagContext, null, query, null);
                if (Objects.nonNull(toolAnswer)) {
                    sendInterruptCommand(dialogEngineRequest);
                    return toolAnswer;
                }
            }

            // 先回调打断指令，再处理生成
            sendInterruptCommand(dialogEngineRequest);
            //只进行FAQ召回
            strategyNodeContext.getStrategyNodeRequest().setRecallContents(recallIntents);
            ToolMixerAnswer strategyAnswer = knowledgeAcceptStrategy.execute(strategyNodeContext, dagContext);
            if(Objects.nonNull(strategyAnswer) && Objects.nonNull(strategyAnswer.getToolAnswer())){
                return strategyAnswer;
            }else{
                // 推送打断话术
                return sendCommonInterruptPhrases(dialogEngineRequest, agentInterruptConfig);
            }
//            List<ToolMixerAnswer> toolMixerAnswers = strategyExecutor.executeWithRecallItems(strategyNodeContext, dagContext, recallIntents);
//            if (!CollectionUtils.isEmpty(toolMixerAnswers)) {
//                log.info("打断召回知识成功，召回意图：{}", JSON.toJSONString(toolMixerAnswers));
//                ToolIntentAnswer toolIntentAnswer = toolMixerAnswers.get(toolMixerAnswers.size() - 1);
//                if(Objects.nonNull(toolIntentAnswer) && Objects.nonNull(toolIntentAnswer.getToolAnswer())){
//                    return toolIntentAnswer;
//                }else{
//                    // 推送打断话术
//                    sendCommonInterruptPhrases(dialogEngineRequest, agentInterruptConfig);
//                }
//                // 再推一笔返回实际处理内容
//                //return toolMixerAnswers.get(toolMixerAnswers.size() - 1);
//            } else {
//                // 推送打断话术
//                sendCommonInterruptPhrases(dialogEngineRequest, agentInterruptConfig);
//            }
        }
        return null;
    }


    protected ToolIntentAnswer executeWorkflow(String workflowId, DagContext context, ToolCall toolCall, String userQuery, Object userFile) {
        Map<String, Object> inputParamMap = new HashMap<>();
        if (Objects.nonNull(toolCall)) {
            JSONObject argsObj = new JSONObject();
            String args = toolCall.getFunction().getArguments();
            if (StringUtils.isNotBlank(args)) {
                argsObj = JSON.parseObject(args);
            }
            inputParamMap.putAll(argsObj);
        }

        inputParamMap.put("BOT_USER_INPUT", userQuery);
        inputParamMap.put("FILE_USER", userFile);

        Map<String, Object> globalVar = new HashMap<>();
        globalVar.put("BOT_USER_INPUT", userQuery);
        globalVar.put("FILE_USER", userFile);

        DagParam childDagParam = DagParamUtils.buildChildParam(context);
        childDagParam.setGlobalVarMap(globalVar);
        childDagParam.setInputParamMap(inputParamMap);
        DagContext childDagContext = new DagContext();
        childDagContext.setMainFlow(false);
        childDagContext.setDialogStatus(context.getDialogStatus());
        childDagContext.setDagParam(childDagParam);
        DagResult dagResult = dagEngine.execute(workflowId, childDagContext);

        ToolIntentAnswer current = dagResult.getToolAnswer();
        if (Objects.nonNull(current)) {
            if (dagResult.getDagStatus().equals(DagStatusEnum.PAUSE)) {
                current.setDialogState(DialogState.WEAK_PROGRESS);
            } else {
                current.setDialogState(DialogState.COMPLETE);
            }
        }


        if (!CollectionUtils.isEmpty(childDagContext.getToolAnswers())) {
            List<ToolIntentAnswer> historyAnswers = new ArrayList<>(childDagContext.getToolAnswers());
            if (Objects.nonNull(current)) {
                //当前产生了answer，已经推出去过了，那就不把子流程产生的最后一笔，也就是相同的answer加进去
                historyAnswers.remove(historyAnswers.size() - 1);
            }
            if (!CollectionUtils.isEmpty(historyAnswers)) {
                context.addToolAnswers(historyAnswers);
            }
        }

        if (Objects.nonNull(current)) {
            current.setDmInfo(toolCall);
            return current;
        }

        return null;
    }


    private List<ToolIntentAnswer> selectIntents(StrategyNodeContext strategyNodeContext, DagContext dagContext, List<ToolIntentAnswer> recallIntents) {

        StrategyNodeRequest strategyNodeRequest = strategyNodeContext.getStrategyNodeRequest();
        strategyNodeRequest.setRecallContents(recallIntents);
        strategyNodeContext.setStrategyNodeRequest(strategyNodeRequest);


        LLMInvokeUtil llmInvokeUtil = SpringContextUtils.getBean(LLMInvokeUtil.class);


        List<WrapLLMMessage> useHistories = sessionHistoryCacheService.getChatHistoryFromCache(dagContext.getSessionId(),
                dagContext.getMessageId(),
                Optional.ofNullable(strategyNodeContext.getAgentStrategyConfig()).map(AgentStrategyConfig::getAgentConfig)
                        .map(AgentConfig::getModelConfig)
                        .map(ModelConfig::getMemoryCount).orElse(null),
                true, true);


        Map<String, List<ToolIntentAnswer>> candidatesByType = this.candidateSelectService.selectCandidates(strategyNodeContext, dagContext, useHistories, llmInvokeUtil);
        if (candidatesByType.size() == 1) {
            return candidatesByType.values().iterator().next();
        }

        return Collections.emptyList();
    }


    private void updateLastState(StrategyNodeRequest strategyNodeRequest, String agentCode, String sessionId) {
        SessionInteraction sessionInteraction = this.agentInteractionStoreService.getInteraction(agentCode, sessionId);
        if (Objects.nonNull(sessionInteraction) && !org.apache.commons.collections4.CollectionUtils.isEmpty(sessionInteraction.getInteractions())) {
            Interaction lastInteraction = sessionInteraction.getInteractions().get(sessionInteraction.getInteractions().size() - 1);
            strategyNodeRequest.setLastDialogState(lastInteraction.getDialogState());
            List<Interaction.ToolIntentState> toolIntentStates = lastInteraction.getToolIntentStates();
            if (!org.apache.commons.collections4.CollectionUtils.isEmpty(toolIntentStates)) {
                strategyNodeRequest.setLastToolCode(toolIntentStates.get(toolIntentStates.size() - 1).getToolIntentCode());
            }
            strategyNodeRequest.setLastDialogBreak(true);
        }
    }

    /**
     * 发送打断话术
     *
     * @param dialogEngineRequest  DialogEngineRequest
     * @param agentInterruptConfig AgentInterruptConfigResponse
     */
    private ToolIntentAnswer sendCommonInterruptPhrases(DialogEngineRequest dialogEngineRequest, AgentInterruptConfigResponse agentInterruptConfig) {
        MessageCallback<ToolMessageResponse> messageCallback = dialogEngineRequest.getMessageCallback();
        Track track = dialogEngineRequest.getHeader().getTrack();
        ToolIntentAnswer toolIntentAnswer = new ToolIntentAnswer();
        ToolAnswer toolAnswer = new ToolAnswer();
        toolAnswer.setMessageId(track.getDownMessageId());
        toolAnswer.setResponseType(AnswerResponseType.DIRECT);
        toolAnswer.setAnswerType(AnswerTypeEnum.PLAIN_TEXT);
        toolAnswer.setContent(agentInterruptConfig.getCommonInterruptPhrases());
        toolAnswer.setContentType(ContentTypeEnum.PART.getCode());
        toolAnswer.setInstructions(Lists.newArrayList(new Instruction(ChatCodeConfigEnum.SYSTEM_INTERRUPT.getCode(), new HashMap<>())));
        toolIntentAnswer.setToolAnswer(toolAnswer);
        toolIntentAnswer.setDialogEngineType(DialogEngineType.SYSTEM);
        toolIntentAnswer.setToolIntentId(ChatCodeConfigEnum.SYSTEM_INTERRUPT.getCode());
        toolIntentAnswer.setToolIntentName(ChatCodeConfigEnum.SYSTEM_INTERRUPT.getDesc());
        toolIntentAnswer.setContent(agentInterruptConfig.getCommonInterruptPhrases());
        toolIntentAnswer.setScore(1.0);
        toolIntentAnswer.setResponseType(AnswerResponseType.DIRECT);


        ToolMessageResponse messageResponse = ToolMessageResponse.builder()
                .eventType(SendMessageTypeEnum.ADD)
                .toolAnswer(toolIntentAnswer)
                .messageType(DialogMessageTypeEnum.PART)
                .sessionId(track.getSessionId())
                .downMsgId(track.getDownMessageId())
                .upMsgId(track.getMessageId())
                .userId(RequestContext.getUserId()).build();
        messageCallback.invoke(messageResponse);
        log.info("发送通用打断话术：{}", JSON.toJSONString(messageResponse));
        return toolIntentAnswer;
    }

    /**
     * 发送打断指令
     *
     * @param dialogEngineRequest DialogEngineRequest
     */
    private void sendInterruptCommand(DialogEngineRequest dialogEngineRequest) {
        MessageCallback<ToolMessageResponse> messageCallback = dialogEngineRequest.getMessageCallback();
        Track track = dialogEngineRequest.getHeader().getTrack();
        ToolIntentAnswer toolIntentAnswer = new ToolIntentAnswer();
        ToolAnswer toolAnswer = new ToolAnswer();
        toolAnswer.setMessageId(track.getDownMessageId());
        toolAnswer.setResponseType(AnswerResponseType.DIRECT);
        toolAnswer.setAnswerType(AnswerTypeEnum.INSTRUCT);
        toolAnswer.setContentType(ContentTypeEnum.PART.getCode());
        toolAnswer.setInstructions(Lists.newArrayList(new Instruction(ChatCodeConfigEnum.SYSTEM_INTERRUPT.getCode(), new HashMap<>())));
        toolIntentAnswer.setToolAnswer(toolAnswer);
        toolIntentAnswer.setDialogEngineType(DialogEngineType.SYSTEM);
        toolIntentAnswer.setToolIntentId(ChatCodeConfigEnum.SYSTEM_INTERRUPT.getCode());
        toolIntentAnswer.setToolIntentName(ChatCodeConfigEnum.SYSTEM_INTERRUPT.getDesc());
        toolIntentAnswer.setScore(1.0);
        toolIntentAnswer.setResponseType(AnswerResponseType.DIRECT);


        ToolMessageResponse messageResponse = ToolMessageResponse.builder()
                .eventType(SendMessageTypeEnum.ADD)
                .toolAnswer(toolIntentAnswer)
                .messageType(DialogMessageTypeEnum.PART)
                .sessionId(track.getSessionId())
                .downMsgId(track.getDownMessageId())
                .upMsgId(track.getMessageId())
                .userId(RequestContext.getUserId()).build();
        messageCallback.invoke(messageResponse);
        log.info("发送打断指令：{}", JSON.toJSONString(messageResponse));
    }


}
