package com.chinatelecom.gs.plugin.mcp.sandbox;

import com.chinatelecom.gs.plugin.mcp.sdk.spec.McpClientSession;
import com.chinatelecom.gs.plugin.mcp.sdk.spec.McpError;
import com.chinatelecom.gs.plugin.mcp.sdk.spec.McpSchema;
import com.chinatelecom.gs.plugin.mcp.sdk.spec.McpSession;
import com.chinatelecom.gs.plugin.mcp.sdk.util.Assert;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import reactor.core.publisher.Mono;
import reactor.core.publisher.MonoSink;

import java.time.Duration;
import java.util.Collections;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;

@Slf4j
public class McpSandboxClientSession implements McpSession {

    /** Duration to wait for request responses before timing out */
    private final Duration requestTimeout;

    /** Transport layer implementation for message exchange */
    private final SandboxStdioClientTransport transport;

    /** Map of pending responses keyed by request ID */
    private final ConcurrentHashMap<Object, MonoSink<McpSchema.JSONRPCResponse>> pendingResponses = new ConcurrentHashMap<>();

    /** Session-specific prefix for request IDs */
    private final String sessionPrefix = UUID.randomUUID().toString().substring(0, 8);

    /** Atomic counter for generating unique request IDs */
    private final AtomicLong requestCounter = new AtomicLong(0);

    /** Map of request handlers keyed by method name */
    private final ConcurrentHashMap<String, McpClientSession.RequestHandler<?>> requestHandlers = new ConcurrentHashMap<>();

    /** Map of notification handlers keyed by method name */
    private final ConcurrentHashMap<String, McpClientSession.NotificationHandler> notificationHandlers = new ConcurrentHashMap<>();


    public McpSandboxClientSession(SandboxSpec spec,
                                   Map<String, McpClientSession.RequestHandler<?>> requestHandlers,
                                   Map<String, McpClientSession.NotificationHandler> notificationHandlers) {
        Assert.notNull(spec, "The spec can not be null");
        this.requestTimeout = spec.getRequestTimeout();
        this.transport = spec.getTransport();
        this.requestHandlers.putAll(requestHandlers);
        this.notificationHandlers.putAll(notificationHandlers);

        Function<Mono<McpSchema.JSONRPCMessage>, Mono<McpSchema.JSONRPCMessage>> handler = mono -> mono.doOnNext(message -> {
            if (message instanceof McpSchema.JSONRPCResponse ) {
                var response = (McpSchema.JSONRPCResponse) message;
                log.info("Received Response: {}", response);
                var sink = pendingResponses.remove(response.getId());
                if (sink == null) {
                    log.warn("Unexpected response for unkown id {}", response.getId());
                }
                else {
                    sink.success(response);
                }
            }
            else if (message instanceof McpSchema.JSONRPCRequest) {
                var request = (McpSchema.JSONRPCRequest) message;
                log.debug("Received request: {}", request);
                handleIncomingRequest(request).subscribe(response -> transport.sendMessage(response).subscribe(),
                        error -> {
                            var errorResponse = new McpSchema.JSONRPCResponse(McpSchema.JSONRPC_VERSION, request.getId(),
                                    null, new McpSchema.JSONRPCResponse.JSONRPCError(
                                    McpSchema.ErrorCodes.INTERNAL_ERROR, error.getMessage(), null));
                            transport.sendMessage(errorResponse).subscribe();
                        });
            }
            else if (message instanceof McpSchema.JSONRPCNotification) {
                var notification = (McpSchema.JSONRPCNotification) message;
                log.debug("Received notification: {}", notification);
                handleIncomingNotification(notification).subscribe(null, error -> log.error("Error handling notification: {}", error.getMessage()));
            }
        });
        this.transport.handleIncomingMessages(handler);
    }

    /**
     * Handler
     */

    private Mono<McpSchema.JSONRPCResponse> handleIncomingRequest(McpSchema.JSONRPCRequest request) {
        return Mono.defer(() -> {
            var handler = this.requestHandlers.get(request.getMethod());
            if (handler == null) {
                McpClientSession.MethodNotFoundError error = getMethodNotFoundError(request.getMethod());
                return Mono.just(new McpSchema.JSONRPCResponse(McpSchema.JSONRPC_VERSION, request.getId(), null,
                        new McpSchema.JSONRPCResponse.JSONRPCError(McpSchema.ErrorCodes.METHOD_NOT_FOUND,
                                error.getMessage(), error.getData())));
            }

            return handler.handle(request.getParams())
                    .map(result -> new McpSchema.JSONRPCResponse(McpSchema.JSONRPC_VERSION, request.getId(), result, null))
                    .onErrorResume(error -> Mono.just(new McpSchema.JSONRPCResponse(McpSchema.JSONRPC_VERSION, request.getId(),
                            null, new McpSchema.JSONRPCResponse.JSONRPCError(McpSchema.ErrorCodes.INTERNAL_ERROR,
                            error.getMessage(), null)))); // TODO: add error message
            // through the data field
        });
    }

    private Mono<Void> handleIncomingNotification(McpSchema.JSONRPCNotification notification) {
        return Mono.defer(() -> {
            var handler = notificationHandlers.get(notification.getMethod());
            if (handler == null) {
                log.error("No handler registered for notification method: {}", notification.getMethod());
                return Mono.empty();
            }
            return handler.handle(notification.getParams());
        });
    }

    public static McpClientSession.MethodNotFoundError getMethodNotFoundError(String method) {
        switch (method) {
            case McpSchema.METHOD_ROOTS_LIST:
                return new McpClientSession.MethodNotFoundError(method, "Roots not supported",
                        Collections.singletonMap("reason", "Client does not have roots capability"));
            default:
                return new McpClientSession.MethodNotFoundError(method, "Method not found: " + method, null);
        }
    }


    /**
     * Generates a unique request ID in a non-blocking way. Combines a session-specific
     * prefix with an atomic counter to ensure uniqueness.
     * @return A unique request ID string
     */
    private String generateRequestId() {
        return this.sessionPrefix + "-" + this.requestCounter.getAndIncrement();
    }

    /**
     * Sends a JSON-RPC request and returns the response.
     * @param <T> The expected response type
     * @param method The method name to call
     * @param requestParams The request parameters
     * @param typeRef Type reference for response deserialization
     * @return A Mono containing the response
     */
    @Override
    public <T> Mono<T> sendRequest(String method, Object requestParams, TypeReference<T> typeRef) {
        String requestId = this.generateRequestId();

        return Mono.<McpSchema.JSONRPCResponse>create(sink -> {
            this.pendingResponses.put(requestId, sink);
            McpSchema.JSONRPCRequest jsonrpcRequest = new McpSchema.JSONRPCRequest(McpSchema.JSONRPC_VERSION, method, requestId, requestParams);

            //--------向沙箱发送请求---------
            this.transport.sendMessage(jsonrpcRequest)
                    .subscribe(v -> {
                    }, error -> {
                        this.pendingResponses.remove(requestId);
                        sink.error(error);
                    });
        }).timeout(this.requestTimeout).handle((jsonRpcResponse, sink) -> {
            if (jsonRpcResponse.getError() != null) {
                sink.error(new McpError(jsonRpcResponse.getError()));
            }
            else {
                if (typeRef.getType().equals(Void.class)) {
                    sink.complete();
                }
                else {
                    sink.next(this.transport.unmarshalFrom(jsonRpcResponse.getResult(), typeRef));
                }
            }
        });
    }

    /**
     * Sends a JSON-RPC notification.
     * @param method The method name for the notification
     * @param params The notification parameters
     * @return A Mono that completes when the notification is sent
     */
    @Override
    public Mono<Void> sendNotification(String method, Map<String, Object> params) {
        McpSchema.JSONRPCNotification jsonrpcNotification = new McpSchema.JSONRPCNotification(McpSchema.JSONRPC_VERSION, method, params);
        return this.transport.sendMessage(jsonrpcNotification);
    }


    @Override
    public Mono<Void> closeGracefully() {
        return Mono.defer(() -> {
            return transport.closeGracefully();
        });
    }

    @Override
    public void close() {
        transport.close();
    }

}
