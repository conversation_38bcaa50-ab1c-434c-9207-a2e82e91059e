package com.chinatelecom.gs.engine.kms.controller;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.config.aspect.annotation.DebugLog;
import com.chinatelecom.gs.engine.common.config.property.GsGlobalConfig;
import com.chinatelecom.gs.engine.common.context.LocalContext;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.enums.LogStatusEnum;
import com.chinatelecom.gs.engine.common.enums.LogTypeEnum;
import com.chinatelecom.gs.engine.common.log.track.LogMessage;
import com.chinatelecom.gs.engine.common.log.track.LogMqProducer;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.BizAssert;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.manager.convert.ConfigConvert;
import com.chinatelecom.gs.engine.core.manager.service.BaseConfigAppService;
import com.chinatelecom.gs.engine.core.manager.vo.config.PublishConfig;
import com.chinatelecom.gs.engine.kms.base.model.CodeEmptyParam;
import com.chinatelecom.gs.engine.kms.sdk.constants.KmsApis;
import com.chinatelecom.gs.engine.kms.sdk.enums.EnvType;
import com.chinatelecom.gs.engine.kms.sdk.enums.IntermediateTypeEnum;
import com.chinatelecom.gs.engine.kms.sdk.enums.SceneType;
import com.chinatelecom.gs.engine.kms.sdk.enums.SearchOrderType;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.chunk.ChunkDataVO;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.chunk.ChunkQueryParam;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.*;
import com.chinatelecom.gs.engine.kms.sdk.vo.search.sdk.SdkSearchResponse;
import com.chinatelecom.gs.engine.kms.search.SearchService;
import com.chinatelecom.gs.engine.kms.service.HighlightService;
import com.chinatelecom.gs.engine.kms.service.KmsSearchMqService;
import com.chinatelecom.gs.engine.kms.service.KnowledgeBaseService;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelecom.gs.workflow.core.workflow.core.utils.LogUtils;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.Result;
import com.chinatelelcom.gs.engine.sdk.common.enums.AppSourceType;
import com.chinatelelcom.gs.engine.sdk.common.enums.RequestSourceType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;


@RestController
@Slf4j
@Tag(name = "检索接口")
@RequestMapping({KmsApis.KMS_API + KmsApis.SEARCH, KmsApis.RPC + KmsApis.SEARCH, KmsApis.OPENAPI + KmsApis.SEARCH})
public class SearchController {

    @Resource
    private SearchService searchService;

    @Resource
    private HighlightService highlightService;

    @Resource
    private BaseConfigAppService baseConfigAppService;

    @Resource
    private KnowledgeBaseService knowledgeBaseService;

    @Resource
    private KmsSearchMqService kmsSearchMqService;

    @Resource
    private GsGlobalConfig gsGlobalConfig;

    @Resource
    private LogMqProducer logMqProducer;

    @Operation(summary = "搜索接口")
    @PlatformRestApi(name = "搜索接口", groupName = "检索管理")
    @AuditLog(businessType = "检索管理", operType = "搜索接口", operDesc = "搜索接口", objId="#searchParam.query")
    @DebugLog(operation = "搜索接口")
    @PostMapping("/v1")
    @PermissionTag(code = {KsMenuConfig.KNWL_SEARCH, KsMenuConfig.KNWL_SEARCH_1})
    public SearchResp<? extends BaseItem> search(@Validated @RequestBody SearchParam searchParam) {
        try {
            setAppSourceType(searchParam);
            processParam(searchParam);
            searchParam.setSceneType(SceneType.search);
            // 搜索避免召回过多不相关的数据,这个限制分数为0.1以上
            Double searchMinThreshold = gsGlobalConfig.getSearch().getSearchMinThreshold();
            if (searchMinThreshold != null) {
                searchParam.getFilter().setThreshold(searchMinThreshold);
            }
            paramCheck(searchParam);
            return searchService.searchSync(searchParam);
        } catch (Exception e) {
            log.error("搜索接口异常", e);
            throw e;
        }finally {
            //搜索埋点
            kmsSearchMqService.sendSearchMq(searchParam);
        }
    }

    @Operation(summary = "分片搜索接口")
    @PlatformRestApi(name = "分片搜索接口", groupName = "检索管理")
    @AuditLog(businessType = "检索管理", operType = "分片搜索接口", operDesc = "分片搜索接口", objId="#searchParam.query")
    @DebugLog(operation = "分片搜索接口")
    @PostMapping({KmsApis.CHUNKS + "/v1"})
    public SearchResp<? extends BaseItem> searchChunks(@Validated @RequestBody SearchParam searchParam) {
        SearchResp<? extends BaseItem> resp = null;
        LocalDateTime startTime = LocalDateTime.now();
        LogStatusEnum statusEnum = LogStatusEnum.SUCCESS;
        try {
            setAppSourceType(searchParam);
            processParam(searchParam);
            searchParam.setSceneType(SceneType.chunk);
            paramCheck(searchParam);
            resp = searchService.searchChunkSync(searchParam);
            return resp;
        } catch (Exception e) {
            log.error("分片搜索异常", e);
            statusEnum = LogStatusEnum.FAILED;
            throw e;
        } finally {
            String messageId = StringUtils.isNotBlank(LocalContext.getMessageId()) ? LocalContext.getMessageId() : RequestContext.getMessageId();
            if (StringUtils.isNotBlank(messageId)) {
                LogMessage logMessage = LogUtils.buildCommonLog(LogTypeEnum.KS_CHUNK_RECALL, JsonUtils.toJsonString(searchParam), JsonUtils.toJsonString(resp), statusEnum, startTime, LocalDateTime.now(), "");
                logMessage.setName("知识库分片召回");
                logMqProducer.sendLogMessage(logMessage);
            }
        }
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "高亮接口")
    @PlatformRestApi(name = "高亮接口", groupName = "检索管理")
    @AuditLog(businessType = "检索管理", operType = "高亮接口", operDesc = "高亮接口", objId="#param.chunkId")
    @PostMapping(KmsApis.HIGHLIGHT_API)
    public Result<HighlightVO> highlight(@Validated @RequestBody HighlightParam param) {
        return Result.success(highlightService.highlight(param));
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "获取分片")
    @PlatformRestApi(name = "获取分片接口", groupName = "检索管理")
    @AuditLog(businessType = "检索管理", operType = "获取分片接口", operDesc = "获取分片接口", objId="#chunkQueryParam.knowledgeCode")
    @PostMapping(KmsApis.PAGE_CHUNK_API)
    public Result<PageImpl<ChunkDataVO>> pageChunk(@RequestBody @Valid ChunkQueryParam chunkQueryParam) {
        PageImpl<ChunkDataVO> chunkDataVOPage = (PageImpl<ChunkDataVO>) searchService.pageChunk(chunkQueryParam);
        return Result.success(chunkDataVOPage);
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "联想搜索接口")
    @PlatformRestApi(name = "联想搜索接口", groupName = "检索管理")
    @AuditLog(businessType = "检索管理", operType = "联想搜索接口", operDesc = "联想搜索接口", objId="#searchParam.query")
    @PostMapping({KmsApis.SUGGEST + "/v1"})
    @PermissionTag(code = {KsMenuConfig.KNWL_SEARCH, KsMenuConfig.KNWL_SEARCH_1})
    public Result<SuggestVO> searchSuggest(@Validated @RequestBody SearchParam searchParam) {
        setAppSourceType(searchParam);
        return Result.success(searchService.searchSuggestSync(searchParam));
    }

    private void setAppSourceType(SearchParam searchParam) {
        SearchParam.Filter filter = searchParam.getFilter();
        if (filter != null) {
            if (filter.getSourceSystem() == null) {
                AppSourceType appSourceType = RequestContext.getAppSourceType();
                BizAssert.notNull(appSourceType, "AA054", "无法获取操作来源信息");
                filter.setSourceSystem(appSourceType);
            }
            if (!CollectionUtils.isEmpty(filter.getKnowledgeFilters()) ) {
                for (SearchParam.KnowledgeFilter knowledgeFilter : filter.getKnowledgeFilters()) {
                    if(StringUtils.isBlank(knowledgeFilter.getKnowledgeBaseCode())){
                        BizAssert.isEmpty(knowledgeFilter.getKnowledgeCodes(), "B0005", "需要有知识库Code");
                        BizAssert.isEmpty(knowledgeFilter.getCatalogCodes(), "B0005", "需要有知识库Code");
                    }
                }
            }

        } else {
            filter = new SearchParam.Filter();
            searchParam.setFilter(filter);
            AppSourceType appSourceType = RequestContext.getAppSourceType();
            BizAssert.notNull(appSourceType, "AA054", "无法获取操作来源信息");
            filter.setSourceSystem(appSourceType);
        }
    }

    /**
     * 处理一下默认参数逻辑
     * @param searchParam
     * @return
     */
    private void processParam(SearchParam searchParam) {
        //获取开关配置
        PublishConfig config = baseConfigAppService.getConfigOrDefault(ConfigConvert.PUBLISH_CONFIG, RequestContext.getTenantId());
        if (!config.getPublishSwitch() && Objects.equals(EnvType.PROD, searchParam.getEnv())) {
            // 发布功能关闭，线上线下检索都强制走线下
            log.info("发布功能已关闭，关闭线上索引检索，强制走线下检索");
            searchParam.setEnv(EnvType.TEST);
        }
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "命中测试接口")
    @PlatformRestApi(name = "知识库命中测试", groupName = "知识库管理")
    @AuditLog(businessType = "知识库管理", operType = "知识库命中测试", operDesc = "知识库命中测试", objId="#searchParam.query")
    @DebugLog(operation = "知识库命中测试")
    @PostMapping(KmsApis.HIT_TEST + "/v1")
    @PermissionTag(code = {KsMenuConfig.KNOWLEDGE_BASE, KsMenuConfig.KNOWLEDGE_BASE_1, MenuConfig.WORD_KNOWLEDGE, MenuConfig.QA_KNOWLEDGE})
    public SearchResp<? extends BaseItem> hitTest(@Validated @RequestBody SearchParam searchParam) {
        try {
            // 验证知识库参数
            String knowledgeBaseCode = getSearchParamKnowledgeBaseCode(searchParam);
            BizAssert.notNull(knowledgeBaseCode, "AA001", "知识库编码不能为空");
           //保存命中测试的结果
            searchParam.setIntermediateTypes(Collections.singletonList(IntermediateTypeEnum.HIT_TEST));
            setAppSourceType(searchParam);
            processParam(searchParam);
            searchParam.setSceneType(SceneType.chunk);
            paramCheck(searchParam);
            SearchResp<? extends BaseItem> searchResp = searchService.searchChunkSync(searchParam);

            // 设置视图分数
            List<? extends SdkSearchResponse.SdkRetrieveItem<? extends BaseItem>> items = searchResp.getData().getItems();
            if (CollectionUtils.isNotEmpty(items)) {
                items.stream().forEach(item -> {
                    BaseItem.MetaData metaData = item.getSource().getMetaData();
                    metaData.setRankScore(Double.parseDouble(String.format("%.2f", metaData.getRankScore())));
                });
            }
            return searchResp;
        } catch (Exception e) {
            log.error("知识库命中测试异常", e);
            throw e;
        }
    }

    private void paramCheck(SearchParam searchParam) {
        if (searchParam.getFilter() != null && !searchParam.getFilter().isCheckRole() && Objects.equals(RequestContext.getRequestSourceType(), RequestSourceType.WEB)) {
            // web请求必须校验权限，避免越权
            BizAssert.throwBizException("AA108", "web请求必须校验权限");
        }

        SearchOrderType orderBy = searchParam.getOrderBy();
        Boolean isRank = searchParam.getFilter().getIsRank();
        if (!Objects.equals(orderBy, SearchOrderType.DEFAULT) && Objects.equals(isRank, true)) {
            BizAssert.throwBizException("AA114", "开启精排不能指定其他排序方式");
        }

    }

    private String getSearchParamKnowledgeBaseCode(SearchParam searchParam) {
        return Optional.ofNullable(searchParam)
                .map(SearchParam::getFilter)
                .map(SearchParam.Filter::getKnowledgeFilters)
                .filter(filters -> !filters.isEmpty())
                .map(filters -> filters.get(0))
                .map(SearchParam.KnowledgeFilter::getKnowledgeBaseCode)
                .orElse(null);
    }

    @HideFromApiTypes(ApiType.OPENAPI)
    @Operation(summary = "更新同义词分析器")
    @PlatformRestApi(name = "更新同义词分析器", groupName = "检索管理")
    @AuditLog(businessType = "检索管理", operType = "更新同义词分析器", operDesc = "更新同义词分析器", objId = "#codeParam")
    @DebugLog(operation = "更新同义词分析器")
    @PostMapping(KmsApis.UPDATE_SYNONYM_ANALYZER)
    public Result<Void> updateSynonymAnalyzer(@Validated @RequestBody CodeEmptyParam codeParam) {
        Collection<String> codes = codeParam.getCodes();
        Collection<String> tenantIds = codeParam.getTenantIds();
        try {
            // 处理知识库编码，筛选出需要更新的知识库列表
            if (!CollectionUtils.isEmpty(codes) && !CollectionUtils.isEmpty(tenantIds)) {
                //交集
                Collection<String> tenantCodes = knowledgeBaseService.findCodesByTenantIds(tenantIds);
                codes = CollectionUtils.intersection(codes, tenantCodes);
            } else if (CollectionUtils.isEmpty(codes)) {
                // 如果知识库编码为空，根据租户ID获取知识库
                if (CollectionUtils.isEmpty(tenantIds)) {
                    // 如果租户ID为空，获取所有知识库
                    codes = knowledgeBaseService.findAllCodesWithoutTenant();
                } else {
                    // 如果租户ID不为空，获取指定租户的知识库
                    codes = knowledgeBaseService.findCodesByTenantIds(tenantIds);
                }
            }

            // 更新每个知识库的同义词分析器
            for (String code : codes) {
                searchService.updateSynonymAnalyzer(code);
            }
            return Result.success();
        } catch (Exception e) {
            log.error("更新同义词分析器失败", e);
            throw e;
        }
    }

}
