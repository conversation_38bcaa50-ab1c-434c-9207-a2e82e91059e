package com.chinatelecom.gs.engine.core.model.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.chinatelecom.ai.search.client.AiSearchClient;
import com.chinatelecom.ai.search.req.AnalyzerCreateRequest;
import com.chinatelecom.ai.search.resp.StringResponse;
import com.chinatelecom.ai.search.sdk.enums.AnalyzerType;
import com.chinatelecom.ai.search.sdk.enums.DiscoveryType;
import com.chinatelecom.ai.search.sdk.enums.VectorSimilarity;
import com.chinatelecom.ai.search.sdk.enums.VectorType;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.utils.IdGenerator;
import com.chinatelecom.gs.engine.core.model.converter.RobotConfigConverter;
import com.chinatelecom.gs.engine.core.model.entity.dto.*;
import com.chinatelecom.gs.engine.core.model.entity.param.*;
import com.chinatelecom.gs.engine.core.model.entity.po.AiIntentPO;
import com.chinatelecom.gs.engine.core.model.entity.po.AiModelPO;
import com.chinatelecom.gs.engine.core.model.entity.vo.QueryByModelTypeVO;
import com.chinatelecom.gs.engine.core.model.entity.vo.UploadResultVO;
import com.chinatelecom.gs.engine.core.model.enums.AiModelStateEnum;
import com.chinatelecom.gs.engine.core.model.enums.ModelConfigTypeEnum;
import com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum;
import com.chinatelecom.gs.engine.core.model.listener.AiModelInfoListener;
import com.chinatelecom.gs.engine.core.model.listener.AiModelIntentListener;
import com.chinatelecom.gs.engine.core.model.respository.AiIntentRepository;
import com.chinatelecom.gs.engine.core.model.respository.AiModelRepository;
import com.chinatelecom.gs.engine.core.model.respository.respository.AgentIntentConfigRepository;
import com.chinatelecom.gs.engine.core.model.service.AiModelAppService;
import com.chinatelecom.gs.engine.core.model.service.AiModelService;
import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.ModelProviderEnum;
import com.chinatelecom.gs.engine.core.sdk.enums.ModelDataFormatEnum;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelIntentParam;
import com.chinatelecom.gs.engine.core.sdk.vo.ModelPageListParam;
import com.chinatelelcom.gs.engine.sdk.common.PageImpl;
import com.chinatelelcom.gs.engine.sdk.common.exception.BizException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.chinatelecom.gs.engine.core.model.enums.ModelTypeEnum.CLASSIFIER;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
@RefreshScope
public class AiModelAppServiceImpl implements AiModelAppService {
    /*** 上传文件最大5M */
    private static final long MAX_FILE_SIZE = (long) 5 * 1024 * 1024;
    /*** 上传文件类型 */
    private static final List<String> ALLOW_FILE_TYPE = Collections.singletonList("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

    @Resource
    private AiModelRepository aiModelRepository;

    @Resource
    private AiModelService aiModelService;

    @Resource
    private AiIntentRepository aiIntentRepository;

    @Resource
    @Lazy
    private AiSearchClient aiSearchClient;

    @Resource
    private AgentIntentConfigRepository agentIntentConfigRepository;

    @Value("${platform.super.tenant:SuperAis}")
    private String superTenant;

    /**
     * 分页查询模型列表
     *
     * @param param ModelPageParam
     * @return PageImpl<ModelPageListParam>
     */
    @Override
    public PageImpl<ModelPageListParam> aiModelList(ModelPageParam param) {
        RequestInfo requestInfo = RequestContext.get();
        param.setAppId(requestInfo.getAppCode());
        if (CollUtil.isNotEmpty(param.getModelCodeList())) {
            param.setIsQueryAll(false);
        } else {
            param.setIsQueryAll(true);
        }
        PageImpl<ModelPageListParam> modelPage = aiModelRepository.pageList(param);
        if ((ModelTypeEnum.CLASSIFIER.getCode().equals(param.getModelType()))) {
            for (ModelPageListParam model : modelPage.getRecords()) {
                fetchIntent(model);
            }
        }
        return modelPage;
    }

    /**
     * 保存/编辑模型
     *
     * @param param ModelPageListParam
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(ModelEditParam param) {
        RequestInfo requestInfo = RequestContext.get();
        param.setAppId(requestInfo.getAppCode());
        // 校验模型名称
        checkModelName(param);
        // 校验意图是否重复,先做模型维度验重，有需要再做机器人维度的
        checkModelIntent(param);
        checkModelUrl(param);
        // 校验阈值
        checkThreshold(param);
        checkLlmParam(param);
        LocalDateTime now = LocalDateTime.now();
        AiModelDTO aiModelDTO = initConvertAiModelDTO(param, now);
        if (superTenant.equals(RequestContext.getTenantId())) {
            aiModelDTO.setGlobalFlag(1);
        }
        if (CharSequenceUtil.isNotBlank(param.getModelCode())) {
            // 编辑
            editAiModel(param, aiModelDTO);
            return true;
        }
        saveModel(param, aiModelDTO, requestInfo, now);
        return true;
    }

    /**
     * 校验大模型参数
     *
     * @param param ModelEditParam
     */
    private void checkLlmParam(ModelEditParam param) {
        if (ModelTypeEnum.LLM.getCode().equals(param.getModelType()) && CharSequenceUtil.isBlank(param.getModelProvider())) {
            throw new BizException("A0031", new String[]{"模型提供方不能为空"}, "模型提供方不能为空");
        }

    }

    /**
     * 校验分类模型参数
     *
     * @param param ModelEditParam
     */
    private void checkThreshold(ModelEditParam param) {
        if (CLASSIFIER.getCode().equals(param.getModelType())) {
            //  校验分类模型阈值
            double threshold = param.getThreshold() == null ? 0.85 : param.getThreshold();
            if (threshold > 1 || threshold <= 0) {
                throw new BizException("A0032", "分类模型阈值错误，请保持在0-1区间");
            }
        }
    }

    /**
     * 校验模型名称
     *
     * @param param ModelEditParam
     */
    private void checkModelName(ModelEditParam param) {
        if (param.getModelName().length() > 120) {
            throw new BizException("A0033", "模型名称【" + param.getModelName() + "不能超过120个字符");
        }
    }

    /**
     * 转换参数
     *
     * @param param ModelEditParam
     * @param now   LocalDateTime
     * @return AiModelDTO
     */
    private AiModelDTO initConvertAiModelDTO(ModelEditParam param, LocalDateTime now) {
        RequestInfo requestInfo = RequestContext.get();
        AiModelDTO aiModelDTO = new AiModelDTO();
        BeanUtil.copyProperties(param, aiModelDTO, "dataFormat");
        if (CharSequenceUtil.isNotBlank(param.getDataFormat())) {
            ModelDataFormatEnum dataFormatEnum = ModelDataFormatEnum.valueOf(param.getDataFormat());
            aiModelDTO.setDataFormat(dataFormatEnum);
        }
        if (Objects.nonNull(param.getExtraDataVO())) {
            aiModelDTO.setExtraData(JSON.toJSONString(param.getExtraDataVO()));
        }
        aiModelDTO.setDataFormat(CharSequenceUtil.isNotBlank(param.getDataFormat()) ? ModelDataFormatEnum.valueOf(param.getDataFormat()) : null);
        aiModelDTO.setIntentNumber(CollUtil.isEmpty(param.getIntentList()) ? 0 : param.getIntentList().size());
        aiModelDTO.setUpdateId(requestInfo.getUserId());
        aiModelDTO.setUpdateName(requestInfo.getUserName());
        aiModelDTO.setUpdateTime(now);
        return aiModelDTO;
    }

    /**
     * 校验模型url
     *
     * @param param ModelEditParam
     */
    private void checkModelUrl(ModelEditParam param) {
        if (!param.getExternalModelUrl().matches("^https?://.*$")) {
            throw new BizException("A0034", "模型地址以【http://】或者【https://】开始");
        }
    }

    /**
     * 校验意图是否重复
     *
     * @param param ModelEditParam
     */
    private void checkModelIntent(ModelEditParam param) {
        List<ModelIntentParam> intentList = param.getIntentList();
        if (CollUtil.isNotEmpty(intentList)) {
            HashMap<String, String> intentNameMap = new HashMap<>();
            HashMap<String, String> intentCodeMap = new HashMap<>();
            for (ModelIntentParam modelIntentParam : intentList) {
                if (intentNameMap.get(modelIntentParam.getIntentName()) != null) {
                    throw new BizException("A0035", new String[]{modelIntentParam.getIntentName()}, "意图名称重复【" + modelIntentParam.getIntentName() + "】");
                }
                intentNameMap.put(modelIntentParam.getIntentName(), modelIntentParam.getIntentName());
                if (intentCodeMap.get(modelIntentParam.getIntentCode()) != null) {
                    throw new BizException("A0036", new String[]{modelIntentParam.getIntentCode()}, "意图code重复【" + modelIntentParam.getIntentCode() + "】");
                }
                intentCodeMap.put(modelIntentParam.getIntentCode(), modelIntentParam.getIntentCode());
            }
        }
    }

    public List<NameAndCodeParam> getModelProvider() {
        return Arrays.stream(ModelProviderEnum.values()).map(o -> {
            NameAndCodeParam nameAndCodeParam = new NameAndCodeParam();
            nameAndCodeParam.setCode(o.getCode());
            nameAndCodeParam.setName(o.getDesc());
            return nameAndCodeParam;
        }).collect(Collectors.toList());
    }

    /**
     * 保存模型
     *
     * @param param       ModelEditParam
     * @param aiModelDTO  AiModelDTO
     * @param requestInfo requestInfo
     * @param now         LocalDateTime
     */
    private String saveModel(ModelEditParam param, AiModelDTO aiModelDTO, RequestInfo requestInfo, LocalDateTime now) {
        // 校验名称是否重复
        checkModelName(param, false);
        aiModelDTO.setCreateId(requestInfo.getUserId());
        aiModelDTO.setCreateName(requestInfo.getUserName());
        aiModelDTO.setCreateTime(now);
        // 创建
        String code = IdGenerator.getId("model_");
        aiModelDTO.setModelCode(code);
        aiModelDTO.setModelState(AiModelStateEnum.VALID);
        param.setModelCode(code);
        String vectorConfig = null;
        switch (ModelTypeEnum.valueOf(param.getModelType())) {
            case CLASSIFIER:
                // 分类模型保存意图
                saveIntent(param);
                break;
            case EMBEDDING:
                // 创建向量分析器
                vectorConfig = createVector(param, code);
                // 处理模型配置
                processModelConfig(param, aiModelDTO);
                break;
            default:
                break;
        }
        aiModelDTO.setExternalModelConfig(vectorConfig);
        aiModelRepository.save(aiModelDTO);
        return code;
    }

    /**
     * 修改模型
     *
     * @param param      ModelEditParam
     * @param aiModelDTO AiModelDTO
     */
    private void editAiModel(ModelEditParam param, AiModelDTO aiModelDTO) {
        // 校验名称是否重复
        checkModelName(param, true);
        ModelPageListParam modelPageListParam = get(param.getModelCode(), true);
        if (modelPageListParam == null || (!RequestContext.get().getIsAdmin() && modelPageListParam.getIsGlobal())) {
            throw new BizException("A0037", "模型不存在");
        }
        if (ModelTypeEnum.EMBEDDING.equals(ModelTypeEnum.valueOf(param.getModelType()))) {
            // 创建向量分析器
            String vectorConfig = createVector(param, param.getModelCode());
            aiModelDTO.setExternalModelConfig(vectorConfig);
            // 处理模型配置
            processModelConfig(param, aiModelDTO);
        }
        aiModelRepository.updateModel(aiModelDTO);
        if (CLASSIFIER.equals(ModelTypeEnum.valueOf(param.getModelType()))) {
            // 分类模型保存意图
            editIntent(param, modelPageListParam);
        }
    }

    /**
     * 校验模型名称是否重复
     *
     * @param param  ModelEditParam
     * @param isEdit boolean
     */
    private void checkModelName(ModelEditParam param, boolean isEdit) {
        List<AiModelPO> list = aiModelService.checkModelName(param.getModelName(), param.getModelCode(), param.getAppId(), isEdit);
        if (CollUtil.isNotEmpty(list)) {
            throw new BizException("A0038", new String[]{param.getModelName()}, "【" + param.getModelName() + "】模型名已存在");
        }
    }

    /**
     * 创建向量分析器
     */
    private String createVector(ModelEditParam param, String modelCode) {
        // 创建向量分析器
        return buildAndSaveVector(param, modelCode);
    }

    /**
     * 构建并创建向量分析器
     *
     * @param param ModelEditParam
     * @return String
     */
    private String buildAndSaveVector(ModelEditParam param, String modelCode) {
        ModelConfigDTO modelTemplate;
        try {
            modelTemplate = JSONUtil.toBean(param.getModelConfig(), ModelConfigDTO.class);
        } catch (Exception e) {
            log.error("创建模型参数错误", e);
            throw new BizException("A0065", "参数校验失败");
        }
        AnalyzerCreateRequest analyzerCreateRequest = buildVectorParam(param, modelCode, modelTemplate, true);
        doCreateVector(param, analyzerCreateRequest);
        VectorConfigParam vectorConfigParam = new VectorConfigParam();
        BeanUtil.copyProperties(analyzerCreateRequest, vectorConfigParam);
        vectorConfigParam.setVectorType(ModelConfigTypeEnum.USERREQUEST.getCode());
        List<VectorConfigParam> analyzerCreateRequestList = Lists.newArrayList(vectorConfigParam);
        // 创建离线文档向量分析器
        if (CharSequenceUtil.isNotBlank(param.getOfflineDocumentsModelConfig())) {
            ModelConfigDTO modelConfigDTO = JSON.parseObject(param.getOfflineDocumentsModelConfig(), ModelConfigDTO.class);
            AnalyzerCreateRequest offlineDocumentsModelConfigRequest = buildVectorParam(param, modelCode, modelConfigDTO, false);
            doCreateVector(param, offlineDocumentsModelConfigRequest);
            VectorConfigParam vectorConfigDocParam = new VectorConfigParam();
            BeanUtil.copyProperties(analyzerCreateRequest, vectorConfigDocParam);
            vectorConfigDocParam.setVectorType(ModelConfigTypeEnum.OFFLINEDOCUMENTSMODELCONFIG.getCode());
            analyzerCreateRequestList.add(vectorConfigDocParam);
            JSONUtil.parseObj(offlineDocumentsModelConfigRequest).set("vectorType", ModelConfigTypeEnum.OFFLINEDOCUMENTSMODELCONFIG.getCode());
        }
        return JSON.toJSONString(analyzerCreateRequestList);
    }

    /**
     * 创建向量分析起
     *
     * @param param                 ModelEditParam
     * @param analyzerCreateRequest AnalyzerCreateRequest
     */
    private void doCreateVector(ModelEditParam param, AnalyzerCreateRequest analyzerCreateRequest) {
        try {
            log.info("请求参数{}，创建向量分析参数{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(analyzerCreateRequest));
            StringResponse createVectorResponse = aiSearchClient.analyzer().create(analyzerCreateRequest);
            log.info("创建向量分析器返回结果:{}", createVectorResponse);
            JSONUtil.toJsonStr(analyzerCreateRequest);
        } catch (IOException e) {
            log.error("创建向量分析器失败", e);
            throw new BizException("A0039", "创建向量分析器失败");
        }
    }

    /**
     * 构建参数
     *
     * @param param         ModelEditParam
     * @param modelCode     String
     * @param modelTemplate ModelConfigDTO
     * @return AnalyzerCreateRequest
     */
    private AnalyzerCreateRequest buildVectorParam(ModelEditParam param, String modelCode, ModelConfigDTO
            modelTemplate, boolean isUserRequest) {
        URL url = URLUtil.url(param.getExternalModelUrl());
        String host = url.getHost();
        String uri = url.getPath();
        int port = url.getPort();
        String typeName = isUserRequest ? ModelConfigTypeEnum.USERREQUEST.getCode() : ModelConfigTypeEnum.OFFLINEDOCUMENTSMODELCONFIG.getCode();
        AnalyzerCreateRequest.VectorConfig vectorConfig = AnalyzerCreateRequest.VectorConfig.builder()
                .dims(param.getDataDimension())
                .index(true)
                .similarity(VectorSimilarity.valueOf(modelTemplate.getSimilarity()))
                .build();
        ModelDataFormatEnum modelDataFormatEnum = CharSequenceUtil.isNotBlank(param.getDataFormat()) ? ModelDataFormatEnum.valueOf(param.getDataFormat()) : null;
        AnalyzerCreateRequest.ModelHttpTemplate template = AnalyzerCreateRequest.ModelHttpTemplate.builder()
                .body(modelTemplate.getBody())
                .uri(uri)
                .vectorField(modelTemplate.getVectorField())
                .vectorFieldParseExp(modelTemplate.getVectorFieldParseExp())
                .method(modelTemplate.getMethod())
                .header(modelTemplate.getHeader())
                .build();
        AnalyzerCreateRequest.VectorAnalyzer vectorAnalyzer = AnalyzerCreateRequest.VectorAnalyzer.builder()
                .vectorType(modelDataFormatEnum != null ? VectorType.valueOf(modelDataFormatEnum.getCode()) : null)
                .discoveryType(DiscoveryType.simple)
                .address(host + ":" + port)
                .nacosServerName("simple")
                .reqTemplate(template)
                .build();
        AnalyzerCreateRequest.VectorAnalyzerParam analyzerParam = AnalyzerCreateRequest.VectorAnalyzerParam.builder()
                .vectorConfig(vectorConfig)
                .analyzerInfo(vectorAnalyzer)
                .build();
        return AnalyzerCreateRequest.builder()
                .name(typeName + "(" + modelCode + ")")
                .type(AnalyzerType.VECTOR)
                .vectorParam(analyzerParam)
                .build();
    }

    /**
     * 批量保存意图
     *
     * @param param ModelEditParam
     */
    private void saveIntent(ModelEditParam param) {
        if (CollUtil.isNotEmpty(param.getIntentList())) {
            List<AiIntentDTO> intentDTOs = param.getIntentList().stream().map(o -> {
                // 判断是新增还是删除
                AiIntentDTO aiIntentDTO = new AiIntentDTO();
                BeanUtil.copyProperties(o, aiIntentDTO);
                aiIntentDTO.setIntentId(IdGenerator.id());
                aiIntentDTO.setAppId(param.getAppId());
                aiIntentDTO.setModelCode(param.getModelCode());
                return aiIntentDTO;
            }).collect(Collectors.toList());
            aiIntentRepository.saveBatch(intentDTOs);
        }
    }

    /**
     * 批量编辑意图
     *
     * @param param ModelEditParam
     */
    private void editIntent(ModelEditParam param, ModelPageListParam modelPageListParam) {
        if (CollUtil.isEmpty(param.getIntentList())) {
            aiModelRepository.remove(param.getAppId(), param.getModelCode());
            return;
        }
        List<AiIntentDTO> saveList = new ArrayList<>();
        if (CollUtil.isEmpty(modelPageListParam.getIntentList())) {
            // 数据库中该模型没有意图，直接保存
            saveIntent(param);
            return;
        }
        Map<String, ModelIntentParam> intentMap = modelPageListParam.getIntentList().stream().collect(Collectors.toMap(ModelIntentParam::getIntentId, Function.identity()));
        for (ModelIntentParam modelIntentParam : param.getIntentList()) {
            AiIntentDTO aiIntentDTO = new AiIntentDTO();
            BeanUtil.copyProperties(modelIntentParam, aiIntentDTO);
            aiIntentDTO.setAppId(param.getAppId());
            aiIntentDTO.setModelCode(param.getModelCode());
            if (modelIntentParam.getIntentId() != null) {
                // 排除已存在intent
                intentMap.remove(modelIntentParam.getIntentId());
            } else {
                // 保存list
                aiIntentDTO.setIntentId(IdGenerator.id());
                saveList.add(aiIntentDTO);
            }
        }
        if (CollUtil.isNotEmpty(saveList)) {
            // 新增知识
            aiIntentRepository.saveBatch(saveList);
        }
        if (CollUtil.isNotEmpty(intentMap)) {
            // 删除的知识
            List<String> intentIdList = new ArrayList<>();
            intentMap.forEach((k, v) -> intentIdList.add(k));
            aiIntentRepository.remove(intentIdList, param.getAppId(), param.getModelCode());
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(String modelCode) {
        RequestInfo requestInfo = RequestContext.get();
        String appId = requestInfo.getAppCode();
        ModelPageListParam modelPageListParam = get(modelCode, true);
        if (modelPageListParam == null) {
            return false;
        }

        // 检查模型是否在意图高级设置中被使用
        if (agentIntentConfigRepository.existsByModelCode(modelCode)) {
            String message="意图高级设置";
            throw new BizException("A0077", new String[]{message}, message);
        }

        aiModelRepository.remove(appId, modelCode);
        if (CLASSIFIER.equals(ModelTypeEnum.valueOf(modelPageListParam.getModelType()))) {
            aiIntentRepository.remove(modelCode);
        }
        return true;
    }

    @Override
    public List<ModelTypeParam> getModelType() {
        return Arrays.stream(ModelTypeEnum.values()).map(o -> {
            ModelTypeParam modelTypeParam = new ModelTypeParam();
            modelTypeParam.setModelTypeCode(o.getCode());
            modelTypeParam.setModelTypeName(o.getDesc());
            return modelTypeParam;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ModelTypeParam> getModelDataType() {
        return Arrays.stream(ModelDataFormatEnum.values()).map(o -> {
            ModelTypeParam modelTypeParam = new ModelTypeParam();
            modelTypeParam.setModelTypeCode(o.getCode());
            modelTypeParam.setModelTypeName(o.getDesc());
            return modelTypeParam;
        }).collect(Collectors.toList());
    }

    @Override
    public ModelPageListParam get(String modelCode, boolean isEdit) {
        ModelPageListParam model;
        if (isEdit) {
            model = getModelAndCheckAuth(modelCode);
        } else {
            model = queryModel(modelCode);
        }
        if (model == null) {
            return null;
        }
        ModelPageListParam param = new ModelPageListParam();
        BeanUtil.copyProperties(model, param);
        if (ModelTypeEnum.EMBEDDING.equals(ModelTypeEnum.valueOf(model.getModelType()))) {
            String modelConfig = param.getModelConfig();
            List<ModelConfigDTO> modelConfigDTOS = null;
            try {
                modelConfigDTOS = JSONUtil.toList(modelConfig, ModelConfigDTO.class);
            } catch (Exception e) {
                param.setModelConfig(modelConfig);
            }
            if (CollUtil.isNotEmpty(modelConfigDTOS)) {
                for (ModelConfigDTO modelConfigDTO : modelConfigDTOS) {
                    switch (ModelConfigTypeEnum.valueOf(modelConfigDTO.getVectorType())) {
                        case USERREQUEST:
                            param.setModelConfig(JSON.toJSONString(modelConfigDTO));
                            break;
                        case OFFLINEDOCUMENTSMODELCONFIG:
                            param.setOfflineDocumentsModelConfig(JSON.toJSONString(modelConfigDTO));
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        fetchIntent(param);
        return param;
    }

    private void fetchIntent(ModelPageListParam param) {
        List<AiIntentPO> intentPOList = aiIntentRepository.queryByModelCode(param.getModelCode());
        if (CLASSIFIER.equals(ModelTypeEnum.valueOf(param.getModelType()))) {
            param.setIntentList(intentPOList.stream().map(o -> {
                ModelIntentParam modelIntentParam = new ModelIntentParam();
                BeanUtil.copyProperties(o, modelIntentParam);
                return modelIntentParam;
            }).collect(Collectors.toList()));
        }
    }

    /**
     * 校验模型权限
     *
     * @param modelCode String
     * @return ModelPageListParam
     */
    private ModelPageListParam getModelAndCheckAuth(String modelCode) {
        RequestInfo requestInfo = RequestContext.get();
        String appId = requestInfo.getAppCode();
        return aiModelRepository.getByModelCode(appId, modelCode);
    }

    /**
     * 校验模型权限
     *
     * @param modelCode String
     * @return ModelPageListParam
     */
    private ModelPageListParam queryModel(String modelCode) {
        RequestInfo requestInfo = RequestContext.get();
        String appId = requestInfo.getAppCode();
        ModelPageListParam model = aiModelRepository.getByModelCode(appId, modelCode);
        if (model == null) {
            return null;
        }
        if (Boolean.TRUE.equals(model.getIsGlobal())) {
            return model;
        }
        return model;
    }

    @Override
    public PageImpl<ModelIntentParam> queryIntentPage(ModelIntentPageParam param) {
        Page<AiIntentPO> page = aiIntentRepository.queryPageByModelCode(param.getModelCode(), Math.toIntExact(param.getPageNum()),
                Math.toIntExact(param.getPageSize()), param.getName());
        PageImpl<ModelIntentParam> pageResult = new PageImpl<>();
        pageResult.setCurrent(page.getCurrent());
        pageResult.setSize(page.getSize());
        pageResult.setTotal(page.getTotal());
        pageResult.setPages(page.getPages());
        pageResult.setRecords(CollUtil.isEmpty(page.getRecords()) ? Lists.newArrayList() : page.getRecords().stream().map(o -> {
            ModelIntentParam modelIntentParam = new ModelIntentParam();
            modelIntentParam.setIntentCode(o.getIntentCode());
            modelIntentParam.setIntentName(o.getIntentName());
            return modelIntentParam;
        }).collect(Collectors.toList()));
        return pageResult;
    }

    @Override
    public Boolean createIntent(String modelCode, String key, String intentJson, String userId) {
        RequestInfo requestInfo = RequestContext.get();
        String appId = requestInfo.getAppCode();
        if (StringUtils.isNotBlank(key)) {
            JSONArray jsonArray = com.alibaba.fastjson.JSON.parseArray(intentJson);
            List<AiIntentDTO> aiIntentDTOS = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                Object oj = jsonArray.get(i);
                String ojstr = oj.toString();
                JSONObject jsonObject = com.alibaba.fastjson.JSON.parseObject(ojstr);
                String name = jsonObject.getString("name");
                String code = jsonObject.getString("code");
                AiIntentDTO aiIntentDTO = new AiIntentDTO();
                aiIntentDTO.setAppId(appId);
                aiIntentDTO.setModelCode(modelCode);
                aiIntentDTO.setIntentCode(code);
                aiIntentDTO.setIntentName(name);
                aiIntentDTO.setIntentId(IdGenerator.getId(""));
                aiIntentDTO.setTenantId(0L);
                aiIntentDTO.setCreateId(userId);
                aiIntentDTO.setCreateName("root");
                aiIntentDTO.setUpdateId(userId);
                aiIntentDTO.setUpdateName("root");
                aiIntentDTOS.add(aiIntentDTO);
                if (i % 100 == 0) {
                    aiIntentRepository.saveBatch(aiIntentDTOS);
                    aiIntentDTOS.clear();
                }
            }
            aiIntentRepository.saveBatch(aiIntentDTOS);
        }
        return true;
    }

    @Override
    public UploadResultVO upload(MultipartFile file) throws Exception {
        RequestInfo requestInfo = RequestContext.get();
        String appId = requestInfo.getAppCode();
        //校验文件
        checkFileUpload(file);

        InputStream is = file.getInputStream();
        ExcelReader excelReader = EasyExcelFactory.read(is).build();
        List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();

        //1.多sheet读取，每个sheet分别添加listener
        List<ReadSheet> parxSheetlist = new ArrayList<>();
        AiModelCacheDTO modelCache = new AiModelCacheDTO(appId);
        AiModelInfoListener modelInfoListener = new AiModelInfoListener(modelCache);
        AiModelIntentListener modelIntentListener = new AiModelIntentListener(modelCache);

        for (ReadSheet sheet : sheets) {
            String sheetName = sheet.getSheetName();
            if (StringUtils.isEmpty(sheetName)) {
                continue;
            }
            if (sheetName.startsWith("分类模型信息_")) {
                parxSheetlist.add(EasyExcelFactory.readSheet(sheet.getSheetNo()).head(AiModelInfoExcelDTO.class).registerReadListener(modelInfoListener).build());
            } else if (sheetName.startsWith("分类模型意图_")) {
                parxSheetlist.add(EasyExcelFactory.readSheet(sheet.getSheetNo()).head(AiModelIntentExcelDTO.class).registerReadListener(modelIntentListener).build());
            } else {
                log.warn("当前sheet命名不合规范，数据会被忽略，sheet名为:{}", sheetName);
            }
        }
        //2.无有效sheet，返回失败
        if (CollectionUtils.isEmpty(parxSheetlist)) {
            throw new BizException("A0043", "无效的导入数据！");
        }
        //3.读取
        excelReader.read(parxSheetlist);
        //4.构造模型DTO
        List<Pair<AiModelDTO, List<AiIntentDTO>>> importModelList = contructAiModels(modelCache);
        if (CollectionUtils.isEmpty(importModelList)) {
            return new UploadResultVO(false, "无效的导入数据！");
        }
        //5.批量添加模型
        List<AiModelDTO> aiModelDTOs = importModelList.stream().map(Pair::getKey).collect(Collectors.toList());
        aiModelRepository.saveBatch(aiModelDTOs);
        //6.批量添加模型意图
        List<AiIntentDTO> modelIntentDTOs = importModelList.stream().flatMap(pair -> pair.getValue().stream()).collect(Collectors.toList());
        aiIntentRepository.saveBatch(modelIntentDTOs);
        //7.返回结果
        return new UploadResultVO(true, "成功");
    }

    private void checkFileUpload(MultipartFile file) {
        String exceptionCode = "A0044";
        if (file == null) {
            throw new BizException(exceptionCode, new String[]{"uploadEvaluateData occur errer, file is  empty"}, "uploadEvaluateData occur errer, file is  empty");
        }
        long fileSize = file.getSize();
        if (fileSize > MAX_FILE_SIZE) {
            throw new BizException(exceptionCode, new String[]{"uploadEvaluateData occur errer, file over max size"}, "uploadEvaluateData occur errer, file over max size");
        }
        String contentType = file.getContentType();
        if (!ALLOW_FILE_TYPE.contains(contentType)) {
            throw new BizException(exceptionCode, new String[]{"uploadEvaluateData occur errer, invaid file type"}, "uploadEvaluateData occur errer, invaid file type");
        }
    }

    @Override
    public void download(AiModelDownParam param, HttpServletResponse response) throws Exception {
        param.setAppId(RequestContext.get().getAppCode());
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("分类模型信息", "UTF-8").replace("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            try (ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream()).build()) {
                WriteSheet modelSheet = EasyExcelFactory.writerSheet(0, "分类模型信息_" + "1").head(AiModelInfoExcelDTO.class).build();
                WriteSheet intentSheet = EasyExcelFactory.writerSheet(1, "分类模型意图_" + "1").head(AiModelIntentExcelDTO.class).build();

                Map<String, String> modelMap = Maps.newLinkedHashMap();
                //1.导出模板信息
                int modelPageNo = 1;
                while (true) {
                    ModelPageParam queryModelParam = buildQueryModelParam(param, modelPageNo);
                    PageImpl<ModelPageListParam> modelPage = aiModelList(queryModelParam);
                    if (CollectionUtils.isNotEmpty(modelPage.getRecords())) {
                        List<ModelPageListParam> classifierModels = modelPage.getRecords().stream().
                                filter(e -> (e.getIsGlobal() == null || !e.getIsGlobal()))
                                .collect(Collectors.toList());
                        classifierModels.forEach(e -> modelMap.put(e.getModelCode(), e.getModelName()));
                        List<AiModelInfoExcelDTO> exportModels = RobotConfigConverter.convertToModelWriteList(classifierModels);
                        excelWriter.write(exportModels, modelSheet);
                    }

                    if (modelPage.getCurrent() >= modelPage.getPages()) {
                        break;
                    }
                    modelPageNo++;
                }
                //2.导出意图信息
                for (Map.Entry<String, String> model : modelMap.entrySet()) {
                    int intentPageNo = 1;
                    while (true) {
                        ModelIntentPageParam queryIntentParam = buildQueryIntentParam(model.getKey(), intentPageNo);
                        PageImpl<ModelIntentParam> intentPage = queryIntentPage(queryIntentParam);
                        List<AiModelIntentExcelDTO> intents = RobotConfigConverter.convertToIntentWriteList(model.getValue(), intentPage.getRecords());
                        excelWriter.write(intents, intentSheet);
                        if (intentPage.getCurrent() >= intentPage.getPages()) {
                            break;
                        }
                        intentPageNo++;
                    }
                }
            }
        } catch (IOException e) {
            response.reset();
            response.setHeader("X-Content-Type-Options", "nosniff");
            response.setHeader("Content-Security-Policy", "default-src 'self'");
            response.setHeader("X-Frame-Options", "DENY");
            response.setHeader("X-XSS-Protection", "1; mode=block");
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            Map<String, String> map = com.alibaba.excel.util.MapUtils.newHashMap();
            map.put("status", "failure");
            map.put("message", "导出失败，请稍候重试" + e.getMessage());
            response.getWriter().println(com.alibaba.fastjson.JSON.toJSONString(map));
        }
    }

    @Override
    public List<QueryByModelTypeVO> queryByModelType(String modelType) {
        // 构造查询条件
        LambdaQueryWrapper<AiModelPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AiModelPO::getModelType, modelType);
        queryWrapper.eq(AiModelPO::getAppId, RequestContext.getAppCode());

        queryWrapper.or(wapper -> {
            wapper.eq(AiModelPO::getGlobalFlag, 1);
            wapper.eq(AiModelPO::getModelType, modelType);
        });

        List<AiModelPO> aiModelPOList = this.aiModelService.list(queryWrapper);
        if (CollUtil.isEmpty(aiModelPOList)) {
            return Collections.emptyList();
        }
        return aiModelPOList.stream().map(aiModelPO -> {
            QueryByModelTypeVO queryByModelTypeVO = new QueryByModelTypeVO();
            BeanUtil.copyProperties(aiModelPO, queryByModelTypeVO);
            return queryByModelTypeVO;
        }).collect(Collectors.toList());

    }

    private ModelPageParam buildQueryModelParam(AiModelDownParam param, Integer pageNo) {
        ModelPageParam queryParam = new ModelPageParam();
        queryParam.setAppId(param.getAppId());
        queryParam.setModelName(param.getModelName());
        queryParam.setModelCodeList(param.getModelCodeList());
        queryParam.setPageNum(pageNo);
        queryParam.setPageSize(200);
        return queryParam;
    }

    private ModelIntentPageParam buildQueryIntentParam(String modelCode, Integer pageNo) {
        ModelIntentPageParam queryParam = new ModelIntentPageParam();
        queryParam.setModelCode(modelCode);
        queryParam.setPageNum(pageNo);
        queryParam.setPageSize(200);
        return queryParam;
    }

    public List<Pair<AiModelDTO, List<AiIntentDTO>>> contructAiModels(AiModelCacheDTO modelCache) {
        List<ModelEditParam> modelParams = convert(modelCache);
        if (CollectionUtils.isEmpty(modelParams)) {
            return Collections.emptyList();
        }
        List<Pair<AiModelDTO, List<AiIntentDTO>>> importModelList = Lists.newArrayList();
        for (ModelEditParam param : modelParams) {
            List<AiModelPO> list = aiModelService.checkModelName(param.getModelName(), param.getModelCode(), param.getAppId(), false);
            if (CollUtil.isNotEmpty(list)) {
                continue;
            }
            Pair<AiModelDTO, List<AiIntentDTO>> pair = buildAiModel(param);
            importModelList.add(pair);
        }
        return importModelList;
    }

    private List<ModelEditParam> convert(AiModelCacheDTO modelCache) {
        return modelCache.getModelInfoMap().values().stream().map(e -> {
            ModelEditParam modelEditParam = RobotConfigConverter.convert(e, modelCache.getModelIntents(e.getModelName()));
            modelEditParam.setAppId(modelCache.getAppId());
            return modelEditParam;
        }).collect(Collectors.toList());
    }

    public Pair<AiModelDTO, List<AiIntentDTO>> buildAiModel(ModelEditParam param) {
        LocalDateTime now = LocalDateTime.now();
        AiModelDTO aiModelDTO = initConvertAiModelDTO(param, now);
        List<AiIntentDTO> modelIntents = Lists.newArrayList();
        //1.创建
        String modelCode = IdGenerator.getId("model_");
        aiModelDTO.setModelCode(modelCode);
        aiModelDTO.setModelState(AiModelStateEnum.VALID);
        String vectorConfig = null;

        switch (ModelTypeEnum.valueOf(param.getModelType())) {
            case CLASSIFIER:
                modelIntents = buildIntents(modelCode, param);
                break;
            case EMBEDDING:
                // 创建向量分析器
                vectorConfig = createVector(param, modelCode);
                // 处理模型配置
                processModelConfig(param, aiModelDTO);
                break;
            default:
                break;
        }
        aiModelDTO.setExternalModelConfig(vectorConfig);
        return Pair.of(aiModelDTO, modelIntents);
    }


    private void processModelConfig(ModelEditParam param, AiModelDTO aiModelDTO) {
        ModelConfigDTO modelConfigDTO = JSON.parseObject(param.getModelConfig(), ModelConfigDTO.class);
        modelConfigDTO.setVectorType(ModelConfigTypeEnum.USERREQUEST.getCode());

        // 判断是否区分模型配置
        if (param.getDistinguishModelConfig() != null && param.getDistinguishModelConfig() == 0) {
            // 不区分模型配置，复制用户请求配置作为离线文档配置
            ModelConfigDTO offlineDocumentsModelConfig = new ModelConfigDTO();
            BeanUtil.copyProperties(modelConfigDTO, offlineDocumentsModelConfig);
            offlineDocumentsModelConfig.setVectorType(ModelConfigTypeEnum.OFFLINEDOCUMENTSMODELCONFIG.getCode());
            aiModelDTO.setModelConfig(JSON.toJSONString(Lists.newArrayList(modelConfigDTO, offlineDocumentsModelConfig)));
        } else if (CharSequenceUtil.isNotBlank(param.getOfflineDocumentsModelConfig())) {
            // 区分模型配置，使用传入的离线文档配置
            ModelConfigDTO offlineDocumentsModelConfig = JSON.parseObject(param.getOfflineDocumentsModelConfig(), ModelConfigDTO.class);
            offlineDocumentsModelConfig.setVectorType(ModelConfigTypeEnum.OFFLINEDOCUMENTSMODELCONFIG.getCode());
            aiModelDTO.setModelConfig(JSON.toJSONString(Lists.newArrayList(modelConfigDTO, offlineDocumentsModelConfig)));
        }
    }

    private List<AiIntentDTO> buildIntents(String modelCode, ModelEditParam param) {
        if (CollUtil.isEmpty(param.getIntentList())) {
            return Collections.emptyList();
        }
        return param.getIntentList().stream().map(o -> {
            // 判断是新增还是删除
            AiIntentDTO aiIntentDTO = new AiIntentDTO();
            BeanUtil.copyProperties(o, aiIntentDTO);
            aiIntentDTO.setIntentId(IdGenerator.id());
            aiIntentDTO.setModelCode(modelCode);
            aiIntentDTO.setAppId(param.getAppId());
            return aiIntentDTO;
        }).collect(Collectors.toList());
    }
}
