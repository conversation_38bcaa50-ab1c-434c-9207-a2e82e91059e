package com.chinatelecom.gs.workflow.core.workflow.core.model.dag;

import com.alibaba.fastjson.annotation.JSONField;
import com.chinatelecom.gs.engine.core.sdk.vo.msg.MessageCallback;
import com.chinatelecom.gs.engine.robot.sdk.enums.InteractionTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.ToolMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.Instruction;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.Action;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.ActionMessage;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.IActionCallBackService;
import com.chinatelecom.gs.workflow.core.workflow.core.enums.EnvTypeEnum;
import com.chinatelecom.gs.workflow.core.workflow.core.model.entity.Tool;
import lombok.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 图请求参数
 * @USER: pengmc1
 * @DATE: 2024/12/26 16:04
 */

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DagParam {
    /**
     * 应用编码 = 租户ID
     */
    private String appCode;
    /**
     * 会话ID
     */
    private String sessionId;
    /**
     * 上行消息ID
     */
    private String messageId;
    /**
     * 消息消息ID
     */
    private String downMessageId;
    /**
     * 环境
     */
    private EnvTypeEnum env;
    /**
     * 输入参数
     */
    private Map<String,Object> inputParamMap = new HashMap<>();

    private Map<String,Object> globalVarMap = new HashMap<>();

    /**
     * 用户ID
     */
    private String userId;
    /**
     * 交互方式
     */
    private InteractionTypeEnum interactionType;
    /**
     * 消息回调
     */
    @JSONField(serialize = false)
    private MessageCallback<ToolMessageResponse> messageCallback;

    @JSONField(serialize = false)
    private IActionCallBackService<ActionMessage> actionCallBack;
    /**
     * agent编码
     */
    private String agentCode;

    /**
     * 是否是公开市场agent
     */
    private Boolean isSystemAgent;

    /**
     * 是否debug模式，debug模式会保存执行过程, 默认为false
     */
    private Boolean debug = false;
    /**
     * 直接进入的工具
     */
    private Tool directTool;
    /**
     * 聊天类型
     * 见 com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.ChatTypeEnum
     */
    private String chatType;
    /**
     * 安全围栏开关
     */
    private Boolean safeFenceSwitch;
    /**
     * 闲聊开关
     */
    private Boolean chatLLMSwitch;

    /**
     * 收到的指令
     */
    private Instruction instruction;
}

