package com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.secure;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.chinatelecom.gs.engine.common.utils.JsonUtils;
import com.chinatelecom.gs.engine.core.corekit.common.core.AnswerService;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.SafeCheckService;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.enums.ContentTypeEnum;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.request.CheckContentRequest;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.service.SafeFenceConfigService;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.utils.SafeFenceAPIKeyGenerator;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.utils.SafeRequestUtils;
import com.chinatelecom.gs.engine.robot.sdk.enums.AnswerTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.InteractionTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.enums.SystemIntentEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.AgentRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.AgentResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.config.AgentConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.DmToolIntent;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.ChatTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.dialog.enums.DialogEngineType;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.SseMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.*;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.entry.RichTextCard;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.entry.RichTextOptionCard;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.card.option.OptionEntry;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.request.AnswerBuildRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.DialogMessageTypeEnum;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.enums.SendMessageTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.websocket.Session;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class AgentSafeFenceCheck {
    @Autowired
    private SafeCheckService safeCheckService;

    @Resource
    private SafeFenceConfigService safeFenceConfigService;

    @Autowired
    private AnswerService answerService;

    private final String ENTER_KEY = "\n";

    private final String DETAIL_KEY = "\n";

    public AgentResponse preCheck(AgentRequest agentRequest, AgentConfig agentConfig){
        try {
            boolean safeFenceSwitch = getSafeFenceSwitch(agentRequest, agentConfig);
            if(agentRequest.getChatType().equalsIgnoreCase(
                    ChatTypeEnum.CHAT.getCode()) && safeFenceSwitch){
                CheckContentRequest checkContentRequest = SafeRequestUtils.buildAgentContentRequest(agentRequest.getOriginContent(), ContentTypeEnum.USER_QUERY, agentRequest, agentConfig.getSafeFenceConfig(), 1);
                HashMap<String, String> header = SafeFenceAPIKeyGenerator.generateHeader(agentConfig.getSafeFenceConfig());
                Boolean isPassSafeFence = safeCheckService.safeCheckContent(header, checkContentRequest, true);
                if (!isPassSafeFence) {
                    log.info("【Agent】【SafeFence】 请求{} 未通过安全围栏", agentRequest.getOriginContent());
                    AgentAnswer secureAnswer = buildSecureMatchAnswer(agentConfig, null);
                    return new AgentResponse(Lists.newArrayList(secureAnswer));
                }
            }
        } catch (Exception e) {
            log.error("【Agent】【Secure】 安全围栏检查失败 ", e);
        }
        return null;
    }

    private boolean getSafeFenceSwitch(AgentRequest agentRequest, AgentConfig agentConfig) {
        boolean defaultSecurityFence = agentConfig.getGlobalPolicyConfig().isSecurityFenceSwitch() && agentConfig.getSafeFenceSwitch();
        Boolean clientSafeFenceSwitch = agentRequest.getHeader().getClient().getSafeFenceSwitch();
        return safeFenceConfigService.isForceEnableFence() || (Objects.nonNull(clientSafeFenceSwitch) ? (clientSafeFenceSwitch && agentConfig.getSafeFenceSwitch()) : defaultSecurityFence);
    }

    private AgentAnswer buildSecureMatchAnswer(AgentConfig agentConfig, String messageId) {
        AnswerBuildRequest answerBuildRequest = new AnswerBuildRequest();
        answerBuildRequest.setAnswerTypeEnum(AnswerTypeEnum.SIMPLE_RICH_TEXT);
        answerBuildRequest.setContent(agentConfig.getGlobalPolicyConfig().getSecurityFenceScript());
        Answer answer = this.answerService.buildAnswer(answerBuildRequest);
        if (!ObjectUtils.isEmpty(messageId)) {
            answer.setMessageId(messageId);
        }

        AgentAnswer agentAnswer = BeanUtil.toBean(answer, AgentAnswer.class);

        DmToolIntent answerIntent = new DmToolIntent();
        answerIntent.setToolIntentId(SystemIntentEnum.SENSITIVE.getCode());
        answerIntent.setToolIntentName(SystemIntentEnum.SENSITIVE.getDesc());
        answerIntent.setDialogEngineType(DialogEngineType.SYSTEM);
        agentAnswer.setAnswerSourceType(AnswerSourceType.SYSTEM);
        agentAnswer.setDmToolIntent(answerIntent);

        return agentAnswer;
    }

    public AgentResponse postCheck(AgentRequest agentRequest, AgentConfig agentConfig, AgentResponse agentResponse) {
        if (Objects.isNull(agentResponse)) {
            return null;
        }

        boolean safeFenceSwitch = getSafeFenceSwitch(agentRequest, agentConfig);

        if (!safeFenceSwitch) {
            return agentResponse;
        }

        boolean matchSecure = false;
        AnswerSourceType preResponseEnum = null;
        String preMessageId = null;
        for (AgentAnswer intentAnswer : agentResponse.getIntentAnswers()) {
            if (intentAnswer.getDmToolIntent().getToolIntentId().equalsIgnoreCase(SystemIntentEnum.SENSITIVE.getCode())) {
                continue;
            }


//            MatchSensitiveRequest matchSensitiveRequest = new MatchSensitiveRequest();
//            matchSensitiveRequest.setContent(JSON.toJSONString(intentAnswer));
            try {
                String checkContent = buildCheckContent(intentAnswer);
                CheckContentRequest checkContentRequest = SafeRequestUtils.buildAgentContentRequest(checkContent, ContentTypeEnum.MODEL_OUTPUT, agentRequest, agentConfig.getSafeFenceConfig(), 1);
                HashMap<String, String> header = SafeFenceAPIKeyGenerator.generateHeader(agentConfig.getSafeFenceConfig());
                Boolean isPassSafeFence = safeCheckService.safeCheckContent(header, checkContentRequest, true);
                if (!isPassSafeFence) {
                    log.info("【Agent】【SafeFence】 答案{} 未通过安全围栏", agentRequest.getOriginContent());
                    matchSecure = true;
                    preResponseEnum = intentAnswer.getAnswerSourceType();
                    preMessageId = agentRequest.getHeader().getTrack().getDownMessageId();
                }
            } catch (Exception e) {
                log.error("【Agent】【Secure】 安全围栏检查失败 ", e);
            }

            if (matchSecure) {
                break;
            }

        }

        if (matchSecure) {
            AgentAnswer secureAnswer = buildSecureMatchAnswer(agentConfig, preMessageId);
            //命中敏感词结果是大模型结果，需要推送一笔part的cover消息
            if (Objects.nonNull(preResponseEnum) && preResponseEnum.equals(AnswerSourceType.LLM)) {
                sendSecureCoverMessage(agentRequest, agentConfig, secureAnswer);
            }
            return new AgentResponse(Lists.newArrayList(secureAnswer));
        }

        return agentResponse;
    }

    private String buildCheckContent(AgentAnswer intentAnswer) {
        try {
            if (Objects.isNull(intentAnswer)) {
                return "";
            }
            StringBuilder contentBuilder = new StringBuilder();
            String content = null;
            if (intentAnswer.getContent() instanceof RichTextOptionCard) {
                List<OptionEntry<RichTextCard>> entries = ((RichTextOptionCard) intentAnswer.getContent()).getOption().getEntries();
                entries.forEach(entry -> contentBuilder.append(entry.getShowCard().getHtml()));
            } else {
                content = Objects.nonNull(intentAnswer.getContent()) ? intentAnswer.getContent().toString() : "";
            }
            String reasoning = Objects.nonNull(intentAnswer.getReasoning()) ? intentAnswer.getReasoning().getReasoningContent() : "";
            String speech = Objects.nonNull(intentAnswer.getSpeech()) ? intentAnswer.getSpeech().toString() : "";
            contentBuilder.append(content);
            if (!StringUtils.isEmpty(reasoning)) {
                contentBuilder.append(ENTER_KEY).append(reasoning);
            }
            if (!StringUtils.isEmpty(speech)) {
                contentBuilder.append(ENTER_KEY).append(speech);
            }
            if (!CollectionUtils.isEmpty(intentAnswer.getSourceInfos())) {
                contentBuilder.append(ENTER_KEY).append("知识库来源：");
                intentAnswer.getSourceInfos().forEach(sourceInfo -> {
                    if (Objects.nonNull(sourceInfo)) {
                        contentBuilder.append(sourceInfo.getSourceName()).append(DETAIL_KEY);
                        if (Objects.nonNull(sourceInfo.getCandidates()) && !CollectionUtils.isEmpty(sourceInfo.getCandidates())) {
                            sourceInfo.getCandidates().forEach(candidate -> {
                                contentBuilder.append(candidate.getContent()).append(candidate.getSimilarContent());
                            });
                        }
                        contentBuilder.append(ENTER_KEY);
                    }
                });
            }
            return contentBuilder.toString();
        } catch (Exception e) {
            log.error("【Agent】【Secure】 构建安全检测内容失败 ", e);
        }
        return "";
    }

    private void sendSecureCoverMessage(AgentRequest agentRequest, AgentConfig agentConfig, AgentAnswer secureAnswer) {
        SseMessageResponse secureMessageResponse = buildSseMessage(agentRequest, secureAnswer);
        try {
            if (InteractionTypeEnum.SSE.getCode()
                    .equals(agentRequest.getHeader().getClient().getInteractionType().getCode())) {
                SseEmitter sseEmitter = agentRequest.getHeader().getClient().getEmitter();
                synchronized (sseEmitter) {
                    sseEmitter.send(secureMessageResponse, MediaType.APPLICATION_JSON);
                }
            } else if (InteractionTypeEnum.WEBSOCKET.getCode()
                    .equals(agentRequest.getHeader().getClient().getInteractionType().getCode())) {
                Session session = agentRequest.getHeader().getClient().getSession();
                synchronized (session) {
                    if (session.isOpen()) {
                        session.getBasicRemote().sendText(JsonUtils.toJsonString(secureMessageResponse));
                    }
                }
            }
        } catch (Exception e) {
            log.error("【Agent】【Secure】 发送敏感词检测消息{} 失败 ", JSON.toJSONString(secureAnswer), e);
        }

    }

    @NotNull
    private SseMessageResponse buildSseMessage(AgentRequest agentRequest, AgentAnswer secureAnswer) {
        Answer answer = BeanUtil.toBean(secureAnswer, Answer.class);
        BotAnswer botAnswer = BeanUtil.toBean(answer, BotAnswer.class);
        botAnswer.setIntent(toAnswerIntent(secureAnswer));
        botAnswer.setContentType(com.chinatelecom.gs.engine.robot.sdk.enums.ContentTypeEnum.ALL.getCode());
        botAnswer.setContent("");
        botAnswer.setSpeech(null);
        SseMessageResponse secureMessageResponse = new SseMessageResponse();
        secureMessageResponse.setAnswer(botAnswer);
        secureMessageResponse.setUserId(agentRequest.getHeader().getUser().getUserId());
        secureMessageResponse.setUpMsgId(agentRequest.getHeader().getTrack().getMessageId());
        secureMessageResponse.setSessionId(agentRequest.getHeader().getTrack().getSessionId());
        secureMessageResponse.setDownMsgId(agentRequest.getHeader().getTrack().getDownMessageId());
        secureMessageResponse.setMessageType(DialogMessageTypeEnum.PART);
        secureMessageResponse.setEventType(SendMessageTypeEnum.COVER);
        return secureMessageResponse;
    }

    private AnswerIntent toAnswerIntent(AgentAnswer agentAnswer) {
        DmToolIntent dmToolIntent = agentAnswer.getDmToolIntent();
        AnswerIntent intent = new AnswerIntent();
        intent.setToolIntentId(dmToolIntent.getToolIntentId());
        intent.setDialogEngineType(dmToolIntent.getDialogEngineType());
        intent.setToolIntentName(dmToolIntent.getToolIntentName());
        intent.setSimilarContent(dmToolIntent.getSimilarContent());
        intent.setAnswerSourceType(agentAnswer.getAnswerSourceType());
        return intent;
    }
}
