package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"os/signal"
	"strings"
)

func main() {
    configOn := [NPMCONFIGON]

    if configOn {
        npmCmd := exec.Command("npm", "config", "set", "[TOKEN]")
        err := npmCmd.Run()
        if err != nil {
            fmt.Println("Error executing npm config:", err)
            return
        }
    }

	cmd := exec.Command("[CMD]", [ARGS])
	cmd.Env = append(os.Environ(), [ENVS])

	stdout, err := cmd.StdoutPipe()
	if err != nil {
		fmt.Println("Error creating stdout pipe:", err)
		return
	}

	stderr, err := cmd.StderrPipe()
	if err != nil {
		fmt.Println("Error creating stderr pipe:", err)
		return
	}

	stdin, err := cmd.StdinPipe()
	if err != nil {
		fmt.Println("Error creating stdin pipe:", err)
		return
	}

	if err := cmd.Start(); err != nil {
		fmt.Println("Error starting command:", err)
		return
	}

	go func() {
		scanner := bufio.NewScanner(stdout)
		for scanner.Scan() {
			line := strings.TrimSpace(scanner.Text())
			if line != "" {
				var response map[string]interface{}
				err := json.Unmarshal([]byte(line), &response)
				if err == nil {
					jsonResponse, _ := json.Marshal(response)
					fmt.Println("Received from server:@MCP@", string(jsonResponse), "@MCP@")
				} else {
					fmt.Println("Invalid JSON:", line)
				}
			}
		}
	}()

	go func() {
		scanner := bufio.NewScanner(stderr)
		for scanner.Scan() {
			fmt.Println("Server output text:", scanner.Text())
		}
	}()

	go func() {
		jsonTemplate := `{
			"jsonrpc": "2.0",
			"id": "[ID]",
			"method": "[METHOD]",
			"params": [PARAMS]
		}`

		var request map[string]interface{}
		if err := json.Unmarshal([]byte(jsonTemplate), &request); err != nil {
			fmt.Println("Error parsing JSON template:", err)
			return
		}

		jsonRequest, err := json.Marshal(request)
		if err != nil {
			fmt.Println("Error marshaling request:", err)
			return
		}

		if _, err := stdin.Write(append(jsonRequest, '\n')); err != nil {
			fmt.Println("Error writing to stdin:", err)
		}

		if err := stdin.Close(); err != nil {
			fmt.Println("Error closing stdin:", err)
		}
	}()

	signalChan := make(chan os.Signal, 1)
	signal.Notify(signalChan, os.Interrupt)

	done := make(chan struct{})
	go func() {
		if err := cmd.Wait(); err != nil {
			fmt.Println("Command finished with error:", err)
		} else {
			fmt.Println("Command finished successfully.")
		}
		close(done)
	}()

	select {
	case <-signalChan:
		fmt.Println("Interrupt received, shutting down...")
		cmd.Process.Kill()
	case <-done:
	}
}