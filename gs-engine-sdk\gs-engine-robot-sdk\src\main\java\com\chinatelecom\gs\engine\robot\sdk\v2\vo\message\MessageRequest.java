package com.chinatelecom.gs.engine.robot.sdk.v2.vo.message;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@Schema(description = "机器人对话请求体")
public class MessageRequest implements Serializable {


    @NotBlank(message = "机器人编码不能为空！")
    @Schema(description = "机器人编码", example = "robot_001")
    private String agentCode;
    /**
     * 为空时，默认为内部聊天窗接入
     */
    @Schema(description = "请求渠道入口", example = "web")
    private String channelId;
    /**
     * 为空时，默认是chat
     */
    @Schema(description = "聊天类型", example = "chat")
    private String chatType;

    @Schema(description = "请求内容", example = "你好")
    @NotBlank(message = "content不能为空！")
    private String content;

    @Schema(description = "会话ID", example = "session_001")
    private String sessionId;

    @Schema(description = "消息ID", example = "msg_001")
    private String messageId;

//    @NotBlank(message = "用户唯一标识不能为空！")
    @Schema(description = "用户ID", example = "user_001")
    @NotBlank(message = "用户唯一标识不能为空！")
    private String userId;

    @Schema(description = "第三方用户ID", example = "third_user_001")
    private String thirdUserId;

    /**
     * 是否Debug模式
     */
    @Schema(description = "DEBUG模式", example = "0")
    private Integer debug;

    /**
     * 是否Test模式
     */
    @Schema(description = "Test模式", example = "0")
    private Integer test;


    @Schema(description = "请求时间", example = "1718304014")
    private Long requestTime;


    @Schema(description = "临时变量列表")
    Map<String, Object> variableMap;

    @Schema(description = "扩展信息", example = "{filter: \"{\"knowledgeFilters\":[{\"knowledgeBaseCode\":\"1234\"}]}\", range: \"all\"}")
    private Map<String, Object> extraData;

    @Schema(description = "文件列表")
    private List<String> fileCodes;

    /**
     * 入口
     */
    @JsonIgnore
    private String entry;
    /**
     * 业务类型
     */
    @JsonIgnore
    private String bizName;

    @Schema(description = "聊天窗是否使用安全围栏", example = "false")
    private Boolean safeFenceSwitch;

    @Schema(description = "聊天窗是否打开闲聊", example = "true")
    private Boolean chatLLMSwitch;
}