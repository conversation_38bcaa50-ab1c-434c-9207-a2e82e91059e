package com.chinatelecom.gs.engine.kms.sdk.vo.catalog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CatalogMoveParam {

    @NotNull(message = "知识库Code不能为空")
    @Schema(description = "知识库Code")
    private String knowledgeBaseCode;

    @Schema(description = "父目录Code")
    private String parentCode;

    @Schema(description = "同层级前置目录code")
    private String prevCode;

    @NotNull(message = "是否包括子目录不能为空")
    @Schema(description = "是否包括子目录", hidden = true)
    private Boolean withChildren;
}
