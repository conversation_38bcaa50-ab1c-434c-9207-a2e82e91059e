package com.chinatelecom.gs.engine.robot.manage.controller.web.manage;

import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.DataResourceAccess;
import com.chinatelecom.gs.engine.kms.sdk.vo.knowledge.base.KnowledgeBaseVO;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.AgentKmsAuthConfigRequest;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.AgentKmsConfigRequest;
import com.chinatelecom.gs.engine.robot.manage.info.domain.response.AgentKmsAuthConfigResponse;
import com.chinatelecom.gs.engine.robot.manage.info.domain.response.AgentKmsConfigResponse;
import com.chinatelecom.gs.engine.robot.manage.info.domain.response.AgentKnowledgeResponse;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.manage.service.KmsManageService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.request.DeleteKnowledgeBaseRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.request.QueryKnowledgeBaseRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.kms.request.SubscribeKnowledgeRequest;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelelcom.gs.engine.sdk.common.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @description 知识库
 * @date 2024年07月04日
 */

@RestController
@Tag(name = "agent知识库操作")
@RequestMapping({Constants.ROBOT_PREFIX + Constants.WEB_PREFIX + "/kms", Constants.ROBOT_PREFIX + Constants.API_PREFIX + "/kms"})
public class AgentKmsController {

    private static final String AGENT_TYPE = "agentType";

    @Autowired
    private DataResourceAccess dataResourceAccess;

    @Resource
    private KmsManageService kmsManageService;

    @Resource
    private AgentBasicConfigService agentBasicConfigService;

    /**
     * 知识库列表查询
     *
     * @param request QueryKnowledgeBaseRequest
     * @return QueryKnowledgeBaseResponse
     */
    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "知识库列表查询")
    @PlatformRestApi(name = "知识库列表查询", groupName = "agent知识库操作")
    @PostMapping({"/knowledge/base/page"})
    @AuditLog(businessType = "agent知识库操作", operType = "知识库列表查询", operDesc = "知识库列表查询", objId = "#request.agentCode")
    Result<Page<KnowledgeBaseVO>> page(@Validated @RequestBody QueryKnowledgeBaseRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(kmsManageService.page(request));
    }

    /**
     * 知识库列表查询
     *
     * @return QueryKnowledgeBaseResponse
     */
    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "机器人发布时检查是否存在未发布文档的知识库", description = "检查机器人是否存在未发布的文档")
    @PlatformRestApi(name = "检查机器人是否存在未发布的文档", groupName = "agent知识库操作")
    @GetMapping({"/knowledge/base/checkKms"})
    @AuditLog(businessType = "agent知识库操作", operType = "检查机器人是否存在未发布的文档", operDesc = "检查机器人是否存在未发布的文档", objId = "#agentCode")
    @HideFromApiTypes(ApiType.OPENAPI)
    Result<List<AgentKnowledgeResponse>> checkKms(
            @Parameter(description = "机器人code", required = true)
            @RequestParam(value = "agentCode") String agentCode) {
        agentBasicConfigService.checkAgentAuth(agentCode, true);
        return Result.success(kmsManageService.checkKms(agentCode));
    }

    /**
     * 已有知识库列表过滤查询
     *
     * @param request QueryKnowledgeBaseRequest
     * @return QueryKnowledgeBaseResponse
     */
    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "已有知识库查询")
    @PlatformRestApi(name = "已有知识库查询", groupName = "agent知识库操作")
    @PostMapping({"/knowledge/base/existpage"})
    @AuditLog(businessType = "agent知识库操作", operType = "已有知识库查询", operDesc = "已有知识库查询", objId = "#request.agentCode")
    Result<Page<KnowledgeBaseVO>> pageExist(@Validated @RequestBody QueryKnowledgeBaseRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(kmsManageService.pageExist(request));
    }


    /**
     * 删除知识库
     *
     * @param request DeleteKnowledgeBaseRequest
     * @return Boolean
     */
    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "删除知识库")
    @PlatformRestApi(name = "删除知识库", groupName = "agent知识库操作")
    @PostMapping({"/knowledge/base/delete"})
    @AuditLog(businessType = "agent知识库操作", operType = "删除知识库", operDesc = "删除知识库", objId = "#request.agentCode")
    Result<Boolean> delete(@Validated @RequestBody DeleteKnowledgeBaseRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(kmsManageService.delete(request));
    }

    /**
     * 知识库订阅
     * @param request
     * @return
     */
    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "知识库订阅")
    @PlatformRestApi(name = "知识库订阅", groupName = "agent知识库操作")
    @PostMapping({"/knowledge/base/subscribe"})
    @AuditLog(businessType = "agent知识库操作", operType = "知识库订阅", operDesc = "知识库订阅", objId = "#request.agentCode")
    Result<Boolean> subscribe(@Validated @RequestBody SubscribeKnowledgeRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(kmsManageService.subscribe(request));
    }


    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "queryKmsConfig查询接口", method = "GET")
    @PlatformRestApi(name = "queryKmsConfig查询接口", groupName = "agent知识库操作")
    @GetMapping("/queryKmsConfig")
    @AuditLog(businessType = "agent知识库操作", operType = "queryKmsConfig查询接口", operDesc = "queryKmsConfig查询接口", objId = "#agentCode")
    public Result<AgentKmsConfigResponse> queryKmsConfig(@RequestParam(value = "agentCode") String agentCode) {
        agentBasicConfigService.checkAgentAuth(agentCode, true);
        return Result.success(agentBasicConfigService.queryKmsConfig(agentCode));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "kmsConfig保存接口")
    @PlatformRestApi(name = "kmsConfig保存接口", groupName = "agent知识库操作")
    @PostMapping("/saveKmsConfig")
    @AuditLog(businessType = "agent知识库操作", operType = "kmsConfig保存接口", operDesc = "kmsConfig保存接口", objId = "#agentKmsConfigRequest.agentCode")
    public Result<AgentKmsConfigResponse> saveKmsConfig(
            @Validated @RequestBody AgentKmsConfigRequest agentKmsConfigRequest) {
        agentBasicConfigService.checkAgentAuth(agentKmsConfigRequest.getAgentCode(), true);
        return Result.success(agentBasicConfigService.saveKmsConfig(agentKmsConfigRequest, true));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "kmsAuthConfig查询接口")
    @PlatformRestApi(name = "kmsAuthConfig查询接口", groupName = "agent知识库操作")
    @GetMapping("/queryKmsAuthConfig")
    @AuditLog(businessType = "agent知识库操作", operType = "kmsAuthConfig查询接口", operDesc = "kmsAuthConfig查询接口", objId = "#agentCode")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<AgentKmsAuthConfigResponse> queryKmsAuthConfig(
            @RequestParam(value = "agentCode") String agentCode, @RequestParam(value = "knowledgeBaseCode") String knowledgeBaseCode) {
        RequestInfo requestInfo = RequestContext.get();
        dataResourceAccess.getResourceData(agentCode, requestInfo.getTenantId(), AGENT_TYPE, requestInfo.getUserId());
        return Result.success(agentBasicConfigService.queryKmsAuthConfig(agentCode, knowledgeBaseCode));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "kmsAuthConfig保存接口")
    @PlatformRestApi(name = "kmsAuthConfig保存接口", groupName = "agent知识库操作")
    @HideFromApiTypes(ApiType.OPENAPI)
    @PostMapping("/saveKmsAuthConfig")
    @AuditLog(businessType = "agent知识库操作", operType = "kmsAuthConfig保存接口", operDesc = "kmsAuthConfig保存接口", objId = "#agentKmsAuthConfigRequest.agentCode")
    public Result<AgentKmsAuthConfigResponse> saveKmsAuthConfig(
            @Validated @RequestBody AgentKmsAuthConfigRequest agentKmsAuthConfigRequest) {
        RequestInfo requestInfo = RequestContext.get();
        dataResourceAccess.getResourceData(agentKmsAuthConfigRequest.getAgentCode(), requestInfo.getTenantId(), AGENT_TYPE, requestInfo.getUserId());
        return Result.success(agentBasicConfigService.saveKmsAuthConfig(agentKmsAuthConfigRequest));
    }
}
