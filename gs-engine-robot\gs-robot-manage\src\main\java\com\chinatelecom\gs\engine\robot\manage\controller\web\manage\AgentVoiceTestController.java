package com.chinatelecom.gs.engine.robot.manage.controller.web.manage;


import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.context.RequestContext;
import com.chinatelecom.gs.engine.common.context.RequestInfo;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.s3.CloudStorageDao;
import com.chinatelecom.gs.engine.robot.manage.common.check.AgentCodeCheck;
import com.chinatelecom.gs.engine.robot.manage.common.enums.ASRTaskTypeEnum;
import com.chinatelecom.gs.engine.robot.manage.common.utils.AudioFileUtil;
import com.chinatelecom.gs.engine.robot.manage.controller.web.soket.client.config.TTSConfig;
import com.chinatelecom.gs.engine.robot.manage.data.domain.request.ASRMessageRequest;
import com.chinatelecom.gs.engine.robot.manage.data.domain.request.TTSMessageRequest;
import com.chinatelecom.gs.engine.robot.manage.data.domain.response.ASRMessageResponse;
import com.chinatelecom.gs.engine.robot.manage.data.domain.response.TTSMessageResponse;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.manage.info.utils.IdGenerator;
import com.chinatelecom.gs.engine.robot.manage.service.impl.ASRHook;
import com.chinatelecom.gs.engine.robot.manage.service.impl.TtsHook;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.plugin.hub.infra.util.HttpParamUtils;
import com.telecom.ais.telephone.sdk.manager.rpc.request.ConfigCheckDTO;
import com.telecom.ais.telephone.sdk.manager.rpc.response.VoiceConfigResponse;
import com.telecom.ais.telephone.sdk.manager.rpc.service.TelephoneVoiceClient;
import com.telecom.ais.telephone.sdk.voice.asr.emuns.AsrSource;
import com.telecom.ais.telephone.sdk.voice.asr.property.StellumenLmFyAsrProperty;
import com.telecom.ais.telephone.sdk.voice.asr.property.StellumenOfflineLmAsrProperty;
import com.telecom.ais.telephone.sdk.voice.asr.request.StellumenOfflineLmAsrOption;
import com.telecom.ais.telephone.sdk.voice.manager.ClientManager;
import com.telecom.ais.telephone.sdk.voice.tts.emuns.TtsSource;
import com.telecom.ais.telephone.sdk.voice.utils.VoiceUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 语音测试控制器
 *
 * <AUTHOR>
 * @since 2024-07-04 08:55:04
 */
@Slf4j
@Tag(name = "机器人语音测试")
@RefreshScope
@PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
@RestController
@RequestMapping({Constants.ROBOT_PREFIX + Constants.WEB_PREFIX + "/voice/test", Constants.ROBOT_PREFIX + Constants.API_PREFIX + "/voice/test"})
public class AgentVoiceTestController {

    private static final int FRAME_SIZE = 1600; // 每帧大小

    @Autowired
    private ClientManager clientManager;

    @Resource
    private AgentBasicConfigService agentBasicConfigService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private TelephoneVoiceClient telephoneVoiceClient;

    @Autowired
    private CloudStorageDao cloudStorageDao;

    @Value("${platform.gateway.api-six:}")
    private String urlPrefix;

    @Operation(summary = "音频文件转文字", method = "POST")
    @PlatformRestApi(name = "音频文件转文字", groupName = "机器人语音测试")
    @PostMapping("/asr")
    @AuditLog(businessType = "机器人语音测试", operType = "音频文件转文字", operDesc = "音频文件转文字", objId = "#asrMessageRequest.agentCode")
    public Result<ASRMessageResponse> queryAgentDialogConf(@Validated @RequestBody ASRMessageRequest asrMessageRequest) {
        RequestInfo requestInfo = RequestContext.get();
        agentBasicConfigService.checkAgentAuth(asrMessageRequest.getAgentCode(), true);
        String taskId = IdGenerator.getPrefixId("ASR:TASK:");
        ASRMessageResponse asrMessageResponse = new ASRMessageResponse();
        asrMessageResponse.setTaskId(taskId);
        asrMessageResponse.setTaskStatus(ASRTaskTypeEnum.RUNNING);
        stringRedisTemplate.opsForValue().set(taskId, JSON.toJSONString(asrMessageResponse), 30, TimeUnit.MINUTES);
        // 异步执行
        CompletableFuture.runAsync(() -> {
            try {
                RequestContext.set(requestInfo);
                if (AsrSource.STELLUMEN_OFFLINE_LMFY_ASR.equals(AsrSource.valueOf(asrMessageRequest.getModelCode()))) {
                    xhAsrProcess(asrMessageRequest, taskId);
                    return;
                }
                Result<ASRMessageResponse> asrMessageResponseResult = asrProcess(asrMessageRequest, taskId);
                if (Boolean.FALSE.equals(asrMessageResponseResult.isSuccess())) {
                    log.error("音频文件转文字失败{}", asrMessageResponseResult.getMessage());
                    ASRMessageResponse errorResponse = new ASRMessageResponse();
                    errorResponse.setTaskStatus(ASRTaskTypeEnum.FAIL);
                    errorResponse.setTaskMessage(asrMessageResponseResult.getMessage());
                    errorResponse.setTaskId(taskId);
                    stringRedisTemplate.opsForValue().set(taskId, JSON.toJSONString(errorResponse));
                }
            } catch (InterruptedException e) {
                log.error("音频文件转文字失败", e);
                Thread.currentThread().interrupt();
            } catch (Exception e) {
                log.error("音频文件转文字失败", e);
                ASRMessageResponse errorResponse = new ASRMessageResponse();
                errorResponse.setTaskStatus(ASRTaskTypeEnum.FAIL);
                errorResponse.setTaskMessage(e.getMessage());
                errorResponse.setTaskId(taskId);
                stringRedisTemplate.opsForValue().set(taskId, JSON.toJSONString(errorResponse));
            } finally {
                RequestContext.remove();
            }
        });
        return Result.success(asrMessageResponse);
    }

    /**
     * xh asr
     *
     * @param asrMessageRequest ASRMessageRequest
     * @param taskId            String
     * @throws IOException
     */
    private void xhAsrProcess(ASRMessageRequest asrMessageRequest, String taskId) throws IOException {
        ConfigCheckDTO configCheckDTO = new ConfigCheckDTO();
        configCheckDTO.setConfigCode(asrMessageRequest.getModelCode());
        configCheckDTO.setConfigType("ASR");
        configCheckDTO.setConfigInfo("{}");
        com.telecom.ais.telephone.sdk.common.domain.vo.Result<VoiceConfigResponse> originConfigField = this.telephoneVoiceClient.getOriginConfigField(configCheckDTO);
        JSONObject data = JSONUtil.parseObj(originConfigField.getData());
        StellumenLmFyAsrProperty asrXhProperty = data.toBean(StellumenLmFyAsrProperty.class, true);
        StellumenOfflineLmAsrProperty asrProperty = new StellumenOfflineLmAsrProperty();
        asrProperty.setSource(AsrSource.STELLUMEN_OFFLINE_LMFY_ASR);
        asrProperty.setUrl(asrXhProperty.getUrl());
        asrProperty.setAppId(asrXhProperty.getAppId());
        asrProperty.setAppSecret(asrXhProperty.getAppSecret());

        StellumenOfflineLmAsrOption option = new StellumenOfflineLmAsrOption();
        option.setReq_id(taskId);
        byte[] audioStream = Base64.getDecoder().decode(asrMessageRequest.getVoiceStream());
        String filePath = AudioFileUtil.saveWavFile(audioStream);
        String convertFile = UUID.randomUUID() + ".pcm";
        String covertFilePath = Paths.get(AudioFileUtil.STORAGE_DIR).resolve(convertFile).toString();
        AudioFileUtil.convertWithTimeout(filePath, covertFilePath, 16000L);
        String cephKey = "upload/asr/" + convertFile;
        cloudStorageDao.uploadFromLocalFile(covertFilePath, cephKey);
        String downloadUrl = HttpParamUtils.getDownloadUrl("upload/asr/" + convertFile);
        option.setFile_link(downloadUrl);
        option.setSample_rate(16000);
        option.setCallback_url(urlPrefix + "/ais/bot/asr/callback/taskInfo");
        asrProperty.setParam(option);
        log.info("xh asr request{}", asrProperty);
        clientManager.asyncOfflineRequest(asrProperty);
    }

    /**
     * 音频文件转文字（在线）
     *
     * @param asrMessageRequest ASRMessageRequest
     * @param taskId            String
     * @return Result<ASRMessageResponse>
     * @throws InterruptedException
     */
    private Result<ASRMessageResponse> asrProcess(ASRMessageRequest asrMessageRequest, String taskId) throws InterruptedException {
        ASRHook hook = new ASRHook(taskId, stringRedisTemplate);
        CompletableFuture<ASRMessageResponse> future = new CompletableFuture<>();
        hook.setOnFinish(response -> future.complete(hook.getAsrMessageResponse()));
        clientManager.instanceAsrByRemoteProperty(AsrSource.valueOf(asrMessageRequest.getModelCode()),
                "{}", hook);
        log.info("asr request {}", asrMessageRequest);
        byte[] audioStream = Base64.getDecoder().decode(asrMessageRequest.getVoiceStream());
        hook.open();
        // 2. 存储为 WAV 文件
        try {
            String filePath = AudioFileUtil.saveWavFile(audioStream);
            String pcmFilePath = Paths.get(AudioFileUtil.STORAGE_DIR).resolve(UUID.randomUUID() + ".pcm").toString();
            boolean convertResult = AudioFileUtil.convertWithTimeout(filePath, pcmFilePath, 8000L);
            if (!convertResult) {
                // 删除文件
                log.error("Error converting audio stream to WAV file for sessionId: {}", taskId);
                FileUtil.del(filePath);
                FileUtil.del(pcmFilePath);
                return Result.failed("Error converting audio stream to WAV file");
            }
            log.info("音频文件转wav成功");
            audioStream = FileUtil.readBytes(pcmFilePath);
            FileUtil.del(pcmFilePath);
            FileUtil.del(filePath);
        } catch (Exception e) {
            log.error("Error saving audio stream to WAV file for sessionId: {}", asrMessageRequest.getSessionId(), e);
            return Result.failed("Error saving audio stream to WAV file");
        }
        log.info("读取pcm文件成功");
        audioStream = VoiceUtil.changeSampleRate(audioStream, 8000, 8000);
        List<byte[]> chunks = new ArrayList<>();
        byte[] remainder = null;
        remainder = VoiceUtil.splitPcm(audioStream, 100, 8000, chunks, remainder);
        chunks.add(remainder);
        log.info("音频流切分完成，等待websocket连接{}", chunks.size());
        for (byte[] chunk : chunks) {
            if (chunk == null || chunk.length == 0) {
                continue;
            }

            if (chunk.length >= FRAME_SIZE) {
                // 超过或等于一帧大小，直接发送
                hook.sendAudioStream(chunk);
                Thread.sleep(100);
            } else {
                byte[] paddedFrame = new byte[FRAME_SIZE];
                System.arraycopy(chunk, 0, paddedFrame, 0, chunk.length);
                // 剩余位置默认为 0，表示静音
                hook.sendAudioStream(paddedFrame);
                Thread.sleep(100);
            }

        }
        hook.hookVoiceEnd();
        log.info("websocket连接成功-语音流发送完成，等待asr处理完成");
        try {
            // 设置超时时间，避免无限等待
            ASRMessageResponse response = future.get(10, TimeUnit.MINUTES);
            response.setTaskId(taskId);
            response.setTaskStatus(ASRTaskTypeEnum.SUCCESS);
            stringRedisTemplate.opsForValue().set(taskId, JSON.toJSONString(response));
            return Result.success(response);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return Result.failed(e.getMessage());
        } catch (Exception e) {
            log.error("asr processing error for sessionId: {}", asrMessageRequest.getSessionId(), e);
            return Result.failed("asr processing timed out");
        }
    }

    @Operation(summary = "音频文件转文字查询结果", method = "GET")
    @PlatformRestApi(name = "音频文件转文字查询结果", groupName = "机器人语音测试")
    @GetMapping("/{taskId}/getAsrResult")
    @AuditLog(businessType = "机器人语音测试", operType = "音频文件转文字查询结果", operDesc = "音频文件转文字查询结果", objId = "#taskId")
    public Result<ASRMessageResponse> queryAgentDialogConf(@PathVariable("taskId") String taskId) {
        String taskResult = stringRedisTemplate.opsForValue().get(taskId);
        if (StringUtils.isBlank(taskResult)) {
            return Result.failed("任务不存在");
        }
        ASRMessageResponse asrMessageResponse = JSON.parseObject(taskResult, ASRMessageResponse.class);
        return Result.success(asrMessageResponse);
    }

    @AgentCodeCheck
    @Operation(summary = "文字转音频", method = "POST")
    @PlatformRestApi(name = "文字转音频", groupName = "机器人语音测试")
    @PostMapping("/tts")
    @AuditLog(businessType = "机器人语音测试", operType = "文字转音频", operDesc = "文字转音频", objId = "#ttsMessageRequest.agentCode")
    public Result<TTSMessageResponse> saveDialogConf(
            @Validated @RequestBody TTSMessageRequest ttsMessageRequest) {
        agentBasicConfigService.checkAgentAuth(ttsMessageRequest.getAgentCode(), true);
        TtsHook hook = new TtsHook(ttsMessageRequest.getSessionId());
        TTSConfig ttsConfig = TTSConfig.builder()
                .voice(ttsMessageRequest.getInformantId())
                .volume(ttsMessageRequest.getVolume())
                .speech_rate(Float.valueOf(ttsMessageRequest.getSpeed()))
                .pitch(Float.valueOf(ttsMessageRequest.getIntonation()))
                .build();
        CompletableFuture<TTSMessageResponse> future = new CompletableFuture<>();
        hook.setOnFinish(response -> future.complete(hook.getTtsMessageResponse()));
        clientManager.instanceTtsByRemoteProperty(TtsSource.valueOf(ttsMessageRequest.getModelCode()),
                JSONUtil.toJsonStr(ttsConfig), hook);
        hook.open();
        log.info("tts request {}", ttsMessageRequest);
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        hook.sendText(ttsMessageRequest.getText());
        try {
            // 设置超时时间，避免无限等待
            TTSMessageResponse response = future.get(60, TimeUnit.SECONDS);
            return Result.success(response);
        } catch (TimeoutException e) {
            log.error("TTS processing timed out for sessionId: {}", ttsMessageRequest.getSessionId(), e);
            return Result.failed("TTS processing timed out");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Thread was interrupted during tts processing for sessionId: {}", ttsMessageRequest.getSessionId(), e);
            return Result.failed("Thread interrupted");
        } catch (ExecutionException e) {
            log.error("Error during TTS processing for sessionId: {}", ttsMessageRequest.getSessionId(), e);
            return Result.failed("Internal server error");
        }
    }
}

