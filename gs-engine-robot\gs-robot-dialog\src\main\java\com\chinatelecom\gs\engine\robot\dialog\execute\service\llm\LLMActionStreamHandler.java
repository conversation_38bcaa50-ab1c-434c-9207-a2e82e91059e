package com.chinatelecom.gs.engine.robot.dialog.execute.service.llm;

import com.chinatelecom.gs.engine.core.model.toolkit.core.dto.Response;
import com.chinatelecom.gs.engine.core.model.toolkit.handler.CommonStreamMessageHandler;
import com.chinatelecom.gs.engine.core.model.toolkit.safe.SafeCheckAssistService;
import com.chinatelecom.gs.engine.core.sdk.vo.llm.WrapLLMMessage;
import com.chinatelecom.gs.engine.robot.dialog.execute.dto.strategy.AgentStrategyConfig;
import com.chinatelecom.gs.engine.robot.dialog.execute.service.utils.secure.AgentSecureCheck;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.*;
import com.google.common.collect.Lists;
import joptsimple.internal.Strings;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import retrofit2.Call;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class LLMActionStreamHandler extends CommonStreamMessageHandler<IActionCallBackService<ActionMessage>> {

    private AgentSecureCheck agentSecureCheck;

    private SafeCheckAssistService safeCheckAssistService;

    private AgentStrategyConfig actionRequest;

    private ActionTypeEnum actionType;

    private static final String ACTION_CODE_FORMAT = "%s_%s";

    private LocalDateTime startTime;

    private LocalDateTime endTime;
    /**
     * 0 正常
     * 1 用户中断
     * 2 敏感词中断
     */
    @Getter
    private Integer interrupt = 0;

    public LLMActionStreamHandler(CountDownLatch latch, String messageId, IActionCallBackService<ActionMessage> callback,
                                  AgentSecureCheck agentSecureCheck, SafeCheckAssistService safeCheckAssistService,
                                  AgentStrategyConfig actionRequest, ActionTypeEnum actionType) {
        super(latch, messageId, callback);
        this.agentSecureCheck = agentSecureCheck;
        this.safeCheckAssistService = safeCheckAssistService;
        this.actionRequest = actionRequest;
        this.actionType = actionType;
    }

    public void start(String content) {
        startTime = LocalDateTime.now();
        IActionCallBackService<ActionMessage> calllback = getCallback();
        if (Objects.nonNull(calllback)) {
            calllback.onStart(buildStartActions());
        }
    }

    @Override
    public void onNext(Call<ResponseBody> call, WrapLLMMessage token) {
        //todo 暂时无需处理
    }

    @Override
    public void onComplete(Response<WrapLLMMessage> response) {
        this.setResult(new AtomicReference<>(response.content()));
        log.info("最终结果 {}", this.getResult());
        endTime = LocalDateTime.now();
        IActionCallBackService<ActionMessage> calllback = getCallback();
        if (Objects.nonNull(calllback)) {
            calllback.onComplete(buildCompleteActions());
        }
        resumeEngine();
    }

    @Override
    public void onError(Throwable error, Response<WrapLLMMessage> response) {
        if (Objects.nonNull(response)) {
            this.setResult(new AtomicReference<>(response.content()));
        }
        if (interrupt == 0) {
            log.error("调用大模型失败 {}", response, error);
        } else {
            log.warn("中断，调用大模型失败 {}", response, error);
        }
        endTime = LocalDateTime.now();
        IActionCallBackService<ActionMessage> calllback = getCallback();
        if (Objects.nonNull(calllback)) {
            calllback.onError(buildErrorActions());
        }
        resumeEngine();
    }

    private ActionMessage buildStartActions() {
        Action action = Action.builder()
                .type(actionType.getCode())
                .code(getActionCode())
                .name(actionType.getDesc())
                .state(ActionStateEnum.START.getCode())
                .build();
        return ActionMessage.builder().actions(Lists.newArrayList(action)).messageId(getMessageId()).build();
    }

    private ActionMessage buildCompleteActions() {
        Action action = Action.builder()
                .type(actionType.getCode())
                .code(getActionCode())
                .name(actionType.getDesc())
                .state(ActionStateEnum.FINISH.getCode())
                .cost(getCost())
                .build();
        return ActionMessage.builder().actions(Lists.newArrayList(action)).messageId(getMessageId()).build();

    }

    private ActionMessage buildErrorActions() {
        Action action = Action.builder()
                .type(actionType.getCode())
                .code(getActionCode())
                .name(actionType.getDesc())
                .state(ActionStateEnum.ERROR.getCode())
                .cost(getCost())
                .build();
        return ActionMessage.builder().actions(Lists.newArrayList(action)).messageId(getMessageId()).build();
    }

    private String getActionCode() {
        return String.format(ACTION_CODE_FORMAT, actionRequest.getHeader().getTrack().getDownMessageId(), actionType.getCode());
    }

    private String getCost() {
        if (startTime == null || endTime == null) {
            return Strings.EMPTY;
        }
        return String.format("%.3f", Duration.between(startTime, endTime).toNanos() / 1e9);
    }
}
