package com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.engine.request;

import com.chinatelecom.gs.engine.core.sdk.vo.msg.MessageCallback;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.agent.engine.config.DialogEngineConfig;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.common.CommonRequest;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.ToolMessageResponse;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.Action;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.ActionMessage;
import com.chinatelecom.gs.engine.robot.sdk.v2.vo.message.answer.action.IActionCallBackService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * NLU请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DialogEngineRequest extends CommonRequest implements Serializable {

    /**
     * 智能体
     */
    private String agentCode;

    /**
     * 对话类型
     */
    private String chatType;

    /**
     * 处理的content
     */
    private String processContent;

    /**
     * 原始请求
     */
    private String originQuery;

    /**
     * 当前智能体使用到的对话引擎配置
     */
    private DialogEngineConfig dialogEngineConfig;

    private MessageCallback<ToolMessageResponse> messageCallback;

    private IActionCallBackService<ActionMessage> actionCallBack;

    private Boolean isSystemAgent = false;

}
