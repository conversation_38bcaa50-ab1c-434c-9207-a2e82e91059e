package com.chinatelecom.gs.engine.robot.manage.controller.web.manage;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.chinatelecom.cloud.platform.client.annotation.AuditLog;
import com.chinatelecom.gs.engine.common.auth.KsMenuConfig;
import com.chinatelecom.gs.engine.common.auth.MenuConfig;
import com.chinatelecom.gs.engine.common.auth.PermissionTag;
import com.chinatelecom.gs.engine.common.context.Constants;
import com.chinatelecom.gs.engine.common.platform.PlatformRestApi;
import com.chinatelecom.gs.engine.common.utils.DataResourceAccess;
import com.chinatelecom.gs.engine.robot.manage.info.domain.request.*;
import com.chinatelecom.gs.engine.robot.manage.info.domain.response.DatabaseResponse;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentBasicConfigService;
import com.chinatelecom.gs.engine.robot.manage.info.service.AgentWorkflowConfigService;
import com.chinatelecom.gs.engine.robot.sdk.dto.Result;
import com.chinatelecom.gs.engine.sdk.common.annotation.ApiType;
import com.chinatelecom.gs.engine.sdk.common.annotation.HideFromApiTypes;
import com.chinatelecom.gs.workflow.core.domain.param.BotWorkflowDetailRsp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Slf4j
@Tag(name = "机器人绑定业务流管理", description = "管理机器人与业务流程的绑定关系")
@RestController
@RequestMapping({Constants.ROBOT_PREFIX + Constants.WEB_PREFIX, Constants.ROBOT_PREFIX + Constants.API_PREFIX})
public class AgentWorkflowController {
    private static final String AGENT_TYPE = "agentType";

    @Autowired
    private DataResourceAccess dataResourceAccess;

    @Autowired
    private AgentWorkflowConfigService agentWorkflowConfigService;

    @Resource
    private AgentBasicConfigService agentBasicConfigService;

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "编辑绑定策略模板", description = "编辑机器人绑定的业务流程策略模板", method = "POST")
    @PlatformRestApi(name = "编辑绑定策略模板", groupName = "机器人绑定业务流管理")
    @PostMapping("/editBindWorkflow")
    @AuditLog(businessType = "机器人绑定业务流管理", operType = "编辑绑定策略模板", operDesc = "编辑绑定策略模板", objId = "#request.agentCode")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<BotWorkflowDetailRsp> editBindWorkflow(@RequestBody @Valid WorkflowBindRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(agentWorkflowConfigService.editBindWorkflow(request));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "换绑策略模板", description = "更换机器人绑定的业务流程策略模板", method = "POST")
    @PlatformRestApi(name = "换绑策略模板", groupName = "机器人绑定业务流管理")
    @PostMapping("/bindWorkflowTemplate")
    @AuditLog(businessType = "机器人绑定业务流管理", operType = "换绑策略模板", operDesc = "换绑策略模板", objId = "#request.agentCode")
    @HideFromApiTypes(ApiType.OPENAPI)
    public Result<Boolean> bindWorkflowTemplate(@RequestBody @Valid WorkflowTemplateBindRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(agentWorkflowConfigService.bindWorkflowTemplate(request));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "绑定业务流", description = "将指定的业务流程绑定到机器人", method = "POST")
    @PlatformRestApi(name = "绑定业务流", groupName = "机器人绑定业务流管理")
    @PostMapping("/bindWorkflow")
    @AuditLog(businessType = "机器人绑定业务流管理", operType = "绑定业务流", operDesc = "绑定业务流", objId = "#request.agentCode")
    public Result<Boolean> bindWorkflow(@RequestBody @Valid WorkflowBindRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(agentWorkflowConfigService.bindWorkflow(request));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "解绑业务流", description = "解除机器人与指定业务流程的绑定关系", method = "POST")
    @PlatformRestApi(name = "解绑业务流", groupName = "机器人绑定业务流管理")
    @PostMapping("/unbindWorkflow")
    @AuditLog(businessType = "机器人绑定业务流管理", operType = "解绑业务流", operDesc = "解绑业务流", objId = "#request.agentCode")
    public Result<Boolean> unbindWorkflow(@RequestBody @Valid WorkflowBindRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(agentWorkflowConfigService.unbindWorkflow(request));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "更换模板", description = "为机器人更换新的业务流程模板", method = "POST")
    @PlatformRestApi(name = "更换模板", groupName = "机器人绑定业务流管理")
    @PostMapping("/UpdateTemplate")
    @HideFromApiTypes(ApiType.OPENAPI)
    @AuditLog(businessType = "机器人绑定业务流管理", operType = "更换模板", operDesc = "更换模板", objId = "#request.agentCode")
    public Result<Boolean> updateTemplate(@RequestBody @Valid WorkflowBindRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(agentWorkflowConfigService.unbindWorkflow(request));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "获取绑定业务流", description = "分页查询机器人已绑定的业务流程列表", method = "POST")
    @PlatformRestApi(name = "获取绑定业务流", groupName = "机器人绑定业务流管理")
    @PostMapping("/queryBindWorkflow")
    @AuditLog(businessType = "机器人绑定业务流管理", operType = "获取绑定业务流", operDesc = "获取绑定业务流", objId = "#request.agentCode")
    public Result<IPage<BotWorkflowDetailRsp>> queryBindWorkflow(@RequestBody QueryWorkflowBindRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        request.setTest(true);
        return Result.success(agentWorkflowConfigService.queryBindWorkflow(request));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "绑定数据库", description = "为机器人绑定一个数据库以供业务流程使用", method = "POST")
    @PlatformRestApi(name = "绑定数据库", groupName = "机器人绑定业务流管理")
    @PostMapping("/bindDatabase")
    @AuditLog(businessType = "机器人绑定业务流管理", operType = "绑定数据库", operDesc = "绑定数据库", objId = "#request.agentCode")
    public Result<Boolean> bindDatabase(@RequestBody @Valid DatabaseBindRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(agentWorkflowConfigService.bindDatabase(request));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "删除数据库绑定", description = "解除机器人与指定数据库的绑定关系", method = "POST")
    @PlatformRestApi(name = "删除数据库绑定", groupName = "机器人绑定业务流管理")
    @PostMapping("/unbindDatabase")
    @AuditLog(businessType = "机器人绑定业务流管理", operType = "删除数据库绑定", operDesc = "删除数据库绑定", objId = "#request.agentCode")
    public Result<Boolean> unbindDatabase(@RequestBody @Valid DatabaseBindRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(agentWorkflowConfigService.unbindDatabase(request));
    }

    @PermissionTag(code = {MenuConfig.ROBOT, KsMenuConfig.BOT})
    @Operation(summary = "查询数据库绑定", description = "分页查询机器人已绑定的数据库列表", method = "POST")
    @PlatformRestApi(name = "查询数据库绑定", groupName = "机器人绑定业务流管理")
    @PostMapping("/queryBindDatabase")
    @AuditLog(businessType = "机器人绑定业务流管理", operType = "查询数据库绑定", operDesc = "查询数据库绑定", objId = "#request.agentCode")
    public Result<IPage<DatabaseResponse>> queryBindDatabase(@RequestBody @Valid QueryDatabaseBindRequest request) {
        agentBasicConfigService.checkAgentAuth(request.getAgentCode(), true);
        return Result.success(agentWorkflowConfigService.queryBindDatabase(request));
    }
}
